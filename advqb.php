<?php
class Advqb {
  public function __construct($registry) {
    $this->db = $registry->get('db');
    $this->log = $registry->get('log');
    $this->config = $registry->get('config');
    $this->session = $registry->get('session');
  }

  public function updateAccessToken() {
    if (isset($this->session->data['advqb_access_token']) && $this->session->data['advqb_access_token'] && isset($this->session->data['advqb_access_token_time']) && $this->session->data['advqb_access_token_time'] && (time() - $this->session->data['advqb_access_token_time'] <= (30*60))) {
      $result['access_token'] = $this->session->data['advqb_access_token'];

      return $result;
    } else {
      $parameters = array(
        'grant_type' => 'refresh_token',
        'refresh_token' => $this->config->get('module_opc_advqb_refresh_token'),
      );

      $headers = array(
        'Accept: application/json',
        'Authorization: Basic ' . base64_encode($this->config->get('module_opc_advqb_client_key') . ':' . $this->config->get('module_opc_advqb_client_secret')),
        'Content-Type: application/x-www-form-urlencoded'
      );

      $curl = curl_init();
      curl_setopt($curl, CURLOPT_URL,"https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer");
      curl_setopt($curl, CURLOPT_POST, 1);
      curl_setopt($curl, CURLOPT_POSTFIELDS,http_build_query($parameters));
      curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

      $result = json_decode(curl_exec ($curl), 1);

      $err = curl_error($curl);

      curl_close ($curl);

      if (isset($result['access_token']) && $result['access_token']) {
        $this->session->data['advqb_access_token'] = $result['access_token'];

        $this->session->data['advqb_access_token_time'] = time();

        $this->updateSetting('module_opc_advqb_access_token', $result['access_token']);

        $this->updateSetting('module_opc_advqb_refresh_token', $result['refresh_token']);
      }

      if (!$err) {
        return $result;
      }
    }

    return false;
  }

  public function updateSetting($key = '', $value = '') {
    $this->db->query("UPDATE " . DB_PREFIX . "setting SET value = '" . $this->db->escape($value) . "' WHERE `key` = '" . $key . "'");

    $this->config->set($key, $value);
  }

  public function execute_curl($url = '', $method = '', $data = array(), $params = '') {
    if ($url && $method) {
      $result = $this->updateAccessToken();

      if (isset($result['access_token']) && $result['access_token'] && $this->config->get('module_opc_advqb_realmid')) {
        if ($this->config->get('module_opc_advqb_sandbox')) {
          $base_url = 'https://sandbox-quickbooks.api.intuit.com';
        } else {
          $base_url = 'https://quickbooks.api.intuit.com';
        }

        $url = $base_url . '/v3/company/' . $this->config->get('module_opc_advqb_realmid') . '/'. $url . $params;

        $curl = curl_init();

        if ($data) {
          curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_POSTFIELDS =>  json_encode($data),
            CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Content-Type: application/json",
              "Authorization: Bearer ". $result['access_token'],
            ),
          ));
        } else {
          curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Content-Type: application/json",
              "Authorization: Bearer ". $result['access_token'],
            ),
          ));
        }

        $response = json_decode(curl_exec($curl), 1);

        $err = curl_error($curl);

        curl_close($curl);

        if (!$err) {
          return $response;
        } else {
          return false;
        }
      }
    }

    return false;
  }

  public function syncCustomerToAdvQB($customers = array()) {
    $count = 0;

    try {
      if ($customers) {
        foreach ($customers as $customer) {
          $this->session->data['advqbmax_customer_id'] = $customer['customer_id'];

          $data = array(
            "BillAddr" => array(
               "Line1"=>  $customer['address_1'],
               "Line2"=>  $customer['address_2'],
               "City"=>  $customer['city'],
               "Country"=>  $customer['country_name'],
               "CountrySubDivisionCode"=>  $customer['zone_name'],
               "PostalCode"=>  $customer['postcode']
           ),
           "ShipAddr" => array(
              "Line1"=>  $customer['address_1'],
              "Line2"=>  $customer['address_2'],
              "City"=>  $customer['city'],
              "Country"=>  $customer['country_name'],
              "CountrySubDivisionCode"=>  $customer['zone_name'],
              "PostalCode"=>  $customer['postcode']
          ),
           "GivenName"=>  $customer['firstname'],
           "FamilyName"=>  $customer['lastname'],
           "FullyQualifiedName"=>  $customer['firstname'] . ' ' . $customer['lastname'],
           "CompanyName"=>  $customer['company'],
           "DisplayName"=>  $customer['firstname'] . '_' . $customer['customer_id'],
           "Active" => $customer['status'] ? true : false,
           "PrimaryPhone"=>  array(
               "FreeFormNumber"=>  $customer['telephone']
           ),
           "Mobile"=>  array(
               "FreeFormNumber"=>  $customer['telephone']
           ),
           "Fax"=>  array(
               "FreeFormNumber"=>  $customer['fax']
           ),
           "PrimaryEmailAddr"=>  array(
               "Address" => $customer['email']
           )
         );

         $getSyncCustomer = $this->getSyncCustomer($customer['customer_id']);

         if (isset($getSyncCustomer['advqb_customer_id']) && $getSyncCustomer['advqb_customer_id']) {
           $data['Id'] = $getSyncCustomer['advqb_customer_id'];

           $customer_sync_token = $this->execute_curl("customer/" . $getSyncCustomer['advqb_customer_id'], "GET");

            if (isset($customer_sync_token['Customer']['SyncToken']) && $customer_sync_token['Customer']['SyncToken']) {
              $data['SyncToken'] = $customer_sync_token['Customer']['SyncToken'];
            } else {
               $data['SyncToken'] = $getSyncCustomer['advqb_sync_token'];
            }
           }

           $response = $this->execute_curl("customer", "POST", $data);

          if (isset($response['Customer']['Id']) && $response['Customer']['Id']) {
             $this->saveSyncCustomer($customer['customer_id'], $response['Customer']['Id'], $response['Customer']['SyncToken']);

            $count++;
          } else {
            if (isset($response['Fault']['Error']) && isset($response['Fault']['Error'])) {
              foreach ($response['Fault']['Error'] as $error) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_error SET type = 'customer - " . $customer['customer_id'] . "', date = '" . date('Y-m-d') . "', message = ' Error Code " . $error['code'] . ' - ' . $error['Detail'] . "'");
              }
            }
          }
        }
      }
    } catch (\Exception $e) {

    }

    return $count;
  }

  public function importCustomerFromAdvQB() {
    $count = 0;

    try {
      $limit = $this->config->get('module_opc_advqb_slot') ? $this->config->get('module_opc_advqb_slot') : 20;

      $start = 1;

      while (1) {
        $allCustomers = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("select * from Customer STARTPOSITION " . $start . " maxResults " . $limit));

        if (isset($allCustomers['QueryResponse']['Customer']) && $allCustomers['QueryResponse']['Customer']) {
          foreach ($allCustomers['QueryResponse']['Customer'] as $oneCustomer) {
            if (isset($oneCustomer['Id']) && $oneCustomer['Id'] && isset($oneCustomer['PrimaryEmailAddr']['Address']) && $oneCustomer['PrimaryEmailAddr']['Address']) {
              if (!$this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_customer WHERE advqb_customer_id = " . (int)$oneCustomer['Id'])->num_rows) {
                $customer_details = $this->db->query("SELECT customer_id FROM " . DB_PREFIX . "customer WHERE email = '" . $oneCustomer['PrimaryEmailAddr']['Address'] . "'")->row;

                if ($customer_details) {
                  $this->saveSyncCustomer($customer_details['customer_id'], $oneCustomer['Id'], $oneCustomer['SyncToken']);

                  $count++;
                } else {

                  $address = array();

                  if (isset($oneCustomer['ShipAddr']) && $oneCustomer['ShipAddr']) {
                    $address[] = array(
                      'company' => isset($oneCustomer['CompanyName']) ? $oneCustomer['CompanyName'] : '',
                      'address_1' => $oneCustomer['ShipAddr']['Line1'],
                      'address_2' => isset($oneCustomer['ShipAddr']['Line2']) ? $oneCustomer['ShipAddr']['Line2'] : '',
                      'city' => $oneCustomer['ShipAddr']['City'],
                      'postcode' => $oneCustomer['ShipAddr']['PostalCode'],
                      'country_id' => 0,
                      'zone_id' => 0,
                      'default' => 1,
                    );
                  }

                  if (isset($oneCustomer['BillAddr']) && $oneCustomer['BillAddr']) {
                    if (!isset($oneCustomer['ShipAddr']) || !$oneCustomer['ShipAddr'] || $oneCustomer['ShipAddr']['Id'] != $oneCustomer['BillAddr']['Id']) {
                      $address[] = array(
                        'company' => isset($oneCustomer['CompanyName']) ? $oneCustomer['CompanyName'] : '',
                        'address_1' => $oneCustomer['BillAddr']['Line1'],
                        'address_2' => isset($oneCustomer['BillAddr']['Line2']) ? $oneCustomer['BillAddr']['Line2'] : '',
                        'city' => $oneCustomer['BillAddr']['City'],
                        'postcode' => $oneCustomer['BillAddr']['PostalCode'],
                        'country_id' => 0,
                        'zone_id' => 0,
                        'default' => 1,
                      );
                    }
                  }

                  $data = array(
                    'firstname' => $oneCustomer['GivenName'],
                    'lastname' => $oneCustomer['FamilyName'],
                    'email' => $oneCustomer['PrimaryEmailAddr']['Address'],
                    'telephone' => $oneCustomer['PrimaryPhone']['FreeFormNumber'],
                    'fax' => isset($oneCustomer['Fax']) ? $oneCustomer['Fax'] : '',
                    'password' => $oneCustomer['GivenName'] . '' . $oneCustomer['FamilyName'],
                    'status' => 1,
                    'address' => $address,
                  );

                  $customer_id = $this->addCustomer($data);

                  $this->saveSyncCustomer($customer_id, $oneCustomer['Id'], $oneCustomer['SyncToken']);

                  $count++;
                }
              }
            }
            $start++;
          }
        } else {
          break;
        }
      }
    } catch (\Exception $e) {

    }

    return $count;
  }

  public function addCustomer($data) {
    $this->db->query("INSERT INTO " . DB_PREFIX . "customer SET customer_group_id = '1', firstname = '" . $this->db->escape($data['firstname']) . "', lastname = '" . $this->db->escape($data['lastname']) . "', email = '" . $this->db->escape($data['email']) . "', telephone = '" . $this->db->escape($data['telephone']) . "', fax = '" . $this->db->escape($data['fax']) . "', custom_field = '', newsletter = '0', salt = '" . $this->db->escape($salt = token(9)) . "', password = '" . $this->db->escape(sha1($salt . sha1($salt . sha1($data['password'])))) . "', status = '" . (int)$data['status'] . "', approved = '1', safe = '1', date_added = NOW()");

    $customer_id = $this->db->getLastId();

    if (isset($data['address']) && $data['address']) {
      foreach ($data['address'] as $address) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "address SET customer_id = '" . (int)$customer_id . "', firstname = '" . $this->db->escape($data['firstname']) . "', lastname = '" . $this->db->escape($data['lastname']) . "', company = '" . $this->db->escape($address['company']) . "', address_1 = '" . $this->db->escape($address['address_1']) . "', address_2 = '" . $this->db->escape($address['address_2']) . "', city = '" . $this->db->escape($address['city']) . "', postcode = '" . $this->db->escape($address['postcode']) . "', country_id = '" . (int)$address['country_id'] . "', zone_id = '" . (int)$address['zone_id'] . "', custom_field = ''");

        if (isset($address['default'])) {
          $address_id = $this->db->getLastId();

          $this->db->query("UPDATE " . DB_PREFIX . "customer SET address_id = '" . (int)$address_id . "' WHERE customer_id = '" . (int)$customer_id . "'");
        }
      }
    }

    return $customer_id;
  }

  public function syncProductsToAdvQB($products = array()) {
    $count = 0;

    try {
      if ($products) {
        foreach ($products as $product) {
          $this->session->data['advqbmax_product_id'] = $product['product_id'];

          $data = array(
            "Name" => $product['name'],
            "Sku" => $product['sku'] ? $product['sku'] : $product['model'],
            "Description" => strip_tags(html_entity_decode($product['description'])),
            "Active" => $product['status'] ? true : false,
            "FullyQualifiedName" => $product['name'],
            "Taxable" => $product['tax_class_id'] ? true : false,
            "UnitPrice" => $product['price'],
            "Type" => "Inventory",
            "IncomeAccountRef"=> array(
              "value"=> $this->config->get('module_opc_advqb_income'),
            ),
            "ExpenseAccountRef"=> array(
              "value"=> $this->config->get('module_opc_advqb_expense'),
            ),
            "AssetAccountRef"=> array(
              "value"=> $this->config->get('module_opc_advqb_asset'),
            ),
            "TrackQtyOnHand" => true,
            "QtyOnHand"=> $product['quantity'],
            "InvStartDate"=> date('Y-m-d', strtotime($product['date_added']))
          );

          $getSyncProduct = $this->getSyncProduct($product['product_id']);

          if ($getSyncProduct && isset($getSyncProduct['advqb_product_id']) && $getSyncProduct['advqb_product_id']) {
            $data['Id'] = $getSyncProduct['advqb_product_id'];

            $product_sync_token = $this->execute_curl("item/" . $getSyncProduct['advqb_product_id'], "GET");

             if (isset($product_sync_token['Item']['SyncToken']) && $product_sync_token['Item']['SyncToken']) {
               $data['SyncToken'] = $product_sync_token['Item']['SyncToken'];
             } else {
               $data['SyncToken'] = $getSyncProduct['advqb_sync_token'];
             }
          }

          $response = $this->execute_curl("item", "POST", $data);

          if (isset($response['Item']['Id']) && $response['Item']['Id']) {
             $this->saveSyncProduct($product['product_id'], $response['Item']['Id'], $response['Item']['SyncToken']);

            $count++;
          } else {
            if (isset($response['Fault']['Error']) && isset($response['Fault']['Error'])) {
              foreach ($response['Fault']['Error'] as $error) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_error SET type = 'product - " . $product['product_id'] . "', date = '" . date('Y-m-d') . "', message = ' Error Code " . $error['code'] . ' - ' . $error['Detail'] . "'");
              }
            }
          }
        }
      }
    } catch (\Exception $e) {

    }

    return $count;
  }

  public function importProductFromAdvQB() {
    $count = 0;

    try {
      $limit = $this->config->get('module_opc_advqb_slot') ? $this->config->get('module_opc_advqb_slot') : 20;

      $start = 1;

      while (1) {
        $allProducts = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("select * from Item WHERE Type = 'Inventory' STARTPOSITION " . $start . " maxResults " . $limit));

        if (isset($allProducts['QueryResponse']['Item']) && $allProducts['QueryResponse']['Item']) {
          foreach ($allProducts['QueryResponse']['Item'] as $oneProduct) {
            if (isset($oneProduct['Id']) && $oneProduct['Id']) {
              if (!$this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_product WHERE advqb_product_id = " . (int)$oneProduct['Id'])->num_rows) {
                $product_details = $this->db->query("SELECT product_id FROM " . DB_PREFIX . "product_description WHERE name = '" . $this->db->escape($oneProduct['Name']) . "'")->row;

                if (!$product_details) {
                  $product_details = $this->db->query("SELECT product_id FROM " . DB_PREFIX . "product WHERE model = '" . $this->db->escape($oneProduct['Name']) . "'")->row;
                }

                if ($product_details) {
                  $this->saveSyncProduct($product_details['product_id'], $oneProduct['Id'], $oneProduct['SyncToken']);

                  $count++;
                } else {
                  $data = array(
                    'sku' => $oneProduct['Name'],
                    'quantity' => $oneProduct['QtyOnHand'],
                    'price' => $oneProduct['UnitPrice'],
                    'status' => $oneProduct['Active'],
                    'name' => $oneProduct['Name'],
                    'description' => $oneProduct['FullyQualifiedName'],
                  );

                  $product_id = $this->addProduct($data);

                  $this->saveSyncProduct($product_id, $oneProduct['Id'], $oneProduct['SyncToken']);

                  $count++;
                }
              }
            }
            $start++;
          }
        } else {
          break;
        }
      }
    } catch (\Exception $e) {

    }

    return $count;
  }

  public function addProduct($data) {
    $this->db->query("INSERT INTO " . DB_PREFIX . "product SET model = '" . $this->db->escape($data['sku']) . "', sku = '" . $this->db->escape($data['sku']) . "', upc = '', ean = '', jan = '', isbn = '', mpn = '', location = '', quantity = '" . (int)$data['quantity'] . "', minimum = '1', subtract = '1', stock_status_id = '7', date_available = NOW(), manufacturer_id = '', shipping = '1', price = '" . (float)$data['price'] . "', points = '0', weight = '0', weight_class_id = '1', length = '0', width = '0', height = '0', length_class_id = '1', status = '" . (int)$data['status'] . "', tax_class_id = '0', sort_order = '0', date_added = NOW()");

    $product_id = $this->db->getLastId();


    $languages = $this->db->query("SELECT language_id FROM " . DB_PREFIX . "language")->rows;

    foreach ($languages as $key => $language) {
      $this->db->query("INSERT INTO " . DB_PREFIX . "product_description SET product_id = '" . (int)$product_id . "', language_id = '" . $language['language_id'] . "', name = '" . $this->db->escape($data['name']) . "', description = '" . $this->db->escape($data['description']) . "', tag = '', meta_title = '" . $this->db->escape($data['sku']) . "', meta_description = '', meta_keyword = ''");
    }


    $this->db->query("INSERT INTO " . DB_PREFIX . "product_to_store SET product_id = '" . (int)$product_id . "', store_id = '0'");

    return $product_id;
  }

  public function syncOrderToAdvQB($orders = array()) {
    $count = 0;

    try {
      if ($orders) {
        foreach ($orders as $order) {
          $this->session->data['advqbmax_order_id'] = $order['order_id'];

          $advqb_payment = $this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_payment WHERE oc_order_id = " . (int)$order['order_id'])->row;

          if ($advqb_payment) {
            $data = array(
              "Id" => $advqb_payment['advqb_payment_id'],
              "SyncToken" => $advqb_payment['advqb_sync_token']
            );

            $delete_payment_response = $this->execute_curl("payment", "POST", $data, "?operation=delete");

            if (isset($response['Payment']['status']) && $response['Payment']['status'] == 'Deleted') {
              $this->db->query("DELETE FROM " . DB_PREFIX . "advqb_payment WHERE oc_order_id = " . (int)$order['order_id']);
            }
          }

          $payment_method_value = 0;

          $payment_method_response = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM paymentmethod where name = '" . $order['payment_method'] . "'"));

          if (isset($payment_method_response['QueryResponse']['PaymentMethod'][0]['Id']) && $payment_method_response['QueryResponse']['PaymentMethod'][0]['Id']) {
            $payment_method_value = $payment_method_response['QueryResponse']['PaymentMethod'][0]['Id'];
          } else {
            $payment_method_response = $this->execute_curl("paymentmethod", "POST", array("Name" => $order['payment_method']));

            if (isset($payment_method_response['PaymentMethod']['Id']) && $payment_method_response['PaymentMethod']['Id']) {
              $payment_method_value = $payment_method_response['PaymentMethod']['Id'];
            }
          }

          $advqb_customer_id = 0;

          $getSyncCustomer = $this->getSyncCustomer($order['customer_id']);

          if ($getSyncCustomer && isset($getSyncCustomer['advqb_customer_id']) && $getSyncCustomer['advqb_customer_id']) {
            $advqb_customer_id = $getSyncCustomer['advqb_customer_id'];
          } else {
            $order_customer = $this->getCustomersToSync(array('customer_id' => $order['customer_id']));

            $this->syncCustomerToAdvQB($order_customer);

            $getSyncCustomer = $this->getSyncCustomer($order['customer_id']);

            if ($getSyncCustomer && isset($getSyncCustomer['advqb_customer_id']) && $getSyncCustomer['advqb_customer_id']) {
              $advqb_customer_id = $getSyncCustomer['advqb_customer_id'];
            } else {
              $customer_response = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM customer where PrimaryEmailAddr = '" . $order['email'] . "'"));

              if (isset($customer_response['QueryResponse']['Customer'][0]['Id']) && $customer_response['QueryResponse']['Customer'][0]['Id']) {
                $advqb_customer_id = $customer_response['QueryResponse']['Customer'][0]['Id'];
              } else {
                $data = array(
                  "BillAddr" => array(
                    "Line1"=>  $order['payment_address_1'],
                    "Line2"=>  $order['payment_address_2'],
                    "City"=>  $order['payment_city'],
                    "Country"=>  $order['payment_country'],
                    "CountrySubDivisionCode"=>  $order['payment_zone'],
                    "PostalCode"=>  $order['payment_postcode']
                  ),
                  "ShipAddr" => array(
                    "Line1"=>  $order['shipping_address_1'],
                    "Line2"=>  $order['shipping_address_2'],
                    "City"=>  $order['shipping_city'],
                    "Country"=>  $order['shipping_country'],
                    "CountrySubDivisionCode"=>  $order['shipping_zone'],
                    "PostalCode"=>  $order['shipping_postcode']
                  ),
                  "GivenName"=>  $order['firstname'],
                  "FamilyName"=>  $order['lastname'],
                  "FullyQualifiedName"=>  $order['firstname'] . ' ' . $order['lastname'],
                  "CompanyName"=>  $order['payment_company'],
                  "DisplayName"=>  $order['firstname'] . '-' . $order['lastname'],
                  "Active" => true,
                  "PrimaryPhone"=>  array(
                    "FreeFormNumber"=>  $order['telephone']
                  ),
                  "Mobile"=>  array(
                    "FreeFormNumber"=>  $order['telephone']
                  ),
                  "PrimaryEmailAddr"=>  array(
                    "Address" => $order['email']
                  )
                );

                $customer_response = $this->execute_curl("customer", "POST", $data);

                if (isset($customer_response['Customer']['Id']) && $customer_response['Customer']['Id']) {
                  $advqb_customer_id = $customer_response['Customer']['Id'];
                }
              }
            }
          }

         if ($advqb_customer_id) {
           $company_country = 0;

           $CompanyInfo = $this->execute_curl("companyinfo/" . $this->config->get('module_opc_advqb_realmid'), "GET");

           if (isset($CompanyInfo['CompanyInfo']['Country']) && $CompanyInfo['CompanyInfo']['Country'] == 'US') {
             $company_country = 1;
           }

           $line_array = array();

           if ($order['products']) {
             foreach ($order['products'] as $key => $order_product) {
                $advqb_product_id = 0;

               $getSyncProduct = $this->getSyncProduct($order_product['product_id']);

               if ($getSyncProduct && isset($getSyncProduct['advqb_product_id']) && $getSyncProduct['advqb_product_id']) {
                 $advqb_product_id = $getSyncProduct['advqb_product_id'];
               } else {
                 $order_product_sync = $this->getProductsToSync(array('product_id' => $order_product['product_id']));

                 $this->syncProductsToAdvQB($order_product_sync);

                 $getSyncProduct = $this->getSyncProduct($order_product['product_id']);

                 if ($getSyncProduct && isset($getSyncProduct['advqb_product_id']) && $getSyncProduct['advqb_product_id']) {
                   $advqb_product_id = $getSyncProduct['advqb_product_id'];
                 } else {
                   $product_response = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM item where Name = '" . $order_product['name'] . "'"));

                   if (isset($product_response['QueryResponse']['Item'][0]['Id']) && $product_response['QueryResponse']['Item'][0]['Id']) {
                     $advqb_product_id = $product_response['QueryResponse']['Item'][0]['Id'];
                   } else {
                     $order_product_array = array(
                       "Name" => $order_product['name'],
                       "Sku" => $order_product['model'],
                       "Active" => true,
                       "FullyQualifiedName" => $order_product['name'],
                       "Taxable" => $order_product['tax'] ? true : false,
                       "UnitPrice" => $order_product['price'],
                       "Type" => "Inventory",
                       "IncomeAccountRef"=> array(
                         "value"=> $this->config->get('module_opc_advqb_income'),
                       ),
                       "ExpenseAccountRef"=> array(
                         "value"=> $this->config->get('module_opc_advqb_expense'),
                       ),
                       "AssetAccountRef"=> array(
                         "value"=> $this->config->get('module_opc_advqb_asset'),
                       ),
                       "TrackQtyOnHand" => true,
                       "QtyOnHand"=> $order_product['quantity'],
                       "InvStartDate"=> date('Y-m-d', strtotime($order['date_added']))
                     );

                     $product_response = $this->execute_curl("item", "POST", $order_product_array);

                     if (isset($product_response['Item']['Id']) && $product_response['Item']['Id']) {
                       $advqb_product_id = $product_response['Item']['Id'];
                     }
                   }
                 }
               }

               if ($company_country) {
                 if ($order_product['tax']) {
                   $tax_code = 'TAX';
                 } else {
                   $tax_code = 'NON';
                 }
               } else {
                 $tax_code = $this->config->get('module_opc_advqb_tax');
               }

               $line_array[] = array(
                 "LineNum" => $key + 1,
                 "Description" => $order_product['model'],
                 "Amount" =>  $order_product['total'],
                 "DetailType" => "SalesItemLineDetail",
                 "SalesItemLineDetail" => array(
                   "ItemRef" => array(
                     "value" => $advqb_product_id,
                   ),
                   "UnitPrice" => $order_product['price'],
                   "Qty" => $order_product['quantity'],
                   "TaxCodeRef" => array(
                     "value" => $tax_code
                   )
                 ),
               );
             }
           }

           $discount = 0;

           $shipping = 0;

           $tax = 0;

           if ($order['order_totals']) {
              foreach ($order['order_totals'] as $key => $order_total) {
                if ($order_total['code'] == 'shipping') {
                  $shipping += $order_total['value'];
                } elseif ($order_total['code'] == 'tax') {
                  $tax += $order_total['value'];
                } elseif ($order_total['code'] == 'coupon' || $order_total['code'] == 'reward' || $order_total['code'] == 'voucher') {
                  $discount += abs($order_total['value']);
                }
              }
           }

           if ($discount) {
             $line_array[] = array(
               "Amount" =>  $discount,
               "DetailType" => "DiscountLineDetail",
               "DiscountLineDetail" => array(
                 "PercentBased" => false,
                 "DiscountAccountRef" => array(
                   "value" => $this->config->get('module_opc_advqb_discount'),
                 )
               ),
             );
           }

           if ($shipping) {
             if ($company_country) {
               $line_array[] = array(
                 "Amount" =>  $shipping,
                 "DetailType" => "SalesItemLineDetail",
                 "SalesItemLineDetail" => array(
                   "ItemRef" => array(
                     "value" => "SHIPPING_ITEM_ID",
                   ),
                 ),
               );
             } else {
               $line_array[] = array(
                 "Amount" =>  $shipping,
                 "DetailType" => "SalesItemLineDetail",
                 "SalesItemLineDetail" => array(
                   "ItemRef" => array(
                     "value" => "SHIPPING_ITEM_ID",
                   ),
                   "TaxCodeRef" => array(
                     "value" => $this->config->get('module_opc_advqb_tax')
                   ),
                 ),
               );
             }
           }

           $prefix = $this->config->get('module_opc_advqb_transaction_prefix') ? $this->config->get('module_opc_advqb_transaction_prefix') : "OC-";

           $data = array(
             "DocNumber" =>  $prefix . $order['order_id'],
             "TxnDate" => date('Y-m-d', strtotime($order['date_added'])),
             "CurrencyRef" => array(
               "value" => $order['currency_code'],
             ),
             "PrivateNote" => $order['comment'],
             "CustomField" => array(array(
               "DefinitionId" => "1",
               "Name" => "Crew #",
               "Type" => "StringType",
               "StringValue" => $order['order_id']
             )),
             "Line" => $line_array,
             "CustomerRef" => array(
               "value" => $advqb_customer_id,
             ),
             "BillAddr" => array(
               "Line1"=>  $order['payment_address_1'],
               "Line2"=>  $order['payment_address_2'],
               "City"=>  $order['payment_city'],
               "Country"=>  $order['payment_country'],
               "CountrySubDivisionCode"=>  $order['payment_zone'],
               "PostalCode"=>  $order['payment_postcode']
             ),
             "ShipAddr" => array(
               "Line1"=>  $order['shipping_address_1'],
               "Line2"=>  $order['shipping_address_2'],
               "City"=>  $order['shipping_city'],
               "Country"=>  $order['shipping_country'],
               "CountrySubDivisionCode"=>  $order['shipping_zone'],
               "PostalCode"=>  $order['shipping_postcode']
             ),
             "ShipMethodRef" => array(
               "value" => substr($order['shipping_method'], 0, 30),
               "name" => substr($order['shipping_method'], 0, 30)
             ),
             "BillEmail" => array(
               "Address" => $order['email']
             ),
             "PaymentRefNum" => $order['order_id'],
           );

           if ($payment_method_value) {
             $data['PaymentMethodRef'] = array(
               "value" => $payment_method_value,
               "name" => $order['payment_method']
             );
           }

           if ($tax) {
             $data['TxnTaxDetail'] = array(
               "TxnTaxCodeRef" => array(
                  "value" => $this->config->get('module_opc_advqb_tax'),
               ),
               "TotalTax" => $tax,
             );
           }

           $getSyncOrder = $this->getSyncOrder($order['order_id']);

           if ($getSyncOrder && isset($getSyncOrder['advqb_order_id']) && $getSyncOrder['advqb_order_id']) {
             $data['Id'] = $getSyncOrder['advqb_order_id'];

             $order_sync_token = $this->execute_curl(strtolower($this->config->get('module_opc_advqb_order_mapping')) . "/" . $getSyncOrder['advqb_order_id'], "GET");

             if (isset($order_sync_token[$this->config->get('module_opc_advqb_order_mapping')]['SyncToken']) && $order_sync_token[$this->config->get('module_opc_advqb_order_mapping')]['SyncToken']) {
               $data['SyncToken'] = $order_sync_token[$this->config->get('module_opc_advqb_order_mapping')]['SyncToken'];
             } else {
               $data['SyncToken'] = $getSyncOrder['advqb_sync_token'];
             }
           }

           $response = $this->execute_curl(strtolower($this->config->get('module_opc_advqb_order_mapping')), "POST", $data);

           if (isset($response[$this->config->get('module_opc_advqb_order_mapping')]['Id']) && $response[$this->config->get('module_opc_advqb_order_mapping')]['Id']) {
             $this->saveSyncOrder($order['order_id'], $response[$this->config->get('module_opc_advqb_order_mapping')]['Id'], $response[$this->config->get('module_opc_advqb_order_mapping')]['SyncToken']);

             $count++;

             if ($this->config->get('module_opc_advqb_paid_order_status') && is_array($this->config->get('module_opc_advqb_paid_order_status')) && in_array($order['order_status_id'], $this->config->get('module_opc_advqb_paid_order_status'))) {
               $data = array(
                 'CustomerRef' => array(
                   'value' => $advqb_customer_id,
                 ),
                 'TotalAmt' => $response['TotalAmt'],
               );

               $payment_response = $this->execute_curl("payment", "POST", $data);

               if (isset($payment_response['Payment']['Id']) && $payment_response['Payment']['Id']) {
                   $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_payment SET oc_order_id = " . (int)$order['order_id'] . ", advqb_payment_id = " . $payment_response['Payment']['Id'] . ", advqb_sync_token	 = " . $payment_response['Payment']['SyncToken']);
               }
             }
           } else {
             if (isset($response['Fault']['Error']) && isset($response['Fault']['Error'])) {
                foreach ($response['Fault']['Error'] as $error) {
                  $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_error SET type = 'order - " . $order['order_id'] . "', date = '" . date('Y-m-d') . "', message = ' Error Code " . $error['code'] . ' - ' . $error['Detail'] . "'");
                }
              }
            }
           } else {
             $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_error SET type = 'order - " . $order['order_id'] . "', date = '" . date('Y-m-d') . "', message = ' Error Code 400 - QuickBooks customer not found.'");
           }
        }
      }
    } catch (\Exception $e) {

    }

    return $count;
  }

  public function deleteOrderFromAdvQB($orders = array()) {
    $count = 0;

    try {
      if ($orders) {
        foreach ($orders as $order) {
          $advqb_order = $this->getSyncOrder($order);

          if (isset($advqb_order['advqb_order_id']) && $advqb_order['advqb_order_id']) {
            $advqb_payment = $this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_payment WHERE oc_order_id = " . (int)$order)->row;

            if ($advqb_payment) {
              $data = array(
                "Id" => $advqb_payment['advqb_payment_id'],
                "SyncToken" => $advqb_payment['advqb_sync_token']
              );

              $delete_payment_response = $this->execute_curl("payment", "POST", $data, "?operation=delete");

              if (isset($response['Payment']['status']) && $response['Payment']['status'] == 'Deleted') {
                $this->db->query("DELETE FROM " . DB_PREFIX . "advqb_payment WHERE oc_order_id = " . (int)$order);
              }
            }

            $data = array(
              "SyncToken" => $advqb_order['advqb_sync_token'],
              "Id" => $advqb_order['advqb_order_id']
            );

            $response = $this->execute_curl(strtolower($this->config->get('module_opc_advqb_order_mapping')), "POST", $data, "?operation=delete");

            if (isset($response[$this->config->get('module_opc_advqb_order_mapping')]['status']) && $response[$this->config->get('module_opc_advqb_order_mapping')]['status'] == 'Deleted') {
              $this->db->query("DELETE FROM " . DB_PREFIX . "advqb_order WHERE oc_order_id = " . (int)$order);

              $count++;
            } else {
              if (isset($response['Fault']['Error']) && isset($response['Fault']['Error'])) {
                foreach ($response['Fault']['Error'] as $error) {
                  $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_error SET type = 'order - " . $order . "', date = '" . date('Y-m-d') . "', message = ' Error Code " . $error['code'] . ' - ' . $error['Detail'] . "'");
                }
              }
            }
          }
        }
      }
    } catch (\Exception $e) {

    }

    return $count;
  }

  public function getSyncCustomer($customer_id = 0) {
    return $this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_customer WHERE oc_customer_id = " . (int)$customer_id)->row;
  }

  public function getSyncProduct($product_id = 0) {
    return $this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_product WHERE oc_product_id = " . (int)$product_id)->row;
  }

  public function getSyncOrder($order_id = 0) {
    return $this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_order WHERE oc_order_id = " . (int)$order_id)->row;
  }

  public function saveSyncCustomer($customer_id = 0, $advqb_customer_id = 0, $sync_token = 0) {
    if ($advqb_customer_id) {
      $this->db->query("DELETE FROM " . DB_PREFIX . "advqb_customer WHERE oc_customer_id = " . (int)$customer_id);

      $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_customer SET oc_customer_id = " . (int)$customer_id . ", advqb_customer_id = " . $advqb_customer_id . ", advqb_sync_token	 = " . $sync_token);
    }
  }

  public function saveSyncProduct($product_id = 0, $advqb_product_id = 0, $sync_token = 0) {
    if ($advqb_product_id) {
      $this->db->query("DELETE FROM " . DB_PREFIX . "advqb_product WHERE oc_product_id = " . (int)$product_id);

      $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_product SET oc_product_id = " . (int)$product_id . ", advqb_product_id = " . $advqb_product_id . ", advqb_sync_token	 = " . $sync_token);
    }
  }

  public function saveSyncOrder($order_id = 0, $advqb_order_id = 0, $sync_token = 0) {
    if ($advqb_order_id) {
      $this->db->query("DELETE FROM " . DB_PREFIX . "advqb_order WHERE oc_order_id = " . (int)$order_id);

      $this->db->query("INSERT INTO " . DB_PREFIX . "advqb_order SET oc_order_id = " . (int)$order_id . ", advqb_order_id = " . $advqb_order_id . ", advqb_sync_token	 = " . $sync_token);
    }
  }

  public function getCustomersToSync($data = array()) {
    $sql = "SELECT c.customer_id, c.firstname, c.lastname, c.email, c.telephone, c.status, c.fax, a.company, a.address_1, a.address_2, a.city, a.postcode, co.name as country_name, z.name as zone_name FROM " . DB_PREFIX . "customer c LEFT JOIN " . DB_PREFIX . "customer_group_description cgd ON (c.customer_group_id = cgd.customer_group_id) LEFT JOIN " . DB_PREFIX . "address a ON (c.address_id = a.address_id) LEFT JOIN " . DB_PREFIX . "country co ON (a.country_id = co.country_id) LEFT JOIN " . DB_PREFIX . "zone z ON (a.zone_id = z.zone_id) WHERE cgd.language_id = '" . (int)$this->config->get('config_language_id') . "'";

    if (isset($data['customer_id']) && $data['customer_id']) {
      $sql .= " AND c.customer_id = " . (int)$data['customer_id'];
    } else {
      if (isset($this->session->data['advqbmax_customer_id']) && $this->session->data['advqbmax_customer_id']) {
        $sql .= " AND c.customer_id > " . $this->session->data['advqbmax_customer_id'];
      }

      $sql .= " AND c.customer_id NOT IN (SELECT oc_customer_id FROM " . DB_PREFIX . "advqb_customer)";
    }

    $sql .= " ORDER BY c.customer_id ASC ";

    if (isset($data['start']) || isset($data['limit'])) {
      if ($data['start'] < 0) {
        $data['start'] = 0;
      }

      if ($data['limit'] < 1) {
        $data['limit'] = 20;
      }

      $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
    }

    $query = $this->db->query($sql);

    return $query->rows;
  }

  public function getProductsToSync($data = array()) {
    $sql = "SELECT * FROM " . DB_PREFIX . "product p LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id) WHERE pd.language_id = '" . (int)$this->config->get('config_language_id') . "'";

    if (isset($data['product_id']) && $data['product_id']) {
      $sql .= " AND p.product_id = " . (int)$data['product_id'];
    } else {
      if (isset($this->session->data['advqbmax_product_id']) && $this->session->data['advqbmax_product_id']) {
        $sql .= " AND p.product_id > " . $this->session->data['advqbmax_product_id'];
      }

      $sql .= " AND p.product_id NOT IN (SELECT oc_product_id FROM " . DB_PREFIX . "advqb_product)";
    }

    $sql .= " ORDER BY p.product_id ASC ";

    if (isset($data['start']) || isset($data['limit'])) {
      if ($data['start'] < 0) {
        $data['start'] = 0;
      }

      if ($data['limit'] < 1) {
        $data['limit'] = 20;
      }

      $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
    }

    $query = $this->db->query($sql);

    return $query->rows;
  }

  public function getOrdersToSync($data = array()) {
    $sql = "SELECT o.order_id, CONCAT(o.firstname, ' ', o.lastname) AS customer, (SELECT os.name FROM " . DB_PREFIX . "order_status os WHERE os.order_status_id = o.order_status_id AND os.language_id = '" . (int)$this->config->get('config_language_id') . "') AS order_status, o.shipping_code, o.total, o.currency_code, o.currency_value, o.date_added, o.date_modified FROM `" . DB_PREFIX . "order` o";

    if (!empty($data['filter_order_status'])) {
      $implode = array();

      foreach ($data['filter_order_status'] as $order_status_id) {
        $implode[] = "o.order_status_id = '" . (int)$order_status_id . "'";
      }

      if ($implode) {
        $sql .= " WHERE (" . implode(" OR ", $implode) . ")";
      }
    } elseif (isset($data['filter_order_status_id']) && $data['filter_order_status_id'] !== '') {
      $sql .= " WHERE o.order_status_id = '" . (int)$data['filter_order_status_id'] . "'";
    } else {
      $sql .= " WHERE o.order_status_id > '0'";
    }

    if (isset($data['order_id']) && $data['order_id']) {
      $sql .= " AND o.order_id = " . (int)$data['order_id'];
    } else {
      if (isset($this->session->data['advqbmax_order_id']) && $this->session->data['advqbmax_order_id']) {
        $sql .= " AND o.order_id > " . $this->session->data['advqbmax_order_id'];
      }

      $sql .= " AND o.order_id NOT IN (SELECT oc_order_id FROM " . DB_PREFIX . "advqb_order)";
    }

    if ($this->config->get('module_opc_advqb_date')) {
      $sql .= " AND DATE(o.date_added) >= DATE('" . $this->db->escape($this->config->get('module_opc_advqb_date')) . "')";
    }

    $sql .= " ORDER BY o.order_id ASC ";

    if (isset($data['start']) || isset($data['limit'])) {
      if ($data['start'] < 0) {
        $data['start'] = 0;
      }

      if ($data['limit'] < 1) {
        $data['limit'] = 20;
      }

      $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
    }

    $query = $this->db->query($sql);

    return $query->rows;
  }

  public function getAccounts(){
    $accounts = array();

    try {
      $income = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM account WHERE AccountType = 'Income' AND AccountSubType = 'SalesOfProductIncome'"));

      if (isset($income['QueryResponse']['Account']) && $income['QueryResponse']['Account']) {
        foreach ($income['QueryResponse']['Account'] as $value) {
          $accounts['income'][] = array(
            'id' => $value['Id'],
            'name' => $value['Name'],
          );
        }
      }

      $asset = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM account WHERE AccountType = 'Other Current Asset' AND AccountSubType = 'Inventory'"));

      if (isset($asset['QueryResponse']['Account']) && $asset['QueryResponse']['Account']) {
        foreach ($asset['QueryResponse']['Account'] as $value) {
          $accounts['asset'][] = array(
            'id' => $value['Id'],
            'name' => $value['Name'],
          );
        }
      }

      $expense = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM account WHERE AccountType = 'Cost of Goods Sold' AND AccountSubType = 'SuppliesMaterialsCogs'"));

      if (isset($expense['QueryResponse']['Account']) && $expense['QueryResponse']['Account']) {
        foreach ($expense['QueryResponse']['Account'] as $value) {
          $accounts['expense'][] = array(
            'id' => $value['Id'],
            'name' => $value['Name'],
          );
        }
      }

      $discount = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM account WHERE AccountType = 'Income' AND AccountSubType = 'DiscountsRefundsGiven'"));

      if (isset($discount['QueryResponse']['Account']) && $discount['QueryResponse']['Account']) {
        foreach ($discount['QueryResponse']['Account'] as $value) {
          $accounts['discount'][] = array(
            'id' => $value['Id'],
            'name' => $value['Name'],
          );
        }
      }

      $tax = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM taxcode"));

      if (isset($tax['QueryResponse']['TaxCode']) && $tax['QueryResponse']['TaxCode']) {
        foreach ($tax['QueryResponse']['TaxCode'] as $value) {
          $accounts['tax'][] = array(
            'id' => $value['Id'],
            'name' => $value['Name'],
          );
        }
      }
    } catch (Exception $e) {
      return $accounts;
    }

    return $accounts;
  }

  public function syncAllQuantitiesFromQB() {
    $updated_count = 0;

    try {
      // Get all mapped products
      $mapped_products = $this->db->query("
        SELECT ap.oc_product_id, ap.advqb_product_id
        FROM " . DB_PREFIX . "advqb_product ap
        INNER JOIN " . DB_PREFIX . "product p ON ap.oc_product_id = p.product_id
        WHERE p.status = 1
      ")->rows;

      foreach ($mapped_products as $product) {
        $qb_product = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM Item WHERE ID = '" . $product['advqb_product_id'] . "'"));

        if (isset($qb_product['QueryResponse']['Item'][0])) {
          $item = $qb_product['QueryResponse']['Item'][0];

          if (isset($item['Type']) && $item['Type'] == 'Inventory' && isset($item['QtyOnHand'])) {
            $this->db->query("UPDATE " . DB_PREFIX . "product SET quantity = '" . (float)$item['QtyOnHand'] . "' WHERE product_id = '" . (int)$product['oc_product_id'] . "'");
            $updated_count++;
          }
        }

        // Small delay to respect API rate limits
        usleep(100000); // 0.1 second
      }

    } catch (Exception $e) {
      $this->log->write('syncAllQuantitiesFromQB error: ' . $e->getMessage());
    }

    return $updated_count;
  }

  public function updateQuantity($data) {
    try {
      if ($data) {
        foreach ($data as $value) {
          if ($value['name'] == 'SalesReceipt' || $value['name'] == 'Invoice') {
            $advqborder = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM " . $value['name'] . " WHERE ID = '" . $value['id'] . "'"));

            if (isset($advqborder['QueryResponse'][$value['name']][0]['Line']) && $advqborder['QueryResponse'][$value['name']][0]['Line']) {
              foreach ($advqborder['QueryResponse'][$value['name']][0]['Line'] as $line) {
                if (isset($line['SalesItemLineDetail']['ItemRef']['value']) && $line['SalesItemLineDetail']['ItemRef']['value']) {
                  $allProducts = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM Item WHERE ID = '" . $line['SalesItemLineDetail']['ItemRef']['value'] . "'"));

                  if (isset($allProducts['QueryResponse']['Item'][0]) && $allProducts['QueryResponse']['Item'][0]) {
                    $oneProduct = $allProducts['QueryResponse']['Item'][0];

                    if (isset($oneProduct['Id']) && $oneProduct['Id'] && isset($oneProduct['Type']) && $oneProduct['Type'] == 'Inventory') {
                      $pid = $this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_product WHERE advqb_product_id = " . (int)$oneProduct['Id'])->row;

                      if (isset($pid['oc_product_id']) && $pid['oc_product_id']) {
                        $this->db->query("UPDATE " . DB_PREFIX . "product SET quantity = '" . $oneProduct['QtyOnHand'] . "' WHERE product_id = '" . (int)$pid['oc_product_id'] . "'");
                      }
                    }
                  }
                }
              }
            }
          } elseif ($value['name'] == 'Item') {
            $allProducts = $this->execute_curl("query", "GET", array(), "?query=" . urlencode("SELECT * FROM Item WHERE ID = '" . $value['id'] . "'"));

            if (isset($allProducts['QueryResponse']['Item'][0]) && $allProducts['QueryResponse']['Item'][0]) {
              $oneProduct = $allProducts['QueryResponse']['Item'][0];

              if (isset($oneProduct['Id']) && $oneProduct['Id'] && isset($oneProduct['Type']) && $oneProduct['Type'] == 'Inventory') {
                $pid = $this->db->query("SELECT * FROM " . DB_PREFIX . "advqb_product WHERE advqb_product_id = " . (int)$oneProduct['Id'])->row;

                if (isset($pid['oc_product_id']) && $pid['oc_product_id']) {
                  $this->db->query("UPDATE " . DB_PREFIX . "product SET quantity = '" . $oneProduct['QtyOnHand'] . "' WHERE product_id = '" . (int)$pid['oc_product_id'] . "'");
                }
              }
            }
          }
        }
      }
    } catch (\Exception $e) {
    }

    return 1;
  }
}
