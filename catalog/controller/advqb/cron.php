<?php
class ControllerAdvqbCron extends Controller {
    
    public function syncQuantities() {
        // Security check - only allow from command line or with secret token
        $token = isset($this->request->get['token']) ? $this->request->get['token'] : '';
        $expected_token = $this->config->get('module_opc_advqb_cron_token');
        
        if (php_sapi_name() !== 'cli' && (!$expected_token || $token !== $expected_token)) {
            http_response_code(403);
            echo 'Unauthorized';
            return;
        }
        
        try {
            $this->log->write('Starting hourly quantity sync from QuickBooks');
            
            // Check if extension is enabled
            if (!$this->config->get('module_opc_advqb_status')) {
                $this->log->write('AdvQB extension is disabled');
                echo 'Extension disabled';
                return;
            }
            
            $this->registry->set('advqb', new Advqb($this->registry));
            
            $updated_count = $this->syncAllProductQuantities();
            
            $message = "Hourly sync completed. Updated {$updated_count} products.";
            $this->log->write($message);
            echo $message;
            
        } catch (Exception $e) {
            $error_message = 'Cron sync error: ' . $e->getMessage();
            $this->log->write($error_message);
            echo $error_message;
        }
    }
    
    private function syncAllProductQuantities() {
        $updated_count = 0;
        $batch_size = $this->config->get('module_opc_advqb_slot') ?: 20;
        
        try {
            // Get all mapped products
            $mapped_products = $this->db->query("
                SELECT ap.oc_product_id, ap.advqb_product_id 
                FROM " . DB_PREFIX . "advqb_product ap 
                INNER JOIN " . DB_PREFIX . "product p ON ap.oc_product_id = p.product_id 
                WHERE p.status = 1
            ")->rows;
            
            if (!$mapped_products) {
                $this->log->write('No mapped products found for sync');
                return 0;
            }
            
            $this->log->write('Found ' . count($mapped_products) . ' mapped products to sync');
            
            // Process in batches to avoid API rate limits
            $batches = array_chunk($mapped_products, $batch_size);
            
            foreach ($batches as $batch_index => $batch) {
                $this->log->write('Processing batch ' . ($batch_index + 1) . ' of ' . count($batches));
                
                foreach ($batch as $product) {
                    if ($this->updateSingleProductQuantity($product['advqb_product_id'], $product['oc_product_id'])) {
                        $updated_count++;
                    }
                    
                    // Small delay to respect API rate limits
                    usleep(100000); // 0.1 second delay
                }
                
                // Longer delay between batches
                if ($batch_index < count($batches) - 1) {
                    sleep(1); // 1 second delay between batches
                }
            }
            
        } catch (Exception $e) {
            $this->log->write('Error in syncAllProductQuantities: ' . $e->getMessage());
        }
        
        return $updated_count;
    }
    
    private function updateSingleProductQuantity($qb_product_id, $oc_product_id) {
        try {
            // Get current quantity from QuickBooks
            $qb_product = $this->advqb->execute_curl(
                "query", 
                "GET", 
                array(), 
                "?query=" . urlencode("SELECT * FROM Item WHERE ID = '{$qb_product_id}'")
            );
            
            if (!isset($qb_product['QueryResponse']['Item'][0])) {
                $this->log->write("QuickBooks product not found: {$qb_product_id}");
                return false;
            }
            
            $item = $qb_product['QueryResponse']['Item'][0];
            
            // Only sync inventory items
            if (!isset($item['Type']) || $item['Type'] !== 'Inventory') {
                return false;
            }
            
            if (!isset($item['QtyOnHand'])) {
                $this->log->write("No QtyOnHand for product: {$qb_product_id}");
                return false;
            }
            
            $qb_quantity = (float)$item['QtyOnHand'];
            
            // Get current OpenCart quantity
            $oc_product = $this->db->query("
                SELECT quantity 
                FROM " . DB_PREFIX . "product 
                WHERE product_id = " . (int)$oc_product_id
            )->row;
            
            if (!$oc_product) {
                $this->log->write("OpenCart product not found: {$oc_product_id}");
                return false;
            }
            
            $oc_quantity = (float)$oc_product['quantity'];
            
            // Only update if quantities are different
            if ($qb_quantity != $oc_quantity) {
                $this->db->query("
                    UPDATE " . DB_PREFIX . "product 
                    SET quantity = '" . $qb_quantity . "', 
                        date_modified = NOW() 
                    WHERE product_id = " . (int)$oc_product_id
                );
                
                $this->log->write("Updated product {$oc_product_id}: {$oc_quantity} -> {$qb_quantity}");
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            $this->log->write("Error updating product {$oc_product_id}: " . $e->getMessage());
            return false;
        }
    }
}
