<?php
class ControllerAccountTestWebhook extends Controller {
    public function index() {
        header('Content-Type: application/json');
        
        $response = array(
            'status' => 'success',
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $_SERVER['REQUEST_METHOD'],
            'headers' => getallheaders(),
            'cloudflare' => array(
                'cf_ray' => $_SERVER['HTTP_CF_RAY'] ?? null,
                'cf_country' => $_SERVER['HTTP_CF_IPCOUNTRY'] ?? null,
                'cf_visitor' => $_SERVER['HTTP_CF_VISITOR'] ?? null,
                'cf_connecting_ip' => $_SERVER['HTTP_CF_CONNECTING_IP'] ?? null
            ),
            'body' => file_get_contents('php://input')
        );
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
}
