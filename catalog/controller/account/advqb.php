<?php
class ControllerAccountAdvqb extends Controller {
	public function index() {
		if (isset($this->request->get['code']) && $this->request->get['code']) {
			$parameters = array(
				'grant_type' => 'authorization_code',
				'code' => $this->request->get['code'],
				'redirect_uri' => $this->url->link('account/advqb', '', true)
			);

			$headers = array(
				'Accept: application/json',
				'Authorization: Basic ' . base64_encode($this->config->get('module_opc_advqb_client_key') . ':' . $this->config->get('module_opc_advqb_client_secret')),
				'Content-Type: application/x-www-form-urlencoded'
			);

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL,"https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer");
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS,http_build_query($parameters));
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

			$result = json_decode(curl_exec ($ch), 1);

			curl_close ($ch);

			if (isset($this->request->get['realmId']) && $this->request->get['realmId']) {
				$this->session->data['module_opc_advqb_realmid'] = $this->request->get['realmId'];
			}

			if (isset($result['access_token']) && $result['access_token']) {
				$this->session->data['module_opc_advqb_access_token'] = $result['access_token'];
			}

			if (isset($result['refresh_token']) && $result['refresh_token']) {
				$this->session->data['module_opc_advqb_refresh_token'] = $result['refresh_token'];
			}
		}

		// JS to close popup and refresh parent page
    echo '<script type="text/javascript">
                window.opener.location.href = window.opener.location.href;
                window.close();
              </script>';
	}

	public function advqbWebHook() {
		// Force HTTPS for webhook security
		if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on') {
			http_response_code(403);
			echo 'HTTPS required';
			exit;
		}

		// Always send response headers first
		header('Content-Type: text/plain');

		try {
			// Log that webhook was called
			$this->log->write('QuickBooks Webhook called at ' . date('Y-m-d H:i:s'));

			// Get the raw payload
			$payload = file_get_contents('php://input');
			$this->log->write('Raw webhook payload: ' . $payload);

			$data = json_decode($payload, true);

			// Validate the webhook data
			if (!$data || !isset($data['eventNotifications'])) {
				$this->log->write('Invalid webhook data received');
				http_response_code(400);
				echo 'Invalid webhook data';
				exit;
			}

			$this->log->write('Valid webhook data received with ' . count($data['eventNotifications']) . ' notifications');

			// Process each event notification
			$processed_count = 0;
			foreach ($data['eventNotifications'] as $notification) {
				if (isset($notification['dataChangeEvent']['entities'])) {
					$this->log->write('Processing entities: ' . json_encode($notification['dataChangeEvent']['entities']));

					// Initialize the Advqb class
					$this->registry->set('advqb', new Advqb($this->registry));

					// Process the entities
					$result = $this->advqb->updateQuantity($notification['dataChangeEvent']['entities']);

					if ($result) {
						$processed_count++;
						$this->log->write('Successfully processed notification');
					} else {
						$this->log->write('Failed to process notification');
					}
				}
			}

			// Always send success response to QuickBooks
			http_response_code(200);
			echo "OK - Processed {$processed_count} notifications";
			$this->log->write("Webhook completed successfully - Processed {$processed_count} notifications");

		} catch (Exception $e) {
			$this->log->write('Webhook error: ' . $e->getMessage());
			http_response_code(200); // Still return 200 to prevent retries
			echo 'Error logged: ' . $e->getMessage();
		}

		// Ensure output is sent
		if (ob_get_level()) {
			ob_end_flush();
		}
		flush();
		exit;
	}
}
