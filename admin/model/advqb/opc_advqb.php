<?php
class ModelAdvqbOpcAdvqb extends Model {
	public function createTable() {
    $this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "advqb_customer (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `oc_customer_id` int(11) NOT NULL,
      `advqb_customer_id` int(11) NOT NULL,
      `advqb_sync_token` int(11) NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");

    $this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "advqb_product (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `oc_product_id` int(11) NOT NULL,
      `advqb_product_id` int(11) NOT NULL,
      `advqb_sync_token` int(11) NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");

    $this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "advqb_order (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `oc_order_id` int(11) NOT NULL,
      `advqb_order_id` int(11) NOT NULL,
      `advqb_sync_token` int(11) NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");

		$this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "advqb_payment (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `oc_order_id` int(11) NOT NULL,
      `advqb_payment_id` int(11) NOT NULL,
      `advqb_sync_token` int(11) NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");

		$this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "advqb_error (
		  `id` int(11) NOT NULL AUTO_INCREMENT,
		  `type` varchar(100) NOT NULL,
		  `date` varchar(100) NOT NULL,
		  `message` text NOT NULL,
		  PRIMARY KEY (`id`)
		) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");
	}

  public function dropTable() {
    $this->db->query("DROP TABLE IF EXISTS " . DB_PREFIX . "advqb_customer");
    $this->db->query("DROP TABLE IF EXISTS " . DB_PREFIX . "advqb_product");
    $this->db->query("DROP TABLE IF EXISTS " . DB_PREFIX . "advqb_order");
		$this->db->query("DROP TABLE IF EXISTS " . DB_PREFIX . "advqb_payment");
		$this->db->query("DROP TABLE IF EXISTS " . DB_PREFIX . "advqb_error");
	}
}
