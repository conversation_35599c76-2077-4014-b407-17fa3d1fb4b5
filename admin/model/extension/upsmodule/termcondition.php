<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleTermcondition file
 *
 * @category Termcondition_Model
 */

class ModelExtensionUpsmoduleTermcondition extends Model
{
    /**
     * ModelExtensionUpsmoduleTermcondition getListCountry
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCountry()
    {
        $sql = "
        SELECT `key`, `value`
        FROM " .DB_PREFIX . "upsmodule_setting
        WHERE `key` = 'ups_shipping_country_code'
                ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleTermcondition getListSetting
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListSetting()
    {
        $sql = "
        SELECT `key`, `value`
                FROM " .DB_PREFIX . "upsmodule_setting
                ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleTermcondition getListCheckCountry
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckCountry()
    {
        $sql = "
        SELECT `key`, `value`
        FROM " .DB_PREFIX . "upsmodule_setting
        WHERE `key` = 'ups_shipping_menu_country'
                ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleTermcondition updateTermcondition
     * UPS <<EMAIL>>
     *
     * @param int $key //The key
     *
     * @return $query
     */
    public function updateTermcondition($key)
    {
        $value = 0;
        if ($key == 'ups_shipping_show_term_condition') {
            $value = 0;
        } elseif ($key == 'ups_shipping_accept_term_condition') {
            $value = 1;
        } else {
            $value = "";
        }
        $language_id = (int)$this->config->get('config_language_id');
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '" . $value . "'
            where `key` = '" . $key . "'"
        );
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_term_condition'"
        );
        $this->cache->delete('termcondition');
    }

    /**
     * ModelExtensionUpsmoduleTermcondition checkValueCountry
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function checkValueCountry()
    {
        $sql = "
            SELECT `value`
            FROM " . DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_accept_term_condition'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }
}
