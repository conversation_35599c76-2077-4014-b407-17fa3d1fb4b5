<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleTracking file
 *
 * @category Tracking_Model
 */

class ModelExtensionUpsmoduleTracking extends Model
{
    /**
     * ModelExtensionUpsmoduleTracking getListTrackingNumberByShipmentNumber
     * @author: UPS <<EMAIL>>
     *
     * @param string $shipment_number //The shipmentNumber
     *
     * @return $query
     */
    public function getListTrackingNumberByShipmentNumber($shipment_number)
    {
        $sql = "
            SELECT *
            FROM " . DB_PREFIX . "upsmodule_shipping_tracking
            WHERE `shipment_number` = '" . $shipment_number . "'
        ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleTracking deleteTracking
     * @author: UPS <<EMAIL>>
     *
     * @param string $shipment_number //The shipmentNumber
     *
     * @return $query
     */
    public function deleteTracking($shipment_number)
    {
        $sql = "DELETE FROM " . DB_PREFIX . "upsmodule_shipping_tracking WHERE shipment_number = '" .
            $shipment_number . "' ";
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleTracking getListTrackingNumberByShipmentNumber
     * @author: UPS <<EMAIL>>
     *
     * @param string $shipment_number //The shipment_number
     * @param string $tracking_number //The tracking_number
     * @param string $package         //The package
     * @param string $order_id         //The orderid
     *
     * @return null
     */
    public function createTracking($shipment_number, $tracking_number, $package, $order_id)
    {
        $sql = "INSERT INTO " . DB_PREFIX . "upsmodule_shipping_tracking
        SET tracking_number = '" . $this->db->escape($tracking_number) . "',
            shipment_number = '" . $this->db->escape($shipment_number) . "',
            status = '1',
            package_detail = '" . $package . "',
            order_id = '" . $order_id . "'";
        $query = $this->db->query($sql);
    }
}
