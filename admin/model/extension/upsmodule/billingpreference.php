<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleBillingpreference file
 *
 * @category Billingpreference_Model
 */

class ModelExtensionUpsmoduleBillingpreference extends Model
{
    /**
     * ModelExtensionUpsmoduleBillingpreference getListCheckDeliveryRates
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckDeliveryRates()
    {
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_menu_delivery_rates'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleBillingpreference updateCheckBillingPreference
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function updateCheckBillingPreference()
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_billling_preference'"
        );
        $this->cache->delete('deliveryrates');
    }

    /**
     * ModelExtensionUpsmoduleBillingpreference updateCheckOpenOrders
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function updateCheckOpenOrders()
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_shipment_manage'"
        );
        $this->cache->delete('deliveryrates');
    }

    public function migratecheck()
    {
       return $this->db->query(
           'SELECT ocr.order_id FROM oc_order ocr LEFT OUTER JOIN oc_upsmodule_open_orders ocor ON ocr.order_id = ocor.order_id_opencart WHERE (ocor.order_id_opencart is null AND ocr.order_status_id=2) OR (ocor.order_id_opencart is null AND ocr.order_status_id=1) ORDER BY `order_id`  DESC'
        );
    }
}
