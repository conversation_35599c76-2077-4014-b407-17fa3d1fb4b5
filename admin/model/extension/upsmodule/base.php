<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleBase file
 *
 * @category Base_Model
 */

class ModelExtensionUpsmoduleBase extends Model
{
    private $_tb_setting = 'upsmodule_setting';
    private $_tb_account = 'upsmodule_account';
    private $_tb_accessorial = 'upsmodule_accessorial';
    private $_tb_package = 'upsmodule_package_default';
    private $_tb_service = 'upsmodule_shipping_services';
    private $_tb_select_from = "SELECT * FROM ";
    private $_title = 'title';
    private $_value = 'value';
    private $_email = 'email';
    private $_country = 'country';
    private $_accessorial_service = 'accessorial_service';
    private $_sortAr = 'sortAr';
    private $_current_page = 'currenPage';
    private $_page_load = '&page={page}';
    private $_order = 'order';
    private $_config_limit_admin = 'config_limit_admin';
    private $_page = '&page=';
    private $_service_name = 'service_name';
    private $_sort_date = '&sort=datesort';
    private $_sort_time = '&sort=timesort';
    private $_ap_name = 'ap_name';
    private $_shipping_postcode = 'shipping_postcode';
    private $_shipping_address_1 = 'shipping_address_1';
    private $_shipping_address_2 = 'shipping_address_2';
    private $_shipping_zone = 'shipping_zone';
    private $_state = 'state';
    private $_title_key = 'title_key';
    private $_postal_code = 'PostalCode';
    private $_country_code = "CountryCode";
    private $_date = 'Y-m-d';
    private $_api_model = 'extension/upsmodule/apiModel';

    /**
     * ModelExtensionUpsmoduleBase listConstanst
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function listConstanst()
    {
        $object = new \stdClass();
        $this->load->language("extension/upsmodule/translate");
        //Plugin Version
        $object->txt_extension_version = '3.2.4';
        //Promo Code for US
        $object->const_promo_code = 'CP4148DX3';
        //Country
        $object->list_country_code = ['AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IS', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'GB','TR','JE'];
        //list country name
        $object->list_country_name = [
            'at' => $this->language->get("text_country_austria"),
            'be' => $this->language->get("text_country_belgium"),
            'bg' => $this->language->get("text_country_bulgaria"),
            'hr' => $this->language->get("text_country_croatia"),
            'cy' => $this->language->get("text_country_cyprus"),
            'cz' => $this->language->get("text_country_czechrepublic"),
            'dk' => $this->language->get("text_country_denmark"),
            'ee' => $this->language->get("text_country_estonia"),
            'fi' => $this->language->get("text_country_finland"),
            'fr' => $this->language->get("text_country_france"),
            'de' => $this->language->get("text_country_germany"),
            'gr' => $this->language->get("text_country_greece"),
            'hu' => $this->language->get("text_country_hungary"),
            'is' => $this->language->get("text_country_iceland"),
            'ie' => $this->language->get("text_country_ireland"),
            'it' => $this->language->get("text_country_italy"),
            'lv' => $this->language->get("text_country_latvia"),
            'lt' => $this->language->get("text_country_lithuania"),
            'lu' => $this->language->get("text_country_luxembourg"),
            'mt' => $this->language->get("text_country_malta"),
            'nl' => $this->language->get("text_country_netherlands"),
            'pl' => $this->language->get("text_country_poland"),
            'pt' => $this->language->get("text_country_portugal"),
            'ro' => $this->language->get("text_country_romania"),
            'sk' => $this->language->get("text_country_slovakia"),
            'si' => $this->language->get("text_country_slovenia"),
            'es' => $this->language->get("text_country_spain"),
            'se' => $this->language->get("text_country_sweden"),
            'ch' => $this->language->get("text_country_switzerland"),
            'rs' => $this->language->get("text_country_serbia"),
            'no' => $this->language->get("text_country_norway"),
            'tr' => $this->language->get("text_country_turkey"),
            'je' => $this->language->get("text_country_Jersey"),
            'gb' => $this->language->get("text_country_united_kingdom"),

        ];
        //list phone support
        $object->list_phone_support = [
            "<li>+44 20 7880 1810</li>"
        ];

        //about page link
        $object->about_link = [
            'pl' => [
                'en' => 'https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page',
                'pl' => 'https://www.ups.com/pl/pl/services/technology-integration/ecommerce-plugins.page'
            ],
            'gb' => [
                'en' => 'https://www.ups.com/gb/en/services/technology-integration/ecommerce-plugins.page',
            ],
            'fr' => [
                'en' => 'https://www.ups.com/fr/en/services/technology-integration/ecommerce-plugins.page',
                'fr' => 'https://www.ups.com/fr/fr/services/technology-integration/ecommerce-plugins.page'
            ],
            'de' => [
                'en' => 'https://www.ups.com/de/en/services/technology-integration/ecommerce-plugins.page',
                'de' => 'https://www.ups.com/de/de/services/technology-integration/ecommerce-plugins.page'
            ],
            'es' => [
                'en' => 'https://www.ups.com/es/en/services/technology-integration/ecommerce-plugins.page',
                'es' => 'https://www.ups.com/es/es/services/technology-integration/ecommerce-plugins.page'
            ],
            'is' => [
                'en' => '',
            ],
            'it' => [
                'en' => 'https://www.ups.com/it/en/services/technology-integration/ecommerce-plugins.page',
                'it' => 'https://www.ups.com/it/it/services/technology-integration/ecommerce-plugins.page'
            ],
            'nl' => [
                'en' => 'https://www.ups.com/nl/en/services/technology-integration/ecommerce-plugins.page',
                'nl' => 'https://www.ups.com/nl/nl/services/technology-integration/ecommerce-plugins.page'
            ],
            'be' => [
                'en' => 'https://www.ups.com/be/en/services/technology-integration/ecommerce-plugins.page',
                'fr' => 'https://www.ups.com/be/fr/services/technology-integration/ecommerce-plugins.page',
                'nl' => 'https://www.ups.com/be/nl/services/technology-integration/ecommerce-plugins.page'
            ],
            'at' => [
                'en' => '',
            ],
            'bg' => [
                'en' => '',
            ],
            'hr' => [
                'en' => '',
            ],
            'cy' => [
                'en' => '',
            ],
            'cz' => [
                'en' => '',
            ],
            'dk' => [
                'en' => '',
            ],
            'ee' => [
                'en' => '',
            ],
            'fi' => [
                'en' => '',
            ],
            'gr' => [
                'en' => '',
            ],
            'hu' => [
                'en' => '',
            ],
            'ie' => [
                'en' => '',
            ],
            'lv' => [
                'en' => '',
            ],
            'lt' => [
                'en' => '',
            ],
            'lu' => [
                'en' => '',
            ],
            'mt' => [
                'en' => '',
            ],
            'pt' => [
                'en' => '',
            ],
            'ro' => [
                'en' => '',
            ],
            'sk' => [
                'en' => '',
            ],
            'si' => [
                'en' => '',
            ],
            'se' => [
                'en' => '',
            ],
            'ch' => [
                'en' => '',
            ],
            'rs' => [
                'en' => '',
            ],
            'no' => [
                'en' => '',
            ],
            'tr' => [
                'en' => '',
            ],
            'je' => [
                'en' => '',
            ]
        ];
        //dangerous good link
        $object->dangerous_good_link = [
            'pl' => [
                'en' => 'https://www.ups.com/pl/en/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
                'pl' => 'https://www.ups.com/pl/pl/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page'
            ],
            'gb' => [
                'en' => 'https://www.ups.com/gb/en/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
            ],
            'fr' => [
                'en' => 'https://www.ups.com/fr/en/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
                'fr' => 'https://www.ups.com/fr/fr/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page'
            ],
            'de' => [
                'en' => 'https://www.ups.com/de/en/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
                'de' => 'https://www.ups.com/de/de/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page'
            ],
            'es' => [
                'en' => 'https://www.ups.com/es/en/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
                'es' => 'https://www.ups.com/es/es/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page'
            ],
            'is' => [
                'en' => '',
            ],
            'it' => [
                'en' => 'https://www.ups.com/it/en/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
                'it' => 'https://www.ups.com/it/it/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page'
            ],
            'nl' => [
                'en' => 'https://www.ups.com/nl/en/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
                'nl' => 'https://www.ups.com/nl/nl/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page'
            ],
            'be' => [
                'en' => 'https://www.ups.com/be/en/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
                'fr' => 'https://www.ups.com/be/fr/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page',
                'nl' => 'https://www.ups.com/be/nl/help-center/packaging-and-supplies/special-care-shipments/global-dangerous-goods.page'
            ],
            'at' => [
                'en' => '',
            ],
            'bg' => [
                'en' => '',
            ],
            'hr' => [
                'en' => '',
            ],
            'cy' => [
                'en' => '',
            ],
            'cz' => [
                'en' => '',
            ],
            'dk' => [
                'en' => '',
            ],
            'ee' => [
                'en' => '',
            ],
            'fi' => [
                'en' => '',
            ],
            'gr' => [
                'en' => '',
            ],
            'hu' => [
                'en' => '',
            ],
            'ie' => [
                'en' => '',
            ],
            'lv' => [
                'en' => '',
            ],
            'lt' => [
                'en' => '',
            ],
            'lu' => [
                'en' => '',
            ],
            'mt' => [
                'en' => '',
            ],
            'pt' => [
                'en' => '',
            ],
            'ro' => [
                'en' => '',
            ],
            'sk' => [
                'en' => '',
            ],
            'si' => [
                'en' => '',
            ],
            'se' => [
                'en' => '',
            ],
            'ch' => [
                'en' => '',
            ],
            'rs' => [
                'en' => '',
            ],
            'no' => [
                'en' => '',
            ],
            'tr' => [
                'en' => '',
            ],
            'je' => [
                'en' => '',
            ]
        ];
        //preparing to ship link
        $object->preparing_to_ship_link = [
            'pl' => [
                'en' => 'https://www.ups.com/pl/en/services/individual-shipper/preparing-to-ship.page',
                'pl' => 'https://www.ups.com/pl/pl/services/individual-shipper/preparing-to-ship.page'
            ],
            'gb' => [
                'en' => 'https://www.ups.com/gb/en/services/individual-shipper/preparing-to-ship.page',
            ],
            'fr' => [
                'en' => 'https://www.ups.com/fr/en/services/individual-shipper/preparing-to-ship.page',
                'fr' => 'https://www.ups.com/fr/fr/services/individual-shipper/preparing-to-ship.page'
            ],
            'de' => [
                'en' => 'https://www.ups.com/de/en/services/individual-shipper/preparing-to-ship.page',
                'de' => 'https://www.ups.com/de/de/services/individual-shipper/preparing-to-ship.page'
            ],
            'es' => [
                'en' => 'https://www.ups.com/es/en/services/individual-shipper/preparing-to-ship.page',
                'es' => 'https://www.ups.com/es/es/services/individual-shipper/preparing-to-ship.page'
            ],
            'is' => [
                'en' => '',
            ],
            'it' => [
                'en' => 'https://www.ups.com/it/en/services/individual-shipper/preparing-to-ship.page',
                'it' => 'https://www.ups.com/it/it/services/individual-shipper/preparing-to-ship.page'
            ],
            'nl' => [
                'en' => 'https://www.ups.com/nl/en/services/individual-shipper/preparing-to-ship.page',
                'nl' => 'https://www.ups.com/nl/nl/services/individual-shipper/preparing-to-ship.page'
            ],
            'be' => [
                'en' => 'https://www.ups.com/be/en/services/individual-shipper/preparing-to-ship.page',
                'fr' => 'https://www.ups.com/be/fr/services/individual-shipper/preparing-to-ship.page',
                'nl' => 'https://www.ups.com/be/nl/services/individual-shipper/preparing-to-ship.page'
            ],
            'at' => [
                'en' => '',
            ],
            'bg' => [
                'en' => '',
            ],
            'hr' => [
                'en' => '',
            ],
            'cy' => [
                'en' => '',
            ],
            'cz' => [
                'en' => '',
            ],
            'dk' => [
                'en' => '',
            ],
            'ee' => [
                'en' => '',
            ],
            'fi' => [
                'en' => '',
            ],
            'gr' => [
                'en' => '',
            ],
            'hu' => [
                'en' => '',
            ],
            'ie' => [
                'en' => '',
            ],
            'lv' => [
                'en' => '',
            ],
            'lt' => [
                'en' => '',
            ],
            'lu' => [
                'en' => '',
            ],
            'mt' => [
                'en' => '',
            ],
            'pt' => [
                'en' => '',
            ],
            'ro' => [
                'en' => '',
            ],
            'sk' => [
                'en' => '',
            ],
            'si' => [
                'en' => '',
            ],
            'se' => [
                'en' => '',
            ],
            'ch' => [
                'en' => '',
            ],
            'rs' => [
                'en' => '',
            ],
            'no' => [
                'en' => '',
            ],
            'tr' => [
                'en' => '',
            ],
            'je' => [
                'en' => '',
            ]
        ];
        //pickup schedule link
        $object->pickup_link = [
            'pl' => [
                'en' => 'https://wwwapps.ups.com/pickup/schedule?loc=en_PL',
                'pl' => 'https://wwwapps.ups.com/pickup/schedule?loc=pl_PL'
            ],
            'gb' => [
                'en' => 'https://wwwapps.ups.com/pickup/schedule?loc=en_GB',
            ],
            'fr' => [
                'en' => 'https://wwwapps.ups.com/pickup/schedule?loc=en_FR',
                'fr' => 'https://wwwapps.ups.com/pickup/schedule?loc=fr_FR'
            ],
            'de' => [
                'en' => 'https://wwwapps.ups.com/pickup/schedule?loc=en_DE',
                'de' => 'https://wwwapps.ups.com/pickup/schedule?loc=de_DE'
            ],
            'es' => [
                'en' => 'https://wwwapps.ups.com/pickup/schedule?loc=en_ES',
                'es' => 'https://wwwapps.ups.com/pickup/schedule?loc=es_ES'
            ],
            'is' => [
                'en' => '',
            ],
            'it' => [
                'en' => 'https://wwwapps.ups.com/pickup/schedule?loc=en_IT',
                'it' => 'https://wwwapps.ups.com/pickup/schedule?loc=it_IT'
            ],
            'nl' => [
                'en' => 'https://wwwapps.ups.com/pickup/schedule?loc=en_NL',
                'nl' => 'https://wwwapps.ups.com/pickup/schedule?loc=nl_NL'
            ],
            'be' => [
                'en' => 'https://wwwapps.ups.com/pickup/schedule?loc=en_BE',
                'fr' => 'https://wwwapps.ups.com/pickup/schedule?loc=fr_BE',
                'nl' => 'https://wwwapps.ups.com/pickup/schedule?loc=nl_BE'
            ],
            'at' => [
                'en' => '',
            ],
            'bg' => [
                'en' => '',
            ],
            'hr' => [
                'en' => '',
            ],
            'cy' => [
                'en' => '',
            ],
            'cz' => [
                'en' => '',
            ],
            'dk' => [
                'en' => '',
            ],
            'ee' => [
                'en' => '',
            ],
            'fi' => [
                'en' => '',
            ],
            'gr' => [
                'en' => '',
            ],
            'hu' => [
                'en' => '',
            ],
            'ie' => [
                'en' => '',
            ],
            'lv' => [
                'en' => '',
            ],
            'lt' => [
                'en' => '',
            ],
            'lu' => [
                'en' => '',
            ],
            'mt' => [
                'en' => '',
            ],
            'pt' => [
                'en' => '',
            ],
            'ro' => [
                'en' => '',
            ],
            'sk' => [
                'en' => '',
            ],
            'si' => [
                'en' => '',
            ],
            'se' => [
                'en' => '',
            ],
            'ch' => [
                'en' => '',
            ],
            'rs' => [
                'en' => '',
            ],
            'no' => [
                'en' => '',
            ],
            'tr' => [
                'en' => '',
            ],
            'je' => [
                'en' => '',
            ]
        ];
        //pickup schedule link
        $object->contact_link = [
            'pl' => [
                'en' => 'https://www.ups.com/pl/en/help-center/contact.page'
            ],
            'gb' => [
                'en' => 'https://www.ups.com/gb/en/help-center/contact.page',
            ],
            'fr' => [
                'en' => 'https://www.ups.com/fr/en/help-center/contact.page',
                'fr' => 'https://www.ups.com/fr/fr/help-center/contact.page'
            ],
            'de' => [
                'en' => 'https://www.ups.com/de/en/help-center/contact.page',
                'de' => 'https://www.ups.com/de/de/help-center/contact.page'
            ],
            'es' => [
                'en' => 'https://www.ups.com/es/en/help-center/contact.page',
                'es' => 'https://www.ups.com/es/es/help-center/contact.page'
            ],
            'is' => [
                'en' => '',
            ],
            'it' => [
                'en' => 'https://www.ups.com/it/en/help-center/contact.page',
                'it' => 'https://www.ups.com/it/it/help-center/contact.page'
            ],
            'nl' => [
                'en' => 'https://www.ups.com/nl/en/help-center/contact.page',
                'nl' => 'https://www.ups.com/nl/nl/help-center/contact.page'
            ],
            'be' => [
                'en' => 'https://www.ups.com/be/en/help-center/contact.page',
                'fr' => 'https://www.ups.com/be/fr/help-center/contact.page',
                'nl' => 'https://www.ups.com/be/nl/help-center/contact.page'
            ],
            'at' => [
                'en' => '',
            ],
            'bg' => [
                'en' => '',
            ],
            'hr' => [
                'en' => '',
            ],
            'cy' => [
                'en' => '',
            ],
            'cz' => [
                'en' => '',
            ],
            'dk' => [
                'en' => '',
            ],
            'ee' => [
                'en' => '',
            ],
            'fi' => [
                'en' => '',
            ],
            'gr' => [
                'en' => '',
            ],
            'hu' => [
                'en' => '',
            ],
            'ie' => [
                'en' => '',
            ],
            'lv' => [
                'en' => '',
            ],
            'lt' => [
                'en' => '',
            ],
            'lu' => [
                'en' => '',
            ],
            'mt' => [
                'en' => '',
            ],
            'pt' => [
                'en' => '',
            ],
            'ro' => [
                'en' => '',
            ],
            'sk' => [
                'en' => '',
            ],
            'si' => [
                'en' => '',
            ],
            'se' => [
                'en' => '',
            ],
            'ch' => [
                'en' => '',
            ],
            'rs' => [
                'en' => '',
            ],
            'no' => [
                'en' => '',
            ],
            'tr' => [
                'en' => '',
            ],
            'je' => [
                'en' => '',
            ]
        ];
        //General
        $object->error = 'error';
        $object->message = 'message';
        //Token
        $object->token = 'user_token=' . $this->session->data['user_token'];
        $object->user_token = 'user_token';
        //Page about
        $object->about = 'text_About_UPS';
        $object->sort_method = "&sort=method";
        $object->sort_time_request = "&sort=time_request";
        $object->sort_time_response = "&sort=time_response";
        //Api data
        $object->UserName = 'username';
        $object->full_uri = 'full_uri';
        $object->request = 'request';
        $object->response = 'response';
        $object->License = 'License';
        $object->Registration = 'UpsReadyProvider/Registration';
        $object->RegistrationSuccess = 'Registration';
        $object->OpenAccount = 'UpsReadyProvider/OpenAccount';
        $object->PromoDiscount = 'PromoDiscount';
        $object->Ship = 'Ship';
        $object->Track = 'Track';
        $object->Void = 'Void';
        $object->LBRecovery = 'LBRecovery';
        $object->Locator = 'Locator';
        $object->Rate = 'Rate';
        $object->Handshake = 'SecurityService/Handshake';
        $object->RegisteredPluginToken = 'SecurityService/RegisteredPluginToken';
        $object->MyUpsID = 'SecurityService/MyUpsID';
        //retry api key
        $object->pm_api_remove_account = "ups_shipping_update_merchant_status_remove_account";
        $object->pm_api_add_account = "ups_shipping_transfer_merchant_info_by_user";
        $object->pm_api_merchant_info = "ups_shipping_transfer_merchant_info";
        $object->pm_api_accessorial = "ups_shipping_transfer_accessorial";
        $object->pm_api_shipping_service = "ups_shipping_transfer_shipping_service";
        $object->pm_api_delivery_rate = "ups_shipping_transfer_delivery_rate";
        $object->pm_api_default_package = "ups_shipping_transfer_default_package";
        $object->pm_api_created_shipment = "ups_shipping_transfer_shipment";
        $object->pm_api_update_shipment_status = "ups_shipping_update_shipment_status";
        $object->pm_api_activated_plugin = "ups_shipping_activated_plugin";
        $object->pm_api_deactivated_plugin = "ups_shipping_deactivated_plugin";
        //method name of api
        $object->UpdateMerchantStatus = 'Merchant/UpdateMerchantStatus';
        $object->TransferMerchantInfoByUsser = 'Merchant/TransferMerchantInfo';
        $object->VerifyMerchant = 'Merchant/VerifyMerchant';
        $object->TransferMerchantInfo = 'Merchant/TransferMerchantInfo';
        $object->TransferDeliveryRates = 'Merchant/TransferDeliveryRates';
        $object->TransferDefaultPackage = 'Merchant/TransferDefaultPackage';
        $object->TransferAccessorials = 'Merchant/TransferAccessorials';
        $object->TransferShippingServices = 'Merchant/TransferShippingServices';
        $object->UpdateShipmentStatus = 'Shipment/UpdateShipmentStatus';
        $object->TransferShipments = 'Shipment/TransferShipments';
        $object->UpgradePluginVersion = 'Shipment/UpgradePluginVersion';
        //Link
        $object->link_translate = "extension/upsmodule/translate";
        $object->link_country = "extension/upsmodule/country";
        $object->link_status = "extension/upsmodule/status";
        $object->link_about = "extension/upsmodule/about";
        $object->link_billingpreference = "extension/upsmodule/billingpreference";
        $object->link_openorders = "extension/upsmodule/openorders";
        $object->link_deliveryrates = "extension/upsmodule/deliveryrates";
        $object->link_account = 'extension/upsmodule/account';
        $object->link_termcondition =  'extension/upsmodule/termcondition';
        $object->link_opencartsetting =  'extension/upsmodule/opencartsetting';
        $object->link_accountsuccess =  'extension/upsmodule/accountsuccess';
        $object->link_api_model =  'extension/upsmodule/apiModel';
        $object->link_cashondelivery = 'extension/upsmodule/cashondelivery';
        $object->link_accessorial = 'extension/upsmodule/accessorial';
        $object->link_archivedorders = 'extension/upsmodule/archivedorders';
        $object->link_packagedimension = 'extension/upsmodule/packagedimension';
        $object->link_shipments = 'extension/upsmodule/shipments';
        $object->link_api_ups = '../system/library/upsmodule/API/Ups.php';
        $object->link_api_manager = '../system/library/upsmodule/API/Manage.php';
        $object->link_search_address_point = "https://www.ups.com/dropoff";
        $object->link_opc_error = 'error/not_found';
        //billing_preference
        $object->text_billing_preference = "text_BillingPreference";
        $object->access_license_text = "AccessLicenseText";
        $object->title = $this->_title;
        $object->description = "description";
        $object->statusid = "statusid";
        //country
        $object->text_country = "text_country";
        //termcondition
        $object->text_termcondition = "text_termcondition";
        $object->value = $this->_value;
        $object->license = "license";
        //Account
        $object->text_account = "text_account";
        $object->text_error = "text_error";
        $object->country_code = "country_code";
        $object->country_name = "country_name";
        $object->iso_code_2 = "iso_code_2";
        $object->postal_code = $this->_postal_code;
        $object->optradio = "optradio";
        $object->keep_num_char = '/[^a-zA-Z0-9]/s';
        $object->post_code = 'post_code';
        $object->name = 'name';
        $object->has_account = 'has_account';
        $object->fullname = 'fullname';
        $object->phone_number = 'PhoneNumber';
        $object->phone = 'phone_number';
        $object->title_account = $this->_title;
        $object->company = 'company';
        $object->customer_name = 'customer_name';
        $object->email = $this->_email;
        $object->address_type = 'address_type';
        $object->ups_account_name = 'ups_account_name';
        $object->account_name = 'AccountName';
        $object->account_name1 = 'AccountName1';
        $object->success = 'success';
        $object->ups_account_number = 'ups_account_number';
        $object->address_line_1 = 'AddressLine1';
        $object->address_line_2 = 'AddressLine2';
        $object->address_line_3 = 'AddressLine3';
        $object->validate_null = 'validateNull';
        $object->account_city = 'AccountCity';
        $object->state_provice_code = 'state_provice_code';
        $object->optradio = 'optradio';
        $object->account_number_2 = 'AccountNumber2';
        $object->account_number_1 = 'AccountNumber1';
        $object->address_1 = 'address_1';
        $object->address_2 = 'address_2';
        $object->address_3 = 'address_3';
        $object->city = 'city';
        $object->country_account = $this->_country;
        $object->account_type = 'account_type';
        $object->account_default = 'account_default';
        $object->ups_invoice_number = 'ups_invoice_number';
        $object->ups_invoice_amount = 'ups_invoice_amount';
        $object->ups_currency = 'ups_currency';
        $object->ups_invoice_date = 'ups_invoice_date';
        $object->country_code_option3 = 'CountryCode';
        $object->customer_name_option3 = 'CustomerName';
        $object->company_name_option3 = 'CompanyName';
        $object->account_city_option3 = 'AccountCity';
        $object->postal_code_option3 = $this->_postal_code;
        $object->phone_number_option3 = 'PhoneNumber';
        $object->address_email_option3 = 'AddressEmail';
        $object->value_data_account = $this->_value;
        $object->account_vat_number = 'AccountVATNumber';
        $object->special_phone_acc = '/[^0-9]/s';
        $object->title_acc = 'Title';
        $object->languageCode_acc = 'LanguageCode';
        $object->company_name_acc = "CompanyName";
        $object->country_code_acc = $this->_country_code;
        $object->province_code_acc = "StateProvinceCode";
        $object->postal_code_acc = "PostalCode";
        $object->account_number_acc = 'AccountNumber';
        $object->ups_account_promocode = 'ups_account_promocode';
        //cod
        $object->txt_cashondelivery = "text_title_cashondelivery";
        //accessorial
        $object->txt_accessorial = 'text_accessorial';
        //archived_orders
        $object->text_archived_orders = 'text_archived_orders';
        $object->accessorial_service = $this->_accessorial_service;
        $object->list_id_orders = 'list_id_orders';
        $object->sort_ar = $this->_sortAr;
        $object->curren_page = $this->_current_page;
        $object->sort_page = $this->_page_load;
        $object->sort_order = $this->_order;
        $object->ymd = $this->_date;
        $object->config_limit_admin = $this->_config_limit_admin;
        $object->order_page = $this->_page;
        $object->sort_id = '&sort=id';
        $object->user_token_ = 'user_token=';
        $object->br_dropdown = '<br/>';
        $object->list_product = 'listProduct';
        $object->service_symbol = 'service_symbol';
        $object->service_name_info = 'service_name_info';
        $object->service_name = $this->_service_name;
        $object->list_sort_page = '&order=DESC&page=1';
        $object->sort_date = $this->_sort_date;
        $object->sort_time = $this->_sort_time;
        $object->sort_name = '&sort=sort_address';
        $object->sort_service_name = '&sort=sort_service';
        $object->sort_payment = '&sort=payment_code';
        $object->sort_address = 'sort_address';
        $object->sort_service = 'sort_service';
        $object->ap_address_1 = 'ap_address1';
        $object->ap_address_2 = 'ap_address2';
        $object->ap_address_3 = 'ap_address3';
        $object->special_char = '&amp;#xD;';
        $object->ap_name = $this->_ap_name;
        $object->date_added = 'date_added';
        $object->total = 'total';
        $object->shipping_postcode = $this->_shipping_postcode;
        $object->shipping_address_1 = $this->_shipping_address_1;
        $object->shipping_address_2 = $this->_shipping_address_2;
        $object->shipping_zone = $this->_shipping_zone;
        $object->state_code = $this->_state;
        $object->ap_state = 'ap_state';
        $object->ap_postcode = 'ap_postcode';
        $object->ap_country = 'ap_country';
        $object->state_name = 'state_name';
        //deliveryrates
        $object->text_deliveryrates = 'text_deliveryrates';
        $object->valuedeli = $this->_value;
        $object->text_default = 'text_default';
        $object->currency = 'currency';
        //open orders
        $object->text_open_orders = 'text_open_orders';
        $object->value_data = $this->_value;
        $object->ymd_orders = $this->_date;
        $object->config_admin = $this->_config_limit_admin;
        $object->curren_page_orders = $this->_current_page;
        $object->page_data = $this->_page;
        $object->list_data_orders = 'listOpenOrders';
        $object->disabled_data = 'disabled';
        $object->sort_id_orders = '&sort=id';
        $object->link_sort_page = '&order=DESC&page=1';
        $object->list_product_name = 'listProduct';
        $object->product_name = 'product_name';
        $object->drop_down = '<br/>';
        $object->order_data = $this->_order;
        $object->ups_access_point = 'UPS_ACSRL_ACCESS_POINT_COD';
        $object->ups_home = 'UPS_ACSRL_TO_HOME_COD';
        $object->total_all = 'totalAll';
        $object->check_shipment = 'checkshipment';
        $object->order_id = 'orderIds';
        $object->service_symbol_orders = 'service_symbol';
        $object->service_name_info_orders = 'service_name_info';
        $object->service_name = $this->_service_name;
        $object->service_type = 'service_type';
        $object->btn_cancel_edit = 'btn_popup_cancel_editing';
        $object->payment_code = 'payment_code';
        $object->sort_page_orders = $this->_page_load;
        $object->sort_address_orders = 'sort_address';
        $object->sort_service_name_orders = '&sort=sort_service';
        $object->sort_service_orders = 'sort_service';
        $object->sort_ar_orders = $this->_sortAr;
        $object->sort_date_orders = $this->_sort_date;
        $object->sort_time_orders = $this->_sort_time;
        $object->sort_ap_name = '&sort=sort_address';
        $object->sort_payment_code = '&sort=payment_code';
        $object->access_service = $this->_accessorial_service;
        $object->shipping_address_1_orders = $this->_shipping_address_1;
        $object->shipping_address_2_orders = $this->_shipping_address_2;
        $object->shipping_post_code = $this->_shipping_postcode;
        $object->shipping_city = 'shipping_city';
        $object->shipping_zone_orders = $this->_shipping_zone;
        $object->state_name_orders= 'state_name';
        $object->state_data = $this->_state;
        $object->shipping_country = 'shipping_country';
        $object->char_special = '#xD;';
        $object->access_point_id = 'access_point_id';
        $object->data_ap_name = $this->_ap_name;
        $object->ap_address1 = 'ap_address1';
        $object->ap_address2 = 'ap_address2';
        $object->ap_address3 = 'ap_address3';
        $object->ap_city = 'ap_city';
        $object->ap_state_orders = 'ap_state';
        $object->ap_post_code = 'ap_postcode';
        $object->date_added_orders = 'date_added';
        $object->ap_country_orders = 'ap_country';
        //packagedimension
        $object->text_pkgdimension = 'text_packagedimension';
        $object->package_id = 'package_id';
        $object->package_name = 'package_name';
        $object->name_package = 'namePackage';
        $object->utf = 'UTF-8';
        $object->text_ups_module = 'text_UPS_Shipping_Module';
        $object->button_delete = 'button_delete';
        $object->package_name_value = 'namePackage';
        //shipment
        $object->text_shipments = 'text_shipments';
        $object->ups_home_cod = "UPS_ACSRL_TO_HOME_COD";
        $object->ups_access_point_cod = "UPS_ACSRL_ACCESS_POINT_COD";
        $object->order_id_shipment = 'order_id';
        $object->order_id_opencart = 'order_id_opencart';
        $object->label_shipment = "LabelShipment";
        $object->order_selected = "order_selected";
        $object->accessorial_service_shipment = $this->_accessorial_service;
        $object->ship_from = 'ship_from';
        $object->ship_from_data = "ShipFrom";
        $object->ship_to_data = "ShipTo";
        $object->ship_rate_option = "ShipmentRatingOptions";
        $object->ship_negotiated_rate = "NegotiatedRatesIndicator";
        $object->shipment_number = 'shipment_number';
        $object->tracking_number = 'tracking_number';
        $object->currency_code = 'currency_code';
        $object->currency_code_data = "CurrencyCode";
        $object->special_remove = "&amp;amp;";
        $object->special_phone = '/[^0-9]/s';
        $object->special_posttal_code = '/[^a-zA-Z0-9]/s';
        $object->code_data = "Code";
        $object->service_type_shipment = 'service_type';
        $object->total_shipment = 'total';
        $object->shipping_country_id = 'shipping_country_id';
        $object->iso_code_2_shipment = 'iso_code_2';
        $object->order_shipment = $this->_order;
        $object->order_value = 'order_value';
        $object->shipping_fee= 'shipping_fee';
        $object->create_date = 'create_date';
        $object->name_value = 'name';
        $object->address_replace = '&#xD;';
        $object->number_format = '([^a-zA-Z0-9.])';
        $object->package_detail = 'package_detail';
        $object->phone_shipment = 'phone';
        $object->phone_data = "Phone";
        $object->name_data = "Name";
        $object->number_data = "Number";
        $object->address_data = "Address";
        $object->address_line = "AddressLine";
        $object->postal_code_shipment = $this->_postal_code;
        $object->country_code_shipment = $this->_country_code;
        $object->city_data = "City";
        $object->type_data = "Type";
        $object->state_province_code = "state_province_code";
        $object->shipper_number = "ShipperNumber";
        $object->attention_name = "AttentionName";
        $object->service_data = "Service";
        $object->description_data = "Description";
        $object->alternate_delivery_address = "AlternateDeliveryAddress";
        $object->shipment_charge = "ShipmentCharge";
        $object->payment_infor = "PaymentInformation";
        $object->bill_shipper = "BillShipper";
        $object->account_number = "AccountNumber";
        $object->monetary_value = "MonetaryValue";
        $object->shipping_address1 = $this->_shipping_address_1;
        $object->shipping_address2 = $this->_shipping_address_2;
        $object->shipping_posttal_code = $this->_shipping_postcode;
        $object->telephone = 'telephone';
        $object->ship_city = 'shipping_city';
        $object->ship_zone = $this->_shipping_zone;
        $object->shipping_zone_id = 'shipping_zone_id';
        $object->name_zone = 'namezone';
        $object->address1 = 'address1';
        $object->address2 = 'address2';
        $object->address3 = 'address3';
        $object->state_shipment = $this->_state;
        $object->config_limit = $this->_config_limit_admin;
        $object->curren_page_shipment = $this->_current_page;
        $object->sort_date_sort = $this->_sort_date;
        $object->sort_time_sort = $this->_sort_time;
        $object->sort_address1 = '&sort=address1';
        $object->page_page = $this->_page_load;
        $object->sort_order_id = '&sort=order_id';
        $object->page_shipment = $this->_page;
        $object->sort_shipping_fee = '&sort=shipping_fee';
        $object->sort_shipment_number = '&sort=shipment_number';
        $object->sort_ar_shipment = $this->_sortAr;
        $object->sort_tracking_number= '&sort=tracking_number';
        $object->post_code_shipment = 'postcode';
        $object->country_shipment = $this->_country;
        $object->access_id = "access_point_id";
        $object->accessorials_data = "accessorials";
        $object->package_data = "Package";
        $object->dimensions = "Dimensions";
        $object->unit_of_measurement = "UnitOfMeasurement";
        $object->unit_dimension = "unit_dimension";
        $object->centimeter = "centimeter";
        $object->inches = "inches";
        $object->length_data = "Length";
        $object->length = 'length';
        $object->width_data = "Width";
        $object->width = 'width';
        $object->height_data = "Height";
        $object->height = 'height';
        $object->package_weight = "PackageWeight";
        $object->unit_weight = 'unit_weight';
        $object->kilograms = "kilograms";
        $object->pounds = "pounds";
        $object->weight_data = "Weight";
        $object->weight = 'weight';
        $object->first_name = 'firstname';
        $object->last_name = 'lastname';
        $object->email_data = $this->_email;
        $object->shipper = "Shipper";
        $object->shipping_type = "ShippingType";
        $object->service_name_shipment = $this->_service_name;
        $object->ap_name_data = $this->_ap_name;
        $object->poland_api_code = 'PLN';
        $object->decimal_place = 'decimal_place';
        $object->shipping_country_shipment = 'shipping_country';
        $object->ymd_shipment = $this->_date;
        $object->check = 'check';

        return $object;
    }

    /**
     * ModelExtensionUpsmoduleBase listCurrency
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function listCurrency()
    {
        $object = new\ stdClass();
        $object->list_currency =  [
            "AED" => "Arab Emirates Dirham",
            "ARS" => "Argentina Peso",
            "AUD" => "Australian Dollar",
            "BBD" => "Barbados Dollar",
            "BHD" => "Bahrain Dinar",
            "BRL" => "Brazilian Real",
            "BYN" => "Belarus Ruble",
            "CAD" => "Canadian Dollar",
            "CHF" => "Swiss Franc",
            "CLP" => "Chilean Peso",
            "CNY" => "China Renminbi Yuan",
            "COP" => "Colombian Peso",
            "CRC" => "Costa Rican Colon",
            "CZK" => "Czech Koruna",
            "DKK" => "Danish Kroner",
            "DOP" => "Dom Rep Peso",
            "EUR" => "Euro",
            "GBP" => "Pound Sterling",
            "HKD" => "Hong Kong Dollar",
            "HUF" => "Hungarian Forint",
            "IDR" => "Indonesian Rupiah",
            "INR" => "Indian Rupee",
            "JPY" => "Japanese Yen",
            "KWD" => "Kuwait Dinar",
            "KRW" => "Korean Won",
            "KZT" => "Kazakhstan Tenge",
            "MAD" => "Morocco Dirham",
            "MOP" => "Macau Pataca",
            "MXN" => "Mexican Peso",
            "MYR" => "Malaysian Ringgit",
            "NGN" => "Nigerian Naira",
            "NOK" => "Norway Kroner",
            "NZD" => "New Zealand Dollar",
            "PAB" => "Panamanian Balboa",
            "PHP" => "Philippine Peso",
            "PLN" => "Polish Zloty",
            "RON" => "Romanian Leu",
            "RUB" => "Russia Ruble",
            "SEK" => "Swedish Kroner",
            "SGD" => "Singapore Dollar",
            "THB" => "Thailand Baht",
            "TRY" => "Turkey",
            "TWD" => "Taiwan Dollar",
            "VND" => "Vietnam dong",
            "UAH" => "Ukraine Hyrvnya",
            "USD" => "U.S. Dollar",
            "ZAR" => "South African Rand",
            "ISK" => "Icelandic Krona"
        ];
        return $object;
    }

    /**
     * ModelExtensionUpsmoduleBase listTitleAccount
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function listTitleAccount()
    {
        $this->load->language('extension/upsmodule/language');
        $language_code = $this->language->get('code');
        $object = new \stdClass();
        if (in_array(strtolower($language_code), ['de', 'nl', 'pl'])) {
            $object->list_title_account = [
                '1' => [$this->_title => 'Mr', $this->_title_key => 'account_Mr'],
                '2' => [$this->_title => 'Mrs', $this->_title_key => 'account_Mrs']
            ];
        } elseif (in_array(strtolower($language_code), ['es', 'it'])) {
            $object->list_title_account = [
                '1' => [$this->_title => 'Mr', $this->_title_key => 'account_Mr'],
                '2' => [$this->_title => 'Mrs', $this->_title_key => 'account_Mrs'],
                '3' => [$this->_title => 'Ms', $this->_title_key => 'account_Ms']
            ];
        } else {
            $object->list_title_account =  [
                '1' => [$this->_title => 'Mr', $this->_title_key => 'account_Mr'],
                '2' => [$this->_title => 'Miss', $this->_title_key => 'account_Miss'],
                '3' => [$this->_title => 'Mrs', $this->_title_key => 'account_Mrs'],
                '4' => [$this->_title => 'Ms', $this->_title_key => 'account_Ms']
                ];
        }

        return $object;
    }

    /**
     * ModelExtensionUpsmoduleBase getPackageCheckData
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function getPackageCheckData()
    {
        $this->load->language('extension/upsmodule/language');
        $object = new \stdClass();
        $option_setting = $this->getSetting();
        $country_code = '';
        if (!empty($option_setting) && isset($option_setting->ups_shipping_country_code)) {
            $country_code = $option_setting->ups_shipping_country_code;
        }
        $object->country_code = $country_code;

        //set weight and size check for EU (Weight: Pounds and Size: Inch)
        $object->medium_weight = 44.09;
        $object->max_weight = 154.32;
        $object->medium_size = 129.92;
        $object->max_size = 157.48;
        $object->medium_length = 100;
        $object->max_length = 100;
        $object->message_weight_warning = $this->language->get("text_warning_weight");
        $object->message_weight_error = $this->language->get("text_error_weight");
        $object->message_size_warning = $this->language->get("text_warning_size");
        $object->message_size_error = $this->language->get("text_error_size");
        $object->message_size_exceed_warning = '';
        $object->message_size_exceed_error = '';

        //set weight and size check for US
        if (!empty($country_code) && strtolower($country_code) == 'us') {
            $object->medium_weight = 44;
            $object->max_weight = 150;
            $object->medium_size = 130;
            $object->max_size = 165;
            $object->medium_length = 38;
            $object->max_length = 108;
            $object->message_weight_warning = $this->language->get("text_warning_weight_US");
            $object->message_weight_error = $this->language->get("text_error_weight_US");
            $object->message_size_warning = $this->language->get("text_warning_size_US");
            $object->message_size_error = $this->language->get("text_error_size_US");
            $object->message_size_exceed_warning = $this->language->get("text_warning_size_exceeds");
            $object->message_size_exceed_error = $this->language->get("text_error_size_exceeds");
        }

        return $object;
    }

    /**
     * ModelExtensionUpsmoduleBase createUpsReferenceLink
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function createUpsReferenceLink($page)
    {
        $this->load->model("extension/upsmodule/country");
        $constants = $this->listConstanst();
        $this->load->language($constants->link_translate);
        $list_link_data = [];
        switch ($page) {
            case 'about':
            case 'account':
                $list_link_data = $constants->about_link;
                break;
            case 'dangerous_good':
                $list_link_data = $constants->dangerous_good_link;
                break;
            case 'preparing_to_ship':
                $list_link_data = $constants->preparing_to_ship_link;
                break;
            case 'pickup':
                $list_link_data = $constants->pickup_link;
                break;
            case 'contact':
                $list_link_data = $constants->contact_link;
                break;
            default:
                break;
        }
        //get country
        $country_data = (object) $this->model_extension_upsmodule_country->getCountryCode();
        $country_code = "pl";
        if (!empty($country_data)) {
            $country_code = strtolower($country_data->value);
        }
        //get language code
        $language_code = strtolower($this->language->get("code"));
        $ups_link = $list_link_data[$country_code]["en"];
        if (isset($list_link_data[$country_code][$language_code])) {
            $ups_link = $list_link_data[$country_code][$language_code];
        }
        return $ups_link;
    }

    /**
     * ModelExtensionUpsmoduleBase listRegexValidate
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function listRegexValidate()
    {
        $object = new\ stdClass();
        $object->regex_validate =  [
            'alphanum' => '/^[a-zA-Z0-9]*$/',
            'alpha' => '/^[a-zA-Z]*$/',
            'number' => '/^\s*-?\d*(\.\d*)?\s*$/',
            'numberInt' => '/^\d+$/',
            'numberFloat' => '/^\d+(\.\d{1,2})?$/',
            $this->_email => '/[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$/',
            'phone' => '/[0|\+][0-9]+/',
            $this->_postal_code => '/[0-9]{2}[\-][0-9]{3}/',
            'accountNumber' => '/[A-Za-z0-9]{6}/',
            'invoiceNumber' => '/^[^-\s][\w\s-]+$/',
            'invoiceAmount' => '/^[0-9]+\.?[0-9]*$/',
            'validateNull' => '/^\D+$/',
            'address' => '/^\D+$/',
            'date' => '/^(0[1-9]|1[0-2])\/(0[1-9]|1\d|2\d|3[01])\/(19|20)\d{2}$/',
            'vatNumber' => '/[A-Za-z0-9]/',
            'promoNumber' => '/[A-Za-z0-9]/'
        ];
        return $object;
    }

    /**
     * ModelExtensionUpsmoduleBase languageCodeForCountry
     * @author: UPS <<EMAIL>>
     *
     * @param string $language_code //The languageCode
     *
     * @return $object
     */
    public function languageCodeForCountry($language_code)
    {
        $object = new\ stdClass();
        $object->availabelCode =   ['en_AT','de_AT','en_BE','nl_BE','fr_BE','en_CZ','cs_CZ','en_DK','da_DK',
        'en_FI','fi_FI','en_FR','fr_FR','en_DE','de_DE','en_GR','el_GR','en_HU','hu_HU','en_IE','en_IT','it_IT',
        'en_LU','fr_LU','en_NL','nl_NL','en_NO','no_NO','en_PL','pl_PL','en_PT','pt_PT','en_RU','ru_RU','en_SI',
        'en_ES','es_ES','en_SE','sv_SE','en_CH','fr_CH','de_CH','en_GB', 'en_US'];
        if (in_array($language_code, $object->availabelCode)) {
            return $language_code;
        } else {
            $language_code = 'en_GB';
            return $language_code;
        }
    }

    /**
     * ModelExtensionUpsmoduleBase listModuleBefore
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function listModuleBefore()
    {
        $object = new\ stdClass();
        $object->termcondition = 'termcondition';
        $object->country = $this->_country;
        $object->about = 'about';
        return $object;
    }

    /**
     * ModelExtensionUpsmoduleBase listModuleAfter
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function listModuleAfter()
    {
        $object = new\ stdClass();
        $object->about = 'about';
        $object->account = 'account';
        $object->shippingservice = 'shippingservice';
        $object->cashondelivery = 'cashondelivery';
        $object->accessorial = 'accessorial';
        $object->deliveryrates = 'deliveryrates';
        $object->billingpreference = 'billingpreference';
        $object->packagedimension = 'packagedimension';
        $object->openorders = 'openorders';
        $object->shipments = 'shipments';
        $object->archivedorders = 'archivedorders';
        return $object;
    }

    /**
     * ModelExtensionUpsmoduleBase getSetting
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function getSetting()
    {
        $sql = "SELECT `key`, `value` FROM " . DB_PREFIX . $this->_tb_setting;
        $query = $this->db->query($sql)->rows;
        //Create Object
        $object = new\ stdClass();
        foreach ($query as $item) {
            $object->{$item['key']} = $item[$this->_value];
        }
        return $object;
    }

    /**
     * ModelExtensionUpsmoduleBase getSetting
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function getSettingByKey($key)
    {
        $sql = "SELECT * FROM " . DB_PREFIX . $this->_tb_setting . " WHERE `key` = '{$key}'";
        $query = $this->db->query($sql)->row;
        if (array_key_exists("value", $query)) {
            return $query['value'];
        }
        return "";
    }

    /**
     * ModelExtensionUpsmoduleBase getAccountList
     * @author: UPS <<EMAIL>>
     *
     * @param string $account_number //The account_number
     *
     * @return $query
     */
    public function getAccountList($account_number = '', $default = -1)
    {
        $sql = "SELECT ac.* , (SELECT s.`value` as currency_code
                    FROM `" . DB_PREFIX . "setting`  s where s.`key` = 'config_currency' limit 1) currency_code FROM " .
                    DB_PREFIX . $this->_tb_account . " ac ";
        if (!empty($account_number)) {
            $sql.= " WHERE ac.ups_account_number = '$account_number' ";
        }
        if (intval($default) != -1) {
            $sql.= " WHERE ac.account_default = '{$default}'";
        }
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleBase getAccountListByID
     * @author: UPS <<EMAIL>>
     *
     * @param string $account_id //The account_id
     *
     * @return $query
     */
    public function getAccountListById($account_id)
    {
        $sql = "SELECT ac.*
                FROM " . DB_PREFIX . $this->_tb_account . " ac ";
        $sql.= " WHERE ac.account_id = '$account_id' ";
        $query = $this->db->query($sql)->row;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleBase getAccessorial
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getAccessorial()
    {
        $sql = $this->_tb_select_from . DB_PREFIX . $this->_tb_accessorial . " WHERE `show_shipping` = '1' ";
        $query = $this->db->query($sql)->rows;
        if (!empty($query) && is_array($query)) {
            foreach ($query as &$item) {
                $item = (object)$item;
            }
        }
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleBase getAccessorialAll
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getAccessorialAll()
    {
        $sql = $this->_tb_select_from . DB_PREFIX . $this->_tb_accessorial;
        $query = $this->db->query($sql)->rows;
        $array = [];
        foreach ($query as $item) {
            $array[$item['accessorial_key']] = $item['accessorial_name'];
        }
        return $array;
    }

    /**
     * ModelExtensionUpsmoduleBase getPackage
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getPackage()
    {
        $sql = $this->_tb_select_from . DB_PREFIX . $this->_tb_package . " order by package_id asc limit 1";
        $query = $this->db->query($sql)->row;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleBase getPackageSetting
     * @author: UPS <<EMAIL>>
     *
     * @param string $merchantKey // merchant key
     *
     * @return $query
     */
    public function getPackageSetting($merchantKey)
    {
        $objectData = new stdClass();
        // Get package setting type
        $package_setting_type = $this->getSettingByKey('ups_shipping_package_setting_type');
        $objectData->packageSettingType = $package_setting_type;
        if ($package_setting_type == 1) {
            // Get data default package
            $sql = $this->_tb_select_from . DB_PREFIX . $this->_tb_package . " order by package_id asc";
            $list_default_package = $this->db->query($sql)->rows;
            foreach ($list_default_package as $default_package) {
                $package = new stdClass();
                $package->merchantKey = $merchantKey;
                $package->name = $default_package['package_name'];
                $package->length = floatval($default_package['length']);
                $package->width = floatval($default_package['width']);
                $package->height = floatval($default_package['height']);
                $package->dimensionUnit = $default_package['unit_dimension'];
                $package->weight = floatval($default_package['weight']);
                $package->weightUnit = $this->transfer($default_package['unit_weight']);
                $package->packageItem = intval($default_package['package_item']);
                $objectData->packageDimesion[] = $package;
            }
        } else {
            // Get setting include dimension
            $include_dimension = $this->getSettingByKey('ups_shipping_package_setting_include_dimension');
            $objectData->packageDimesion->merchantKey = $merchantKey;
            $objectData->packageDimesion->includeDimensionsInRating = intval($include_dimension);
            // Get data product dimension
            $sql = $this->_tb_select_from . DB_PREFIX . "upsmodule_product_dimension order by package_id asc";
            $list_product_dimension = $this->db->query($sql)->rows;
            foreach ($list_product_dimension as $product_dimension) {
                $package = new stdClass();
                $package->name = $product_dimension['package_name'];
                $package->length = floatval($product_dimension['length']);
                $package->width = floatval($product_dimension['width']);
                $package->height = floatval($product_dimension['height']);
                $package->dimensionUnit = $product_dimension['unit_dimension'];
                $package->weight = floatval($product_dimension['weight']);
                $package->weightUnit = $this->transfer($product_dimension['unit_weight']);
//                $objectData->packageDimesion->boxDimension[] = $package;
            }
            // Get data backup rate
            $sql = "SELECT ss.service_key, fr.fallback_rate
                    FROM " . DB_PREFIX . "upsmodule_fallback_rates fr
                    INNER JOIN " . DB_PREFIX . "upsmodule_shipping_services ss
                    ON fr.service_id = ss.id ORDER BY fr.id ASC";
            $list_backup_rate = $this->db->query($sql)->rows;
            foreach ($list_backup_rate as $backup_rate) {
                $backupRate = new stdClass();
                $backupRate->serviceKey = $backup_rate['service_key'];
                $backupRate->rate = floatval($backup_rate['fallback_rate']);
                $objectData->packageDimesion->backupRate[] = $backupRate;
            }
        }
        return $objectData;
    }

    /**
     * ModelExtensionUpsmoduleBase getPackageSetting
     * @author: UPS <<EMAIL>>
     *
     *
     * @return $query
     */
    public function getPackageTransferMerchantInfo()
    {
        $objectData = new stdClass();
        // Get package setting type
        $package_setting_type = $this->getSettingByKey('ups_shipping_package_setting_type');
        if ($package_setting_type == 1) {
            // Get data default package
            $sql = $this->_tb_select_from . DB_PREFIX . $this->_tb_package . " order by package_id asc";
            $list_default_package = $this->db->query($sql)->rows;
            foreach ($list_default_package as $package_default) {
                $package_default = (object) $package_default;
                // Set package item
                $packageItem = new stdClass();
                $packageItem->option = intval($package_setting_type);
                $packageItem->name = $package_default->package_name;
                $packageItem->weight = floatval($package_default->weight);
                $packageItem->weightUnit = $this->transfer($package_default->unit_weight);
                $packageItem->length = floatval($package_default->length);
                $packageItem->width = floatval($package_default->width);
                $packageItem->height = floatval($package_default->height);
                $packageItem->dimensionUnit = $package_default->unit_dimension;
                $packageItem->packageItem = intval($package_default->package_item);
                $packageItem->includeDimensionsInRating = 0;
                $packageItem->serviceKey = '';
                $packageItem->rate = 0;
                // Add item to request
                $objectData->packageDimension[] = $packageItem;
            }
            // Set data for product dimensions
        } else {
            // Get setting include dimension
            $include_dimension = $this->getSettingByKey('ups_shipping_package_setting_include_dimension');
            // Get data backup rate
            $sql = "SELECT ss.service_key, fr.fallback_rate
                    FROM " . DB_PREFIX . "upsmodule_fallback_rates fr
                    INNER JOIN " . DB_PREFIX . "upsmodule_shipping_services ss
                    ON fr.service_id = ss.id ORDER BY fr.id ASC";
            // Query sql
            $list_backup_rate = $this->db->query($sql)->rows;
            foreach ($list_backup_rate as $fallback_rate) {
                $fallback_rate = (object) $fallback_rate;
                $packageItem = new stdClass();
                // Set package item
                $packageItem->option = intval($package_setting_type);
                $packageItem->name = '';
                $packageItem->weight = 0;
                $packageItem->weightUnit = '';
                $packageItem->length = 0;
                $packageItem->width = 0;
                $packageItem->height = 0;
                $packageItem->dimensionUnit = '';
                $packageItem->packageItem = 1;
                $packageItem->includeDimensionsInRating = intval($include_dimension);
                $packageItem->serviceKey = $fallback_rate->service_key;
                $packageItem->rate = floatval($fallback_rate->fallback_rate);
                // Add item to request
                $objectData->packageDimension[] = $packageItem;
            }
            // Get data product dimension
            $sql = $this->_tb_select_from . DB_PREFIX . "upsmodule_product_dimension order by package_id asc";
            $list_product_dimension = $this->db->query($sql)->rows;
            foreach ($list_product_dimension as $product_dimension) {
                $boxDimension = new stdClass();
                $boxDimension->name = $product_dimension['package_name'];
                $boxDimension->length = $product_dimension['length'];
                $boxDimension->width = $product_dimension['width'];
                $boxDimension->height = $product_dimension['height'];
                $boxDimension->dimensionUnit = $product_dimension['unit_dimension'];
                $boxDimension->weight = $product_dimension['weight'];
                $boxDimension->weightUnit = $this->transfer($product_dimension['unit_weight']);
//                $objectData->boxDimension[] = $boxDimension;
            }
        }
        return $objectData;
    }

    /**
     * Manage transfer
     *
     * @param string $key //The key
     *
     * @return $key
     */
    private function transfer($key)
    {
        $array = [];
        $array['lbs'] = 'Pounds';
        $array['kgs'] = 'Kg';
        if (isset($array[$key])) {
            return $array[$key];
        } else {
            return $key;
        }
    }

    /**
     * ModelExtensionUpsmoduleBase getPackageList
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getPackageList()
    {
        $sql = "SELECT pk.*, '0' as trackingnumber FROM " . DB_PREFIX . $this->_tb_package . "  pk ";
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleBase getDeliveryService
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getDeliveryService()
    {
        $setting = $this->getSetting();
        $ups_shipping_to_ap_delivery = intval($setting->ups_shipping_to_ap_delivery);
        $where = '';
        if ($ups_shipping_to_ap_delivery == 0) {
            $where.= " WHERE ss.`service_type` <> 'AP' ";
        }
        $ups_shipping_to_add_delivery = intval($setting->ups_shipping_to_add_delivery);
        if ($ups_shipping_to_add_delivery == 0) {
            if (!empty($where)) {
                $where.= " AND ss.`service_type` <> 'ADD' ";
            } else {
                $where.= " WHERE ss.`service_type` <> 'ADD' ";
            }
        }
        //check
        $sql = "
            SELECT dr.id, dr.rate_type, dr.min_order_value, dr.delivery_rate, '0' as realtime_value,
            ss.*, ss.id as service_id
                FROM  `" . DB_PREFIX . "upsmodule_delivery_rates` dr
                LEFT JOIN `" . DB_PREFIX . "upsmodule_shipping_services` ss ON ss.id = dr.service_id
        ";
        $sql .= $where;
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleBase getServiceList
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getServiceList()
    {
        $setting = $this->getSetting();
        $ups_shipping_to_ap_delivery = $setting->ups_shipping_to_ap_delivery;
        $and = '';
        if ($ups_shipping_to_ap_delivery == 0) {
            $and.= " AND ss.`service_type` <> 'AP' ";
        }
        $ups_shipping_to_add_delivery = $setting->ups_shipping_to_add_delivery;
        if ($ups_shipping_to_add_delivery == 0) {
            $and.= " AND ss.`service_type` <> 'ADD' ";
        }

        $sql = "SELECT ss.id, ss.service_key, ss.service_type, ss.service_name, ss.rate_code,
        ss.service_key_val, ss.id as service_id
            FROM " . DB_PREFIX . $this->_tb_service . " ss
            WHERE `service_selected` = '1'
            $and
            ";
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleBase getCommonInfo
     *
     * @return object $objectData
     */
    public function getCommonInfo()
    {
        $objectData = new stdClass();
        // Get merchant key
        $objectData->merchantKey = $this->getSettingByKey('ups_shipping_merchant_key');
        // Get country code
        $objectData->countryCode = $this->getSettingByKey('ups_shipping_country_code');

        return $objectData;
    }

    /**
     * ModelExtensionUpsmoduleBase beforeCallApi
     *
     * @param string $method //The method
     *
     * @return $id_log
     */
    public function beforeCallApi($method)
    {
        $this->db->query(
            "INSERT INTO " . DB_PREFIX . "upsmodule_shipping_logs_api
            SET `method` = '" . $this->db->escape($method) . "',
            `time_request` = now()"
        );
        $id_log = $this->db->getLastId();
        return $id_log;
    }

    /**
     * ModelExtensionUpsmoduleBase afterCallApi
     *
     * @param string $id       //The id
     * @param string $data_api //The data_api
     *
     * @return null
     */
    public function afterCallApi($id, $data_api)
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_shipping_logs_api
            SET `full_uri` = '" . $this->db->escape($data_api['full_uri']) . "',
            `request` = '" . $this->db->escape($data_api['request']) . "',
            `response` = '" . $this->db->escape($data_api['response']) . "',
            `time_response` = now()
            WHERE `id` = '{$id}'"
        );
    }

    /**
     * ModelExtensionUpsmoduleBase deleteMoreLog
     *
     * @return $query
     */
    public function deleteMoreLog()
    {
        $time_now = date("Y-m-d H:i:s");
        $last_date =  date('Y-m-d H:i:s', strtotime('-10 days', strtotime($time_now)));
        $condition = [];
        $condition[] = "n1.`date_created` < '{$last_date}'";
        $condition[] = "(n1.`id` < n2.`id` AND n1.`method_name` = n2.`method_name` AND n1.`key_api` = n2.`key_api`)";
        $sql = "
        DELETE n1 FROM ".
            DB_PREFIX . "upsmodule_shipping_logs_api_mamage n1, " .
            DB_PREFIX . "upsmodule_shipping_logs_api_mamage n2
            WHERE " . implode(" OR ", $condition);
        $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleBase getListLogFail
     *
     * @return $query
     */
    public function getListLogFail()
    {
        $sql = "
            SELECT *
            FROM `". DB_PREFIX . "upsmodule_shipping_logs_api_mamage`
            WHERE `count_retry` <= 5
            ORDER BY count_retry asc
        ";
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleBase setHeaderSecure
     *
     * @return null
     */
    public function setHeaderSecure()
    {
        if (isset($_COOKIE['OCSESSID'])) {
            $session_id = $_COOKIE['OCSESSID'];
            header('Set-Cookie: OCSESSID=' . $session_id. '; path=/; Secure; HttpOnly');
        }
    }

    /**
     * Account generatePass
     *
     * @param string $length //The length
     *
     * @return $randomString
     */
    private function generateUpsServiceLinkSecurityToken($length)
    {
        //characters
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        //charactersLength
        $charactersLength = strlen($characters);
        //randomString
        $randomString = '';
        //check $i
        for ($i = 0; $i < $length; $i++) {
            //randomString
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    /**
     * ModelExtensionModuleUpsmodule getPreRegisteredPluginToken
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function getPreRegisteredPluginToken()
    {
        $constants = $this->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        $option = $this->getSetting();
        // Set common info for WriteLogger api
        $commonInfo = $this->getCommonInfo();

        include_once"$constants->link_api_manager";
        $webstore_link = '';
        $ups_service_link_url = '';
        if ($this->request->server['HTTPS']) {
            $webstore_link = HTTPS_CATALOG;
            $ups_service_link_url = HTTPS_CATALOG . 'index.php?route=extension/upsmodule/upsservice/UpsServiceLink';
        } else {
            $webstore_link = HTTP_CATALOG;
            $ups_service_link_url = HTTP_CATALOG . 'index.php?route=extension/upsmodule/upsservice/UpsServiceLink';
        }
        $ups_service_token = $this->generateUpsServiceLinkSecurityToken(30);
        $this->saveOptionSetting('ups_shipping_service_link_security_token', $ups_service_token);
        $Api = new Manage();
        $data = new \stdClass();
        $data->MerchantKey = $option->ups_shipping_merchant_key;
        $data->WebstoreUrl = $webstore_link;
        $data->WebstoreUpsServiceLinkSecurityToken = $ups_service_token;
        $data->WebstorePlatform = "OpenCart";
        $data->WebstorePlatformVersion = VERSION;
        $data->UpsReadyPluginName = $this->language->get("txt_extension_name");
        $data->UpsReadyPluginVersion = $constants->txt_extension_version;
        $data->VerboseResponseSecurityKey = "";
        // $data->VerboseResponseSecurityKey = "I'm a developer";
        $data->WebstoreUpsServiceLinkUrl = $ups_service_link_url;
        $id_log = $this->beforeCallApi($constants->Handshake);
        $Api->setCommonApiInfo($commonInfo);
        $response = $Api->handShake($data);
        if(property_exists($response, 'preRegisteredPluginToken')){
            $this->saveOptionSetting('ups_shipping_pre_registered_plugin_token',$response->preRegisteredPluginToken);
            }
        $data_api = $Api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->afterCallApi($id_log, $data_api);
    }

    /**
     * ModelExtensionModuleUpsmodule saveOptionSetting
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function saveOptionSetting($key, $value)
    {
        $option = $this->getSetting();
        if (isset($option->{$key})) {
            //update
            $sql = "UPDATE " . DB_PREFIX . "upsmodule_setting
                SET `value` = '" . $value . "'
                WHERE `key` = '" . $key ."'";
            $this->db->query($sql);
        } else {
            $sql = "
                INSERT INTO " . DB_PREFIX . "upsmodule_setting (`store_id`, `code`, `key`, `value`, `serialized`) " .
                "VALUES (0, 'config', '" . $key . "', '" . $value . "', 0)
                ";
            $this->db->query($sql);
        }
    }

    /**
     * ModelExtensionModuleUpsmodule doRegisteredPluginToken
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function doRegisteredPluginToken($license)
    {
        $constants = $this->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        $option = $this->getSetting();
        // Set common info for WriteLogger api
        $commonInfo = $this->getCommonInfo();

        include_once"$constants->link_api_manager";
        $webstore_link = '';
        if ($this->request->server['HTTPS']) {
            $webstore_link = HTTPS_CATALOG;
        } else {
            $webstore_link = HTTP_CATALOG;
        }
        $Api = new Manage();
        $data = new \stdClass();
        $data->MerchantKey = $option->ups_shipping_merchant_key;
        $data->WebstoreUrl = $webstore_link;
        $data->WebstoreUpsServiceLinkSecurityToken = $this->session->data[$constants->user_token];
        $data->WebstorePlatform = "OpenCart";
        $data->WebstorePlatformVersion = VERSION;
        $data->UpsReadyPluginName = $this->language->get("txt_extension_name");
        $data->UpsReadyPluginVersion = $constants->txt_extension_version;
        try {
            $id_log = $this->beforeCallApi($constants->RegisteredPluginToken);
            $Api->setCommonApiInfo($commonInfo);
            $response = $Api->registeredPluginToken($data, $license);
            $data_api = $Api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
            $this->afterCallApi($id_log, $data_api);
            if ($response) {
                $this->saveOptionSetting('ups_shipping_registered_plugin_token', $response);
            } else {
                $this->saveOptionSetting('ups_shipping_registered_plugin_token', '');
            }
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * ModelExtensionModuleUpsmodule getNewRegisteredPluginToken
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function getNewRegisteredPluginToken()
    {
        //Load model
        $this->load->model($this->_api_model);
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        $this->doRegisteredPluginToken($license);
    }

    /**
     * ModelExtensionUpsmoduleBase getBingMapCredential
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function getBingMapCredential($count = 1)
    {
        $constants = $this->listConstanst();
        //load language
        $option = $this->getSetting();
        // Set common info for WriteLogger api
        $commonInfo = $this->getCommonInfo();

        include_once"$constants->link_api_manager";
        $Api = new Manage();
        try {
            $registered_token = "";
            if (isset($option->ups_shipping_registered_plugin_token)) {
                $registered_token = $option->ups_shipping_registered_plugin_token;
            }
            $id_log = $this->beforeCallApi('SecurityService/UpsBingMapsKey');
            $Api->setCommonApiInfo($commonInfo);
            $response = $Api->getUpsBingMapsKey($registered_token);
            $data_api = $Api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
            $this->afterCallApi($id_log, $data_api);
            if (!empty($response->data)) {
                $this->saveOptionSetting('ups_shipping_bing_map_key', $response->data);
            } else {
                if (isset($response->error->errorCode) && intval($response->error->errorCode) === 401) {
                    $this->getNewRegisteredPluginToken();
                    $count += 1;
                    if ($count <= 3) {
                        $response = $this->getBingMapCredential($count);
                    }
                } else {
                    $this->saveOptionSetting('ups_shipping_bing_map_key', "");
                }
            }
        } catch (Exception $ex) {
            $response = false;
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmoduleBase doUpgradeVersion
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function doUpgradeVersion($option = 1)
    {

        try {
            $check = false;
            if ($option != 1) {
                //load model
                $this->load->model('setting/extension');
                $installed_modules = $this->model_setting_extension->getInstalled('module');
                if (in_array('upsmodule', $installed_modules)) {
                    $check = true;
                }
            } else {
                $check = true;
            }
            if ($check) {
                $this->load->model('extension/upsmodule/pluginmanage');
                $constants = $this->listConstanst();
                $version = $constants->txt_extension_version;
                $setting_data = $this->getSetting();

                if (!isset($setting_data->ups_shipping_check_manage)) {
                    if (!empty($setting_data->ups_shipping_menu_billling_preference) &&
                        intval($setting_data->ups_shipping_menu_billling_preference) == 1
                    ) {
                        $this->getNewRegisteredPluginToken();
                        $this->getBingMapCredential();
                        $this->model_extension_upsmodule_pluginmanage->transferMerchantInfo();
                    }
                } else {
                    if (intval($setting_data->ups_shipping_check_manage) != 1) {
                        $this->getNewRegisteredPluginToken();
                        $this->model_extension_upsmodule_pluginmanage->transferMerchantInfo();
                    }
                }
                //upgrade
                if (!isset($setting_data->ups_shipping_version_plugin)) {
                    if (!empty($setting_data->ups_shipping_check_manage) &&
                        intval($setting_data->ups_shipping_check_manage) == 1
                    ) {
                        $this->saveOptionSetting('ups_shipping_version_plugin', $version);
                        $this->getNewRegisteredPluginToken();
                        $this->model_extension_upsmodule_pluginmanage->upgradePluginVersion();
                    }

                } else {
                    if ($setting_data->ups_shipping_version_plugin != $version) {
                        $this->getNewRegisteredPluginToken();
                        $this->model_extension_upsmodule_pluginmanage->upgradePluginVersion();
                        $this->saveOptionSetting('ups_shipping_version_plugin', $version);
                    }
                }

                $this->upgradeDatabaseChange();
                $this->setShippingFeeCurrencyCode($setting_data);
            }
        } catch (Exception $ex) {
            //exception error.
        }
    }

    /**
     * ModelExtensionUpsmoduleBase getCurrencyCodeByCountryCode()
     * @author: UPS <<EMAIL>>
     *
     * @return $currency_code
     */
    public function getCurrencyCodeByCountryCode($country_code)
    {
        $list_currency = new \stdClass();
        $list_currency->PL = "PLN";
        $list_currency->GB = "GBP";
        $list_currency->FR = "EUR";
        $list_currency->DE = "EUR";
        $list_currency->ES = "EUR";
        $list_currency->IS = "ISK";
        $list_currency->IT = "EUR";
        $list_currency->NL = "EUR";
        $list_currency->BE = "EUR";
        $list_currency->US = "USD";

        $list_currency->AT = "EUR";
        
        $list_currency->BG = "BGN";
        $list_currency->HR = "HRK";
        $list_currency->CY = "EUR";
        $list_currency->CZ = "CZK";
        $list_currency->DK = "DKK";
        $list_currency->EE = "EUR";
        $list_currency->FI = "EUR";
        
        
        $list_currency->GR = "EUR";
        $list_currency->HU = "HUF";
        $list_currency->IE = "EUR";
        
        $list_currency->LV = "EUR";
        $list_currency->LT = "EUR";
        $list_currency->LU = "EUR";
        $list_currency->MT = "EUR";
        
        
        $list_currency->PT = "PLN";
        $list_currency->RO = "RON";
        $list_currency->SK = "EUR";
        $list_currency->SI = "EUR";
        
        $list_currency->SE = "SEK";

        $list_currency->CH = "CHF";
        $list_currency->RS = "RSD";
        $list_currency->NO = "NOK";

        $list_currency->TR = "TRY";
        $list_currency->JE = "GBP";
        

        if (isset($list_currency->{$country_code})) {
            return $list_currency->{$country_code};
        }
        return "";
    }

    /**
     * ModelExtensionUpsmoduleBase uninstallModuleTransfer
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function setShippingFeeCurrencyCode($setting_data)
    {
        $country_code = "PL";
        if (isset($setting_data->ups_shipping_country_code)) {
            $country_code = $setting_data->ups_shipping_country_code;
        }
        $currency = $this->getCurrencyCodeByCountryCode($country_code);
        $this->saveOptionSetting("ups_shipping_fee_currency_code", $currency);
    }

    /**
     * ModelExtensionUpsmoduleBase uninstallModuleTransfer
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function uninstallModuleTransfer($count = 1)
    {
        $constants = $this->listConstanst();
        $option = $this->getSetting();
        // Set common info for WriteLogger api
        $commonInfo = $this->getCommonInfo();
        // Instant api manage
        include_once "$constants->link_api_manager";
        $api = new Manage();
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $option->ups_shipping_merchant_key;
        $data->status = 30;
        $data->upsmodule_token = "";
        if (!empty($option->ups_shipping_registered_plugin_token)) {
            $data->upsmodule_token = $option->ups_shipping_registered_plugin_token;
        }
        //save log before call api
        $id_log = $this->beforeCallApi($constants->UpdateMerchantStatus);
        try {
            $api->setCommonApiInfo($commonInfo);
            $response = $api->updateMerchantStatus($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->afterCallApi($id_log, $data_api);

        if (isset($response->data) && $response->data !== true) {
            //check expired token key.
            if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
                $count = $count + 1;
                $this->getNewRegisteredPluginToken();
                $response = $this->uninstallModuleTransfer($count);
            }
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmoduleBase upgradeDatabaseChange
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function upgradeDatabaseChange()
    {
        try {
            $this->upgradeDatabaseForShippingService();
            $this->upgradeDatabaseForPackageDimension();
        } catch (Exception $e) {
            //error
        }
    }

    /**
     * ModelExtensionUpsmoduleBase upgradeDatabaseForPackageDimension
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    private function upgradeDatabaseForPackageDimension()
    {
        // Add column package_item to table upsmodule_package_default
        $has_package_item_column = $this->db->query("SHOW COLUMNS FROM `" . DB_PREFIX . "upsmodule_package_default` WHERE Field='package_item'")->num_rows > 0;
        if (!$has_package_item_column) {
            $this->db->query("ALTER TABLE " . DB_PREFIX . "upsmodule_package_default
                            ADD COLUMN package_item int(11) DEFAULT NULL COMMENT 'Package Item'");
        }
        // Add column package to table upsmodule_open_orders
        $has_package_item_column = $this->db->query("SHOW COLUMNS FROM `" . DB_PREFIX . "upsmodule_open_orders` WHERE Field='package'")->num_rows > 0;
        if (!$has_package_item_column) {
            $this->db->query("ALTER TABLE " . DB_PREFIX . "upsmodule_open_orders
                            ADD COLUMN package text DEFAULT NULL COMMENT 'Shipping Package'
                            AFTER accessorial_service");
        }
        // Create new table for product dimension
        $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "upsmodule_product_dimension` (
                `package_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'Package ID',
                `package_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Package Name',
                `weight` float(10,2) DEFAULT NULL COMMENT 'Weight',
                `unit_weight` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Unit weight',
                `length` float(10,2) DEFAULT NULL COMMENT 'Length',
                `width` float(10,2) DEFAULT NULL COMMENT 'Width',
                `height` float(10,2) DEFAULT NULL COMMENT 'Height',
                `unit_dimension` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Unit dimension',
                PRIMARY KEY (`package_id`)
            ) DEFAULT COLLATE=utf8_general_ci;");
        // Create new table for backup rate
        $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "upsmodule_fallback_rates` (
                `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `service_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Service Type',
                `service_id` int(11) DEFAULT NULL COMMENT 'Service ID',
                `fallback_rate` float DEFAULT NULL COMMENT 'Fallback Rate',
                PRIMARY KEY (`id`)
            ) DEFAULT COLLATE=utf8_general_ci;");
    }

    /**
     * ModelExtensionUpsmoduleBase upgradeDatabaseForShippingService
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    private function upgradeDatabaseForShippingService()
    {
        try {
            $query = "
                INSERT INTO `" . DB_PREFIX . "upsmodule_shipping_services`(`country_code`, `service_type`,
                    `service_key`, `service_key_delivery`, `service_key_val`, `service_name`, `rate_code`, `tin_t_code`,
                    `service_selected`, `service_symbol`)
                VALUES ('FR', 'AP', 'UPS_SP_SERV_FR_AP_AP_ECONOMY', 'UPS_DELI_FR_AP_AP_ECONOMY',
                    'UPS_DELI_FR_AP_AP_ECONOMY_VAL', 'UPS Access Point Economy', '70', '39', '0', '&trade;'),
                ('FR', 'AP', 'UPS_SP_SERV_FR_AP_STANDARD', 'UPS_DELI_FR_AP_STANDARD', 'UPS_DELI_FR_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPEDITED', 'UPS_DELI_FR_AP_EXPEDITED', 'UPS_DELI_FR_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPRESS_SAVER', 'UPS_DELI_FR_AP_EXPRESS_SAVER',
                    'UPS_DELI_FR_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPRESS', 'UPS_DELI_FR_AP_EXPRESS', 'UPS_DELI_FR_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPRESS_PLUS', 'UPS_DELI_FR_AP_EXPRESS_PLUS',
                    'UPS_DELI_FR_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_STANDARD', 'UPS_DELI_FR_ADD_STANDARD', 'UPS_DELI_FR_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPEDITED', 'UPS_DELI_FR_ADD_EXPEDITED',
                    'UPS_DELI_FR_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPRESS_SAVER', 'UPS_DELI_FR_ADD_EXPRESS_SAVER',
                    'UPS_DELI_FR_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPRESS', 'UPS_DELI_FR_ADD_EXPRESS', 'UPS_DELI_FR_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPRESS_PLUS', 'UPS_DELI_FR_ADD_EXPRESS_PLUS',
                    'UPS_DELI_FR_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('DE', 'AP', 'UPS_SP_SERV_DE_AP_STANDARD', 'UPS_DELI_DE_AP_STANDARD', 'UPS_DELI_DE_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPEDITED', 'UPS_DELI_DE_AP_EXPEDITED', 'UPS_DELI_DE_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS_SAVER', 'UPS_DELI_DE_AP_EXPRESS_SAVER',
                    'UPS_DELI_DE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS_MIDDAY', 'UPS_DELI_DE_AP_EXPRESS_MIDDAY',
                    'UPS_DELI_DE_AP_EXPRESS_MIDDAY_VAL', 'UPS Express 12:00', '74', '0', '0', ''),
                ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS', 'UPS_DELI_DE_AP_EXPRESS', 'UPS_DELI_DE_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS_PLUS', 'UPS_DELI_DE_AP_EXPRESS_PLUS',
                    'UPS_DELI_DE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_STANDARD', 'UPS_DELI_DE_ADD_STANDARD', 'UPS_DELI_DE_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPEDITED', 'UPS_DELI_DE_ADD_EXPEDITED',
                    'UPS_DELI_DE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS_SAVER', 'UPS_DELI_DE_ADD_EXPRESS_SAVER',
                    'UPS_DELI_DE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS_MIDDAY', 'UPS_DELI_DE_ADD_EXPRESS_MIDDAY', 'UPS_DELI_DE_ADD_EXPRESS_MIDDAY_VAL',
                    'UPS Express 12:00', '74', '25', '0', ''),
                ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS', 'UPS_DELI_DE_ADD_EXPRESS', 'UPS_DELI_DE_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS_PLUS', 'UPS_DELI_DE_ADD_EXPRESS_PLUS',
                    'UPS_DELI_DE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('ES', 'AP', 'UPS_SP_SERV_ES_AP_STANDARD', 'UPS_DELI_ES_AP_STANDARD', 'UPS_DELI_ES_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPEDITED', 'UPS_DELI_ES_AP_EXPEDITED', 'UPS_DELI_ES_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPRESS_SAVER', 'UPS_DELI_ES_AP_EXPRESS_SAVER',
                    'UPS_DELI_ES_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPRESS', 'UPS_DELI_ES_AP_EXPRESS', 'UPS_DELI_ES_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPRESS_PLUS', 'UPS_DELI_ES_AP_EXPRESS_PLUS',
                    'UPS_DELI_ES_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_STANDARD', 'UPS_DELI_ES_ADD_STANDARD', 'UPS_DELI_ES_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPEDITED', 'UPS_DELI_ES_ADD_EXPEDITED',
                    'UPS_DELI_ES_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPRESS_SAVER', 'UPS_DELI_ES_ADD_EXPRESS_SAVER',
                    'UPS_DELI_ES_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPRESS', 'UPS_DELI_ES_ADD_EXPRESS', 'UPS_DELI_ES_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPRESS_PLUS', 'UPS_DELI_ES_ADD_EXPRESS_PLUS',
                    'UPS_DELI_ES_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('IS', 'AP', 'UPS_SP_SERV_IS_AP_STANDARD', 'UPS_DELI_IS_AP_STANDARD', 'UPS_DELI_IS_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPEDITED', 'UPS_DELI_IS_AP_EXPEDITED', 'UPS_DELI_IS_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPRESS_SAVER', 'UPS_DELI_IS_AP_EXPRESS_SAVER',
                    'UPS_DELI_IS_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPRESS', 'UPS_DELI_IS_AP_EXPRESS', 'UPS_DELI_IS_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPRESS_PLUS', 'UPS_DELI_IS_AP_EXPRESS_PLUS',
                    'UPS_DELI_IS_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_STANDARD', 'UPS_DELI_IS_ADD_STANDARD', 'UPS_DELI_IS_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPEDITED', 'UPS_DELI_IS_ADD_EXPEDITED',
                    'UPS_DELI_IS_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPRESS_SAVER', 'UPS_DELI_IS_ADD_EXPRESS_SAVER',
                    'UPS_DELI_IS_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPRESS', 'UPS_DELI_IS_ADD_EXPRESS', 'UPS_DELI_IS_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPRESS_PLUS', 'UPS_DELI_IS_ADD_EXPRESS_PLUS',
                    'UPS_DELI_IS_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('IT', 'AP', 'UPS_SP_SERV_IT_AP_STANDARD', 'UPS_DELI_IT_AP_STANDARD', 'UPS_DELI_IT_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPEDITED', 'UPS_DELI_IT_AP_EXPEDITED', 'UPS_DELI_IT_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPRESS_SAVER', 'UPS_DELI_IT_AP_EXPRESS_SAVER',
                    'UPS_DELI_IT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPRESS', 'UPS_DELI_IT_AP_EXPRESS', 'UPS_DELI_IT_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPRESS_PLUS', 'UPS_DELI_IT_AP_EXPRESS_PLUS',
                    'UPS_DELI_IT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_STANDARD', 'UPS_DELI_IT_ADD_STANDARD', 'UPS_DELI_IT_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPEDITED', 'UPS_DELI_IT_ADD_EXPEDITED',
                    'UPS_DELI_IT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPRESS_SAVER', 'UPS_DELI_IT_ADD_EXPRESS_SAVER',
                    'UPS_DELI_IT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPRESS', 'UPS_DELI_IT_ADD_EXPRESS', 'UPS_DELI_IT_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPRESS_PLUS', 'UPS_DELI_IT_ADD_EXPRESS_PLUS',
                    'UPS_DELI_IT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('NL', 'AP', 'UPS_SP_SERV_NL_AP_AP_ECONOMY', 'UPS_DELI_NL_AP_AP_ECONOMY',
                    'UPS_DELI_NL_AP_AP_ECONOMY_VAL', 'UPS Access Point Economy', '70', '39', '0', '&trade;'),
                ('NL', 'AP', 'UPS_SP_SERV_NL_AP_STANDARD', 'UPS_DELI_NL_AP_STANDARD', 'UPS_DELI_NL_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPEDITED', 'UPS_DELI_NL_AP_EXPEDITED', 'UPS_DELI_NL_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPRESS_SAVER', 'UPS_DELI_NL_AP_EXPRESS_SAVER',
                    'UPS_DELI_NL_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPRESS', 'UPS_DELI_NL_AP_EXPRESS', 'UPS_DELI_NL_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPRESS_PLUS', 'UPS_DELI_NL_AP_EXPRESS_PLUS',
                    'UPS_DELI_NL_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_STANDARD', 'UPS_DELI_NL_ADD_STANDARD', 'UPS_DELI_NL_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPEDITED', 'UPS_DELI_NL_ADD_EXPEDITED',
                    'UPS_DELI_NL_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPRESS_SAVER', 'UPS_DELI_NL_ADD_EXPRESS_SAVER',
                    'UPS_DELI_NL_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPRESS', 'UPS_DELI_NL_ADD_EXPRESS', 'UPS_DELI_NL_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPRESS_PLUS', 'UPS_DELI_NL_ADD_EXPRESS_PLUS',
                    'UPS_DELI_NL_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('BE', 'AP', 'UPS_SP_SERV_BE_AP_AP_ECONOMY', 'UPS_DELI_BE_AP_AP_ECONOMY',
                    'UPS_DELI_BE_AP_AP_ECONOMY_VAL', 'UPS Access Point Economy', '70', '39', '0', '&trade;'),
                ('BE', 'AP', 'UPS_SP_SERV_BE_AP_STANDARD', 'UPS_DELI_BE_AP_STANDARD', 'UPS_DELI_BE_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPEDITED', 'UPS_DELI_BE_AP_EXPEDITED', 'UPS_DELI_BE_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPRESS_SAVER', 'UPS_DELI_BE_AP_EXPRESS_SAVER',
                    'UPS_DELI_BE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPRESS', 'UPS_DELI_BE_AP_EXPRESS', 'UPS_DELI_BE_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPRESS_PLUS', 'UPS_DELI_BE_AP_EXPRESS_PLUS',
                    'UPS_DELI_BE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_STANDARD', 'UPS_DELI_BE_ADD_STANDARD', 'UPS_DELI_BE_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPEDITED', 'UPS_DELI_BE_ADD_EXPEDITED',
                    'UPS_DELI_BE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPRESS_SAVER', 'UPS_DELI_BE_ADD_EXPRESS_SAVER',
                    'UPS_DELI_BE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPRESS', 'UPS_DELI_BE_ADD_EXPRESS', 'UPS_DELI_BE_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPRESS_PLUS', 'UPS_DELI_BE_ADD_EXPRESS_PLUS',
                    'UPS_DELI_BE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('AT', 'AP', 'UPS_SP_SERV_AT_AP_STANDARD', 'UPS_DELI_AT_AP_STANDARD', 'UPS_DELI_AT_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPEDITED', 'UPS_DELI_AT_AP_EXPEDITED', 'UPS_DELI_AT_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPRESS_SAVER', 'UPS_DELI_AT_AP_EXPRESS_SAVER',
                    'UPS_DELI_AT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPRESS', 'UPS_DELI_AT_AP_EXPRESS', 'UPS_DELI_AT_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPRESS_PLUS', 'UPS_DELI_AT_AP_EXPRESS_PLUS',
                    'UPS_DELI_AT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_STANDARD', 'UPS_DELI_AT_ADD_STANDARD', 'UPS_DELI_AT_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPEDITED', 'UPS_DELI_AT_ADD_EXPEDITED',
                    'UPS_DELI_AT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPRESS_SAVER', 'UPS_DELI_AT_ADD_EXPRESS_SAVER',
                    'UPS_DELI_AT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPRESS', 'UPS_DELI_AT_ADD_EXPRESS', 'UPS_DELI_AT_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPRESS_PLUS', 'UPS_DELI_AT_ADD_EXPRESS_PLUS',
                    'UPS_DELI_AT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('BG', 'AP', 'UPS_SP_SERV_BG_AP_STANDARD', 'UPS_DELI_BG_AP_STANDARD', 'UPS_DELI_BG_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPEDITED', 'UPS_DELI_BG_AP_EXPEDITED', 'UPS_DELI_BG_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPRESS_SAVER', 'UPS_DELI_BG_AP_EXPRESS_SAVER',
                    'UPS_DELI_BG_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPRESS', 'UPS_DELI_BG_AP_EXPRESS', 'UPS_DELI_BG_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPRESS_PLUS', 'UPS_DELI_BG_AP_EXPRESS_PLUS',
                    'UPS_DELI_BG_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_STANDARD', 'UPS_DELI_BG_ADD_STANDARD', 'UPS_DELI_BG_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPEDITED', 'UPS_DELI_BG_ADD_EXPEDITED',
                    'UPS_DELI_BG_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPRESS_SAVER', 'UPS_DELI_BG_ADD_EXPRESS_SAVER',
                    'UPS_DELI_BG_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPRESS', 'UPS_DELI_BG_ADD_EXPRESS', 'UPS_DELI_BG_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPRESS_PLUS', 'UPS_DELI_BG_ADD_EXPRESS_PLUS',
                    'UPS_DELI_BG_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('HR', 'AP', 'UPS_SP_SERV_HR_AP_STANDARD', 'UPS_DELI_HR_AP_STANDARD', 'UPS_DELI_HR_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPEDITED', 'UPS_DELI_HR_AP_EXPEDITED', 'UPS_DELI_HR_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPRESS_SAVER', 'UPS_DELI_HR_AP_EXPRESS_SAVER',
                    'UPS_DELI_HR_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPRESS', 'UPS_DELI_HR_AP_EXPRESS', 'UPS_DELI_HR_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPRESS_PLUS', 'UPS_DELI_HR_AP_EXPRESS_PLUS',
                    'UPS_DELI_HR_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_STANDARD', 'UPS_DELI_HR_ADD_STANDARD', 'UPS_DELI_HR_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPEDITED', 'UPS_DELI_HR_ADD_EXPEDITED',
                    'UPS_DELI_HR_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPRESS_SAVER', 'UPS_DELI_HR_ADD_EXPRESS_SAVER',
                    'UPS_DELI_HR_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPRESS', 'UPS_DELI_HR_ADD_EXPRESS', 'UPS_DELI_HR_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPRESS_PLUS', 'UPS_DELI_HR_ADD_EXPRESS_PLUS',
                    'UPS_DELI_HR_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('CY', 'AP', 'UPS_SP_SERV_CY_AP_STANDARD', 'UPS_DELI_CY_AP_STANDARD', 'UPS_DELI_CY_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPEDITED', 'UPS_DELI_CY_AP_EXPEDITED', 'UPS_DELI_CY_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPRESS_SAVER', 'UPS_DELI_CY_AP_EXPRESS_SAVER',
                    'UPS_DELI_CY_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPRESS', 'UPS_DELI_CY_AP_EXPRESS', 'UPS_DELI_CY_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPRESS_PLUS', 'UPS_DELI_CY_AP_EXPRESS_PLUS',
                    'UPS_DELI_CY_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_STANDARD', 'UPS_DELI_CY_ADD_STANDARD', 'UPS_DELI_CY_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPEDITED', 'UPS_DELI_CY_ADD_EXPEDITED',
                    'UPS_DELI_CY_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPRESS_SAVER', 'UPS_DELI_CY_ADD_EXPRESS_SAVER',
                    'UPS_DELI_CY_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPRESS', 'UPS_DELI_CY_ADD_EXPRESS', 'UPS_DELI_CY_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPRESS_PLUS', 'UPS_DELI_CY_ADD_EXPRESS_PLUS',
                    'UPS_DELI_CY_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_STANDARD', 'UPS_DELI_CZ_AP_STANDARD', 'UPS_DELI_CZ_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPEDITED', 'UPS_DELI_CZ_AP_EXPEDITED', 'UPS_DELI_CZ_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPRESS_SAVER', 'UPS_DELI_CZ_AP_EXPRESS_SAVER',
                    'UPS_DELI_CZ_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPRESS', 'UPS_DELI_CZ_AP_EXPRESS', 'UPS_DELI_CZ_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPRESS_PLUS', 'UPS_DELI_CZ_AP_EXPRESS_PLUS',
                    'UPS_DELI_CZ_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_STANDARD', 'UPS_DELI_CZ_ADD_STANDARD', 'UPS_DELI_CZ_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPEDITED', 'UPS_DELI_CZ_ADD_EXPEDITED',
                    'UPS_DELI_CZ_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPRESS_SAVER', 'UPS_DELI_CZ_ADD_EXPRESS_SAVER',
                    'UPS_DELI_CZ_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPRESS', 'UPS_DELI_CZ_ADD_EXPRESS', 'UPS_DELI_CZ_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPRESS_PLUS', 'UPS_DELI_CZ_ADD_EXPRESS_PLUS',
                    'UPS_DELI_CZ_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('DK', 'AP', 'UPS_SP_SERV_DK_AP_STANDARD', 'UPS_DELI_DK_AP_STANDARD', 'UPS_DELI_DK_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPEDITED', 'UPS_DELI_DK_AP_EXPEDITED', 'UPS_DELI_DK_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPRESS_SAVER', 'UPS_DELI_DK_AP_EXPRESS_SAVER',
                    'UPS_DELI_DK_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPRESS', 'UPS_DELI_DK_AP_EXPRESS', 'UPS_DELI_DK_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPRESS_PLUS', 'UPS_DELI_DK_AP_EXPRESS_PLUS',
                    'UPS_DELI_DK_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_STANDARD', 'UPS_DELI_DK_ADD_STANDARD', 'UPS_DELI_DK_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPEDITED', 'UPS_DELI_DK_ADD_EXPEDITED',
                    'UPS_DELI_DK_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPRESS_SAVER', 'UPS_DELI_DK_ADD_EXPRESS_SAVER',
                    'UPS_DELI_DK_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPRESS', 'UPS_DELI_DK_ADD_EXPRESS', 'UPS_DELI_DK_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPRESS_PLUS', 'UPS_DELI_DK_ADD_EXPRESS_PLUS',
                    'UPS_DELI_DK_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('EE', 'AP', 'UPS_SP_SERV_EE_AP_STANDARD', 'UPS_DELI_EE_AP_STANDARD', 'UPS_DELI_EE_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPEDITED', 'UPS_DELI_EE_AP_EXPEDITED', 'UPS_DELI_EE_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPRESS_SAVER', 'UPS_DELI_EE_AP_EXPRESS_SAVER',
                    'UPS_DELI_EE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPRESS', 'UPS_DELI_EE_AP_EXPRESS', 'UPS_DELI_EE_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPRESS_PLUS', 'UPS_DELI_EE_AP_EXPRESS_PLUS',
                    'UPS_DELI_EE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_STANDARD', 'UPS_DELI_EE_ADD_STANDARD', 'UPS_DELI_EE_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPEDITED', 'UPS_DELI_EE_ADD_EXPEDITED',
                    'UPS_DELI_EE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPRESS_SAVER', 'UPS_DELI_EE_ADD_EXPRESS_SAVER',
                    'UPS_DELI_EE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPRESS', 'UPS_DELI_EE_ADD_EXPRESS', 'UPS_DELI_EE_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPRESS_PLUS', 'UPS_DELI_EE_ADD_EXPRESS_PLUS',
                    'UPS_DELI_EE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('FI', 'AP', 'UPS_SP_SERV_FI_AP_STANDARD', 'UPS_DELI_FI_AP_STANDARD', 'UPS_DELI_FI_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPEDITED', 'UPS_DELI_FI_AP_EXPEDITED', 'UPS_DELI_FI_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPRESS_SAVER', 'UPS_DELI_FI_AP_EXPRESS_SAVER',
                    'UPS_DELI_FI_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPRESS', 'UPS_DELI_FI_AP_EXPRESS', 'UPS_DELI_FI_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPRESS_PLUS', 'UPS_DELI_FI_AP_EXPRESS_PLUS',
                    'UPS_DELI_FI_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_STANDARD', 'UPS_DELI_FI_ADD_STANDARD', 'UPS_DELI_FI_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPEDITED', 'UPS_DELI_FI_ADD_EXPEDITED',
                    'UPS_DELI_FI_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPRESS_SAVER', 'UPS_DELI_FI_ADD_EXPRESS_SAVER',
                    'UPS_DELI_FI_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPRESS', 'UPS_DELI_FI_ADD_EXPRESS', 'UPS_DELI_FI_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPRESS_PLUS', 'UPS_DELI_FI_ADD_EXPRESS_PLUS',
                    'UPS_DELI_FI_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('GR', 'AP', 'UPS_SP_SERV_GR_AP_STANDARD', 'UPS_DELI_GR_AP_STANDARD', 'UPS_DELI_GR_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPEDITED', 'UPS_DELI_GR_AP_EXPEDITED', 'UPS_DELI_GR_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPRESS_SAVER', 'UPS_DELI_GR_AP_EXPRESS_SAVER',
                    'UPS_DELI_GR_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPRESS', 'UPS_DELI_GR_AP_EXPRESS', 'UPS_DELI_GR_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPRESS_PLUS', 'UPS_DELI_GR_AP_EXPRESS_PLUS',
                    'UPS_DELI_GR_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_STANDARD', 'UPS_DELI_GR_ADD_STANDARD', 'UPS_DELI_GR_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPEDITED', 'UPS_DELI_GR_ADD_EXPEDITED',
                    'UPS_DELI_GR_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPRESS_SAVER', 'UPS_DELI_GR_ADD_EXPRESS_SAVER',
                    'UPS_DELI_GR_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPRESS', 'UPS_DELI_GR_ADD_EXPRESS', 'UPS_DELI_GR_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPRESS_PLUS', 'UPS_DELI_GR_ADD_EXPRESS_PLUS',
                    'UPS_DELI_GR_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('HU', 'AP', 'UPS_SP_SERV_HU_AP_STANDARD', 'UPS_DELI_HU_AP_STANDARD', 'UPS_DELI_HU_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPEDITED', 'UPS_DELI_HU_AP_EXPEDITED', 'UPS_DELI_HU_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPRESS_SAVER', 'UPS_DELI_HU_AP_EXPRESS_SAVER',
                    'UPS_DELI_HU_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPRESS', 'UPS_DELI_HU_AP_EXPRESS', 'UPS_DELI_HU_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPRESS_PLUS', 'UPS_DELI_HU_AP_EXPRESS_PLUS',
                    'UPS_DELI_HU_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_STANDARD', 'UPS_DELI_HU_ADD_STANDARD', 'UPS_DELI_HU_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPEDITED', 'UPS_DELI_HU_ADD_EXPEDITED',
                    'UPS_DELI_HU_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPRESS_SAVER', 'UPS_DELI_HU_ADD_EXPRESS_SAVER',
                    'UPS_DELI_HU_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPRESS', 'UPS_DELI_HU_ADD_EXPRESS', 'UPS_DELI_HU_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPRESS_PLUS', 'UPS_DELI_HU_ADD_EXPRESS_PLUS',
                    'UPS_DELI_HU_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('IE', 'AP', 'UPS_SP_SERV_IE_AP_STANDARD', 'UPS_DELI_IE_AP_STANDARD', 'UPS_DELI_IE_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPEDITED', 'UPS_DELI_IE_AP_EXPEDITED', 'UPS_DELI_IE_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPRESS_SAVER', 'UPS_DELI_IE_AP_EXPRESS_SAVER',
                    'UPS_DELI_IE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPRESS', 'UPS_DELI_IE_AP_EXPRESS', 'UPS_DELI_IE_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPRESS_PLUS', 'UPS_DELI_IE_AP_EXPRESS_PLUS',
                    'UPS_DELI_IE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_STANDARD', 'UPS_DELI_IE_ADD_STANDARD', 'UPS_DELI_IE_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPEDITED', 'UPS_DELI_IE_ADD_EXPEDITED',
                    'UPS_DELI_IE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPRESS_SAVER', 'UPS_DELI_IE_ADD_EXPRESS_SAVER',
                    'UPS_DELI_IE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPRESS', 'UPS_DELI_IE_ADD_EXPRESS', 'UPS_DELI_IE_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPRESS_PLUS', 'UPS_DELI_IE_ADD_EXPRESS_PLUS',
                    'UPS_DELI_IE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('LV', 'AP', 'UPS_SP_SERV_LV_AP_STANDARD', 'UPS_DELI_LV_AP_STANDARD', 'UPS_DELI_LV_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPEDITED', 'UPS_DELI_LV_AP_EXPEDITED', 'UPS_DELI_LV_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPRESS_SAVER', 'UPS_DELI_LV_AP_EXPRESS_SAVER',
                    'UPS_DELI_LV_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPRESS', 'UPS_DELI_LV_AP_EXPRESS', 'UPS_DELI_LV_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPRESS_PLUS', 'UPS_DELI_LV_AP_EXPRESS_PLUS',
                    'UPS_DELI_LV_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_STANDARD', 'UPS_DELI_LV_ADD_STANDARD', 'UPS_DELI_LV_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPEDITED', 'UPS_DELI_LV_ADD_EXPEDITED',
                    'UPS_DELI_LV_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPRESS_SAVER', 'UPS_DELI_LV_ADD_EXPRESS_SAVER',
                    'UPS_DELI_LV_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPRESS', 'UPS_DELI_LV_ADD_EXPRESS', 'UPS_DELI_LV_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPRESS_PLUS', 'UPS_DELI_LV_ADD_EXPRESS_PLUS',
                    'UPS_DELI_LV_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('LT', 'AP', 'UPS_SP_SERV_LT_AP_STANDARD', 'UPS_DELI_LT_AP_STANDARD', 'UPS_DELI_LT_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPEDITED', 'UPS_DELI_LT_AP_EXPEDITED', 'UPS_DELI_LT_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPRESS_SAVER', 'UPS_DELI_LT_AP_EXPRESS_SAVER',
                    'UPS_DELI_LT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPRESS', 'UPS_DELI_LT_AP_EXPRESS', 'UPS_DELI_LT_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPRESS_PLUS', 'UPS_DELI_LT_AP_EXPRESS_PLUS',
                    'UPS_DELI_LT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_STANDARD', 'UPS_DELI_LT_ADD_STANDARD', 'UPS_DELI_LT_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPEDITED', 'UPS_DELI_LT_ADD_EXPEDITED',
                    'UPS_DELI_LT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPRESS_SAVER', 'UPS_DELI_LT_ADD_EXPRESS_SAVER',
                    'UPS_DELI_LT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPRESS', 'UPS_DELI_LT_ADD_EXPRESS', 'UPS_DELI_LT_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPRESS_PLUS', 'UPS_DELI_LT_ADD_EXPRESS_PLUS',
                    'UPS_DELI_LT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('LU', 'AP', 'UPS_SP_SERV_LU_AP_STANDARD', 'UPS_DELI_LU_AP_STANDARD', 'UPS_DELI_LU_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPEDITED', 'UPS_DELI_LU_AP_EXPEDITED', 'UPS_DELI_LU_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPRESS_SAVER', 'UPS_DELI_LU_AP_EXPRESS_SAVER',
                    'UPS_DELI_LU_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPRESS', 'UPS_DELI_LU_AP_EXPRESS', 'UPS_DELI_LU_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPRESS_PLUS', 'UPS_DELI_LU_AP_EXPRESS_PLUS',
                    'UPS_DELI_LU_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_STANDARD', 'UPS_DELI_LU_ADD_STANDARD', 'UPS_DELI_LU_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPEDITED', 'UPS_DELI_LU_ADD_EXPEDITED',
                    'UPS_DELI_LU_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPRESS_SAVER', 'UPS_DELI_LU_ADD_EXPRESS_SAVER',
                    'UPS_DELI_LU_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPRESS', 'UPS_DELI_LU_ADD_EXPRESS', 'UPS_DELI_LU_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPRESS_PLUS', 'UPS_DELI_LU_ADD_EXPRESS_PLUS',
                    'UPS_DELI_LU_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('MT', 'AP', 'UPS_SP_SERV_MT_AP_STANDARD', 'UPS_DELI_MT_AP_STANDARD', 'UPS_DELI_MT_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPEDITED', 'UPS_DELI_MT_AP_EXPEDITED', 'UPS_DELI_MT_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPRESS_SAVER', 'UPS_DELI_MT_AP_EXPRESS_SAVER',
                    'UPS_DELI_MT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPRESS', 'UPS_DELI_MT_AP_EXPRESS', 'UPS_DELI_MT_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPRESS_PLUS', 'UPS_DELI_MT_AP_EXPRESS_PLUS',
                    'UPS_DELI_MT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_STANDARD', 'UPS_DELI_MT_ADD_STANDARD', 'UPS_DELI_MT_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPEDITED', 'UPS_DELI_MT_ADD_EXPEDITED',
                    'UPS_DELI_MT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPRESS_SAVER', 'UPS_DELI_MT_ADD_EXPRESS_SAVER',
                    'UPS_DELI_MT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPRESS', 'UPS_DELI_MT_ADD_EXPRESS', 'UPS_DELI_MT_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPRESS_PLUS', 'UPS_DELI_MT_ADD_EXPRESS_PLUS',
                    'UPS_DELI_MT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('PT', 'AP', 'UPS_SP_SERV_PT_AP_STANDARD', 'UPS_DELI_PT_AP_STANDARD', 'UPS_DELI_PT_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPEDITED', 'UPS_DELI_PT_AP_EXPEDITED', 'UPS_DELI_PT_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPRESS_SAVER', 'UPS_DELI_PT_AP_EXPRESS_SAVER',
                    'UPS_DELI_PT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPRESS', 'UPS_DELI_PT_AP_EXPRESS', 'UPS_DELI_PT_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPRESS_PLUS', 'UPS_DELI_PT_AP_EXPRESS_PLUS',
                    'UPS_DELI_PT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_STANDARD', 'UPS_DELI_PT_ADD_STANDARD', 'UPS_DELI_PT_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPEDITED', 'UPS_DELI_PT_ADD_EXPEDITED',
                    'UPS_DELI_PT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPRESS_SAVER', 'UPS_DELI_PT_ADD_EXPRESS_SAVER',
                    'UPS_DELI_PT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPRESS', 'UPS_DELI_PT_ADD_EXPRESS', 'UPS_DELI_PT_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPRESS_PLUS', 'UPS_DELI_PT_ADD_EXPRESS_PLUS',
                    'UPS_DELI_PT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('RO', 'AP', 'UPS_SP_SERV_RO_AP_STANDARD', 'UPS_DELI_RO_AP_STANDARD', 'UPS_DELI_RO_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPEDITED', 'UPS_DELI_RO_AP_EXPEDITED', 'UPS_DELI_RO_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPRESS_SAVER', 'UPS_DELI_RO_AP_EXPRESS_SAVER',
                    'UPS_DELI_RO_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPRESS', 'UPS_DELI_RO_AP_EXPRESS', 'UPS_DELI_RO_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPRESS_PLUS', 'UPS_DELI_RO_AP_EXPRESS_PLUS',
                    'UPS_DELI_RO_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_STANDARD', 'UPS_DELI_RO_ADD_STANDARD', 'UPS_DELI_RO_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPEDITED', 'UPS_DELI_RO_ADD_EXPEDITED',
                    'UPS_DELI_RO_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPRESS_SAVER', 'UPS_DELI_RO_ADD_EXPRESS_SAVER',
                    'UPS_DELI_RO_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPRESS', 'UPS_DELI_RO_ADD_EXPRESS', 'UPS_DELI_RO_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPRESS_PLUS', 'UPS_DELI_RO_ADD_EXPRESS_PLUS',
                    'UPS_DELI_RO_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('SK', 'AP', 'UPS_SP_SERV_SK_AP_STANDARD', 'UPS_DELI_SK_AP_STANDARD', 'UPS_DELI_SK_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPEDITED', 'UPS_DELI_SK_AP_EXPEDITED', 'UPS_DELI_SK_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPRESS_SAVER', 'UPS_DELI_SK_AP_EXPRESS_SAVER',
                    'UPS_DELI_SK_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPRESS', 'UPS_DELI_SK_AP_EXPRESS', 'UPS_DELI_SK_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPRESS_PLUS', 'UPS_DELI_SK_AP_EXPRESS_PLUS',
                    'UPS_DELI_SK_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_STANDARD', 'UPS_DELI_SK_ADD_STANDARD', 'UPS_DELI_SK_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPEDITED', 'UPS_DELI_SK_ADD_EXPEDITED',
                    'UPS_DELI_SK_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPRESS_SAVER', 'UPS_DELI_SK_ADD_EXPRESS_SAVER',
                    'UPS_DELI_SK_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPRESS', 'UPS_DELI_SK_ADD_EXPRESS', 'UPS_DELI_SK_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPRESS_PLUS', 'UPS_DELI_SK_ADD_EXPRESS_PLUS',
                    'UPS_DELI_SK_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('SI', 'AP', 'UPS_SP_SERV_SI_AP_STANDARD', 'UPS_DELI_SI_AP_STANDARD', 'UPS_DELI_SI_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPEDITED', 'UPS_DELI_SI_AP_EXPEDITED', 'UPS_DELI_SI_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPRESS_SAVER', 'UPS_DELI_SI_AP_EXPRESS_SAVER',
                    'UPS_DELI_SI_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPRESS', 'UPS_DELI_SI_AP_EXPRESS', 'UPS_DELI_SI_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPRESS_PLUS', 'UPS_DELI_SI_AP_EXPRESS_PLUS',
                    'UPS_DELI_SI_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_STANDARD', 'UPS_DELI_SI_ADD_STANDARD', 'UPS_DELI_SI_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPEDITED', 'UPS_DELI_SI_ADD_EXPEDITED',
                    'UPS_DELI_SI_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPRESS_SAVER', 'UPS_DELI_SI_ADD_EXPRESS_SAVER',
                    'UPS_DELI_SI_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPRESS', 'UPS_DELI_SI_ADD_EXPRESS', 'UPS_DELI_SI_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPRESS_PLUS', 'UPS_DELI_SI_ADD_EXPRESS_PLUS',
                    'UPS_DELI_SI_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('SE', 'AP', 'UPS_SP_SERV_SE_AP_STANDARD', 'UPS_DELI_SE_AP_STANDARD', 'UPS_DELI_SE_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPEDITED', 'UPS_DELI_SE_AP_EXPEDITED', 'UPS_DELI_SE_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPRESS_SAVER', 'UPS_DELI_SE_AP_EXPRESS_SAVER',
                    'UPS_DELI_SE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPRESS', 'UPS_DELI_SE_AP_EXPRESS', 'UPS_DELI_SE_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPRESS_PLUS', 'UPS_DELI_SE_AP_EXPRESS_PLUS',
                    'UPS_DELI_SE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_STANDARD', 'UPS_DELI_SE_ADD_STANDARD', 'UPS_DELI_SE_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPEDITED', 'UPS_DELI_SE_ADD_EXPEDITED',
                    'UPS_DELI_SE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPRESS_SAVER', 'UPS_DELI_SE_ADD_EXPRESS_SAVER',
                    'UPS_DELI_SE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPRESS', 'UPS_DELI_SE_ADD_EXPRESS', 'UPS_DELI_SE_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPRESS_PLUS', 'UPS_DELI_SE_ADD_EXPRESS_PLUS',
                    'UPS_DELI_SE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('CH', 'AP', 'UPS_SP_SERV_CH_AP_STANDARD', 'UPS_DELI_CH_AP_STANDARD', 'UPS_DELI_CH_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPEDITED', 'UPS_DELI_CH_AP_EXPEDITED', 'UPS_DELI_CH_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPRESS_SAVER', 'UPS_DELI_CH_AP_EXPRESS_SAVER',
                    'UPS_DELI_CH_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPRESS', 'UPS_DELI_CH_AP_EXPRESS', 'UPS_DELI_CH_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPRESS_PLUS', 'UPS_DELI_CH_AP_EXPRESS_PLUS',
                    'UPS_DELI_CH_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_STANDARD', 'UPS_DELI_CH_ADD_STANDARD', 'UPS_DELI_CH_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPEDITED', 'UPS_DELI_CH_ADD_EXPEDITED',
                    'UPS_DELI_CH_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPRESS_SAVER', 'UPS_DELI_CH_ADD_EXPRESS_SAVER',
                    'UPS_DELI_CH_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPRESS', 'UPS_DELI_CH_ADD_EXPRESS', 'UPS_DELI_CH_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPRESS_PLUS', 'UPS_DELI_CH_ADD_EXPRESS_PLUS',
                    'UPS_DELI_CH_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('RS', 'AP', 'UPS_SP_SERV_RS_AP_STANDARD', 'UPS_DELI_RS_AP_STANDARD', 'UPS_DELI_RS_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPEDITED', 'UPS_DELI_RS_AP_EXPEDITED', 'UPS_DELI_RS_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPRESS_SAVER', 'UPS_DELI_RS_AP_EXPRESS_SAVER',
                    'UPS_DELI_RS_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPRESS', 'UPS_DELI_RS_AP_EXPRESS', 'UPS_DELI_RS_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPRESS_PLUS', 'UPS_DELI_RS_AP_EXPRESS_PLUS',
                    'UPS_DELI_RS_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_STANDARD', 'UPS_DELI_RS_ADD_STANDARD', 'UPS_DELI_RS_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPEDITED', 'UPS_DELI_RS_ADD_EXPEDITED',
                    'UPS_DELI_RS_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPRESS_SAVER', 'UPS_DELI_RS_ADD_EXPRESS_SAVER',
                    'UPS_DELI_RS_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPRESS', 'UPS_DELI_RS_ADD_EXPRESS', 'UPS_DELI_RS_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPRESS_PLUS', 'UPS_DELI_RS_ADD_EXPRESS_PLUS',
                    'UPS_DELI_RS_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('NO', 'AP', 'UPS_SP_SERV_NO_AP_STANDARD', 'UPS_DELI_NO_AP_STANDARD', 'UPS_DELI_NO_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPEDITED', 'UPS_DELI_NO_AP_EXPEDITED', 'UPS_DELI_NO_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPRESS_SAVER', 'UPS_DELI_NO_AP_EXPRESS_SAVER',
                    'UPS_DELI_NO_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPRESS', 'UPS_DELI_NO_AP_EXPRESS', 'UPS_DELI_NO_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPRESS_PLUS', 'UPS_DELI_NO_AP_EXPRESS_PLUS',
                    'UPS_DELI_NO_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_STANDARD', 'UPS_DELI_NO_ADD_STANDARD', 'UPS_DELI_NO_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPEDITED', 'UPS_DELI_NO_ADD_EXPEDITED',
                    'UPS_DELI_NO_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPRESS_SAVER', 'UPS_DELI_NO_ADD_EXPRESS_SAVER',
                    'UPS_DELI_NO_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPRESS', 'UPS_DELI_NO_ADD_EXPRESS', 'UPS_DELI_NO_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPRESS_PLUS', 'UPS_DELI_NO_ADD_EXPRESS_PLUS',
                    'UPS_DELI_NO_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('TR', 'AP', 'UPS_SP_SERV_TR_AP_STANDARD', 'UPS_DELI_TR_AP_STANDARD', 'UPS_DELI_TR_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPEDITED', 'UPS_DELI_TR_AP_EXPEDITED', 'UPS_DELI_TR_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPRESS_SAVER', 'UPS_DELI_TR_AP_EXPRESS_SAVER',
                    'UPS_DELI_TR_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPRESS', 'UPS_DELI_TR_AP_EXPRESS', 'UPS_DELI_TR_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPRESS_PLUS', 'UPS_DELI_TR_AP_EXPRESS_PLUS',
                    'UPS_DELI_TR_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_STANDARD', 'UPS_DELI_TR_ADD_STANDARD', 'UPS_DELI_TR_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPEDITED', 'UPS_DELI_TR_ADD_EXPEDITED',
                    'UPS_DELI_TR_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPRESS_SAVER', 'UPS_DELI_TR_ADD_EXPRESS_SAVER',
                    'UPS_DELI_TR_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPRESS', 'UPS_DELI_TR_ADD_EXPRESS', 'UPS_DELI_TR_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPRESS_PLUS', 'UPS_DELI_TR_ADD_EXPRESS_PLUS',
                    'UPS_DELI_TR_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('JE', 'AP', 'UPS_SP_SERV_JE_AP_STANDARD', 'UPS_DELI_JE_AP_STANDARD', 'UPS_DELI_JE_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '68', '0', ''),
                ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPEDITED', 'UPS_DELI_JE_AP_EXPEDITED', 'UPS_DELI_JE_AP_EXPEDITED_VAL',
                    'UPS Expedited', '08', '05', '0', '&reg;'),
                ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPRESS_SAVER', 'UPS_DELI_JE_AP_EXPRESS_SAVER',
                    'UPS_DELI_JE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPRESS', 'UPS_DELI_JE_AP_EXPRESS', 'UPS_DELI_JE_AP_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPRESS_PLUS', 'UPS_DELI_JE_AP_EXPRESS_PLUS',
                    'UPS_DELI_JE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_STANDARD', 'UPS_DELI_JE_ADD_STANDARD', 'UPS_DELI_JE_ADD_STANDARD_VAL',
                    'UPS&reg; Standard', '11', '25', '0', ''),
                ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPEDITED', 'UPS_DELI_JE_ADD_EXPEDITED',
                    'UPS_DELI_JE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
                ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPRESS_SAVER', 'UPS_DELI_JE_ADD_EXPRESS_SAVER',
                    'UPS_DELI_JE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
                ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPRESS', 'UPS_DELI_JE_ADD_EXPRESS', 'UPS_DELI_JE_ADD_EXPRESS_VAL',
                    'UPS Express', '07', '24', '0', '&reg;'),
                ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPRESS_PLUS', 'UPS_DELI_JE_ADD_EXPRESS_PLUS',
                    'UPS_DELI_JE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_GROUND', 'UPS_DELI_US_AP_GROUND', 'UPS_DELI_US_AP_GROUND_VAL',
                    'UPS&reg; Ground', '03', 'NULL', '0', ''),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_3DAY_SELECT', 'UPS_DELI_US_AP_3DAY_SELECT',
                    'UPS_DELI_US_AP_3DAY_SELECT_VAL', 'UPS 3 Day Select', '12', 'NULL', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_2ND_DAY_AIR', 'UPS_DELI_US_AP_2ND_DAY_AIR',
                    'UPS_DELI_US_AP_2ND_DAY_AIR_VAL', 'UPS 2nd Day Air', '02', 'NULL', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_2ND_DAY_AIR_AM', 'UPS_DELI_US_AP_2ND_DAY_AIR_AM',
                    'UPS_DELI_US_AP_2ND_DAY_AIR_AM_VAL', 'UPS 2nd Day Air A.M', '59', 'NULL', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_NEXT_DAY_AIR_SAVER', 'UPS_DELI_US_AP_NEXT_DAY_AIR_SAVER',
                    'UPS_DELI_US_AP_NEXT_DAY_AIR_SAVER_VAL', 'UPS Next Day Air Saver', '13', '', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_NEXT_DAY_AIR', 'UPS_DELI_US_AP_NEXT_DAY_AIR',
                    'UPS_DELI_US_AP_NEXT_DAY_AIR_VAL', 'UPS Next Day Air', '01', 'NULL', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_NEXT_DAY_AIR_EARLY', 'UPS_DELI_US_AP_NEXT_DAY_AIR_EARLY',
                    'UPS_DELI_US_AP_NEXT_DAY_AIR_EARLY_VAL', 'UPS Next Day Air&reg; Early', '14', '', '0', ''),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_STANDARD', 'UPS_DELI_US_AP_STANDARD', 'UPS_DELI_US_AP_STANDARD_VAL',
                    'UPS&reg; Standard', '11', 'NULL', '0', ''),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_EXPEDITED', 'UPS_DELI_US_AP_WW_EXPEDITED',
                    'UPS_DELI_US_AP_WW_EXPEDITED_VAL', 'UPS Worldwide Expedited', '08', 'NULL', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_SAVER', 'UPS_DELI_US_AP_WW_SAVER', 'UPS_DELI_US_AP_WW_SAVER_VAL',
                    'UPS Worldwide Saver', '65', 'NULL', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_EXPRESS', 'UPS_DELI_US_AP_WW_EXPRESS',
                    'UPS_DELI_US_AP_WW_EXPRESS_VAL', 'UPS Worldwide Express', '07', 'NULL', '0', '&reg;'),
                ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_EXPRESS_PLUS', 'UPS_DELI_US_AP_WW_EXPRESS_PLUS',
                    'UPS_DELI_US_AP_WW_EXPRESS_PLUS_VAL', 'UPS Worldwide Express Plus', '54', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_GROUND', 'UPS_DELI_US_ADD_GROUND', 'UPS_DELI_US_ADD_GROUND_VAL',
                    'UPS&reg; Ground', '03', 'NULL', '0', ''),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_3DAY_SELECT', 'UPS_DELI_US_ADD_3DAY_SELECT',
                    'UPS_DELI_US_ADD_3DAY_SELECT_VAL', 'UPS 3 Day Select', '12', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_2ND_DAY_AIR', 'UPS_DELI_US_ADD_2ND_DAY_AIR',
                    'UPS_DELI_US_ADD_2ND_DAY_AIR_VAL', 'UPS 2nd Day Air', '02', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_2ND_DAY_AIR_AM', 'UPS_DELI_US_ADD_2ND_DAY_AIR_AM',
                    'UPS_DELI_US_ADD_2ND_DAY_AIR_AM_VAL', 'UPS 2nd Day Air A.M', '59', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_NEXT_DAY_AIR_SAVER', 'UPS_DELI_US_ADD_NEXT_DAY_AIR_SAVER',
                    'UPS_DELI_US_ADD_NEXT_DAY_AIR_SAVER_VAL', 'UPS Next Day Air Saver', '13', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_NEXT_DAY_AIR', 'UPS_DELI_US_ADD_NEXT_DAY_AIR',
                    'UPS_DELI_US_ADD_NEXT_DAY_AIR_VAL', 'UPS Next Day Air', '01', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_NEXT_DAY_AIR_EARLY', 'UPS_DELI_US_ADD_NEXT_DAY_AIR_EARLY',
                    'UPS_DELI_US_ADD_NEXT_DAY_AIR_EARLY_VAL', 'UPS Next Day Air&reg; Early', '14', 'NULL', '0', ''),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_STANDARD', 'UPS_DELI_US_ADD_STANDARD',
                    'UPS_DELI_US_ADD_STANDARD_VAL', 'UPS&reg; Standard', '11', 'NULL', '0', ''),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_WW_EXPEDITED', 'UPS_DELI_US_ADD_WW_EXPEDITED',
                    'UPS_DELI_US_ADD_WW_EXPEDITED_VAL', 'UPS Worldwide Expedited', '08', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_WW_SAVER', 'UPS_DELI_US_ADD_WW_SAVER',
                    'UPS_DELI_US_ADD_WW_SAVER_VAL', 'UPS Worldwide Saver', '65', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_WW_EXPRESS', 'UPS_DELI_US_ADD_WW_EXPRESS',
                    'UPS_DELI_US_ADD_WW_EXPRESS_VAL', 'UPS Worldwide Express', '07', 'NULL', '0', '&reg;'),
                ('US', 'ADD', 'UPS_SP_SERV_US_ADD_WW_EXPRESS_PLUS', 'UPS_DELI_US_ADD_WW_EXPRESS_PLUS',
                    'UPS_DELI_US_ADD_WW_EXPRESS_PLUS_VAL', 'UPS Worldwide Express Plus', '54', 'NULL', '0', '&reg;')
                ON DUPLICATE KEY UPDATE `service_key_val` = values(`service_key_val`);";
            $this->db->query($query);
            // echo "<pre>";
            // print_r($query);
            // die();
            $this->upgradeSaturdayDeliveryDbChange();
            $query2 = "
                ALTER TABLE `" . DB_PREFIX . "upsmodule_account`
                ADD COLUMN `state_province_code` CHAR(50) NULL AFTER `city`;
            ";
            $this->db->query($query2);
        } catch (Exception $e) {
            //error
        }
    }

    /**
     * ModelExtensionUpsmoduleBase upgradeSaturdayDeliveryDbChange
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function upgradeSaturdayDeliveryDbChange()
    {
        try {
            //get country code
            $setting_data = $this->getSetting();
            $country_code = "";
            if (isset($setting_data->ups_shipping_country_code)) {
                $country_code = $setting_data->ups_shipping_country_code;
            }
            if (!empty($country_code)) {
                $query = "";
                if ($country_code != 'US') {
                    $query = "
                        INSERT INTO `" . DB_PREFIX . "upsmodule_shipping_services`(`country_code`, `service_type`,
                            `service_key`, `service_key_delivery`, `service_key_val`, `service_name`, `rate_code`,
                            `tin_t_code`, `service_selected`, `service_symbol`)
                        VALUES ('{$country_code}', 'AP', 'UPS_SP_SERV_{$country_code}_AP_STANDARD_SATDELI',
                            'UPS_DELI_{$country_code}_AP_STANDARD_SATDELI',
                            'UPS_DELI_{$country_code}_AP_STANDARD_SATDELI_VAL',
                            'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
                        ('{$country_code}', 'ADD', 'UPS_SP_SERV_{$country_code}_ADD_STANDARD_SATDELI',
                            'UPS_DELI_{$country_code}_ADD_STANDARD_SATDELI',
                            'UPS_DELI_{$country_code}_ADD_STANDARD_SATDELI_VAL',
                            'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
                        ('{$country_code}', 'AP', 'UPS_SP_SERV_{$country_code}_AP_EXPRESS_SATDELI',
                            'UPS_DELI_{$country_code}_AP_EXPRESS_SATDELI',
                            'UPS_DELI_{$country_code}_AP_EXPRESS_SATDELI_VAL',
                            'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
                        ('{$country_code}', 'ADD', 'UPS_SP_SERV_{$country_code}_ADD_EXPRESS_SATDELI',
                            'UPS_DELI_{$country_code}_ADD_EXPRESS_SATDELI',
                            'UPS_DELI_{$country_code}_ADD_EXPRESS_SATDELI_VAL',
                            'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', '')
                        ON DUPLICATE KEY UPDATE `service_key_val` = values(`service_key_val`);";
                }
                $this->db->query($query);
            }
        } catch (Exception $e) {
            //error
        }
    }
}
