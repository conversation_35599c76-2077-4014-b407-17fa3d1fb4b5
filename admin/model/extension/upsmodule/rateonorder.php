<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleOpenorders file
 *
 * @category Openorders_Model
 */

class ModelExtensionUpsmoduleRateonorder extends Model
{
	public function getConfigs()
    {
        $sql = "SELECT us.`key`, us.`value`
                    FROM `" . DB_PREFIX . "upsmodule_setting` us;
            ";
        $query = $this->db->query($sql)->rows;
        $object = new\ stdClass();
        foreach ($query as $item) {
            $object->{$item['key']} = $item['value'];
        }
        return $object;
    }

    public function getPackageByOrder($order_id)
    {
    	$sql = "SELECT `package` FROM `" . DB_PREFIX . "upsmodule_open_orders` WHERE `order_id_opencart` = ".$order_id;
    	$query = $this->db->query($sql);
        return $query->row;
    }

    public function getLicense()
    {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "upsmodule_shipping_license ");
        $querys = $query->row;
        if (!empty($querys['id'])) {
            return $querys;
        } else {
            $object = new\ stdClass();
            $object->id = '';
            $object->AccessLicenseText = '';
            $object->Username = 'nousername';
            $object->Password = 'nopassword';
            $object->AccessLicenseNumber = 'nokey';
            return (array)$object;
        }
    }

    public function getDateTime()
    {
        $sql = "select now() as gettime;";
        $query = $this->db->query($sql)->row;
        return $query['gettime'];
    }

    public function getEnableServices()
    {
    	$sql = "SELECT `service_name`, `rate_code` FROM `" . DB_PREFIX . "upsmodule_shipping_services` WHERE `service_type` = 'ADD' AND `service_selected` = 1";
    	$query = $this->db->query($sql);
        return $query->rows;
    }
}