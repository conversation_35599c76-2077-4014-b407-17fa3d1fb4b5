<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleShipments file
 *
 * @category Shipments_Model
 */

class ModelExtensionUpsmoduleShipments extends Model
{
    private $_special_drop = "'
    ";
    private $_update = "UPDATE ";
    private $_base_model = 'extension/upsmodule/base';

    /**
     * ModelExtensionUpsmoduleShipments getListShipment
     *
     * @param string $data //The data
     *
     * @return $query
     */
    public function getListShipment($data = [])
    {
        try {
            //sql
            $sql = "
                SELECT s.*,  DATE_FORMAT(s.`create_date`, '%b %d, %Y') as datesort, DATE_FORMAT(s.`create_date`, '%T')
                as timesort, s.`shipment_number`,
                so.`currency_code`, so.`order_id`, so.`shipping_zone`, so.`shipping_address_1`, so.`shipping_address_2`,
                so.`shipping_city`,
                upssh.`service_type`,
                ust.`id` as trackingId , ust.`tracking_number`, ust.`package_detail`,
                uso.`id` as idOrder, uso.`ap_address1`, uso.`ap_address2`, uso.`ap_address3`, uso.`ap_city`
                FROM " . DB_PREFIX . "upsmodule_shipping_shipments as s
                LEFT JOIN " . DB_PREFIX . "upsmodule_open_orders as uso ON s.`id` = uso.`shipment_id`
                LEFT JOIN " . DB_PREFIX . "upsmodule_shipping_services AS upssh on upssh.`id` = uso.`shipping_service`
                LEFT JOIN " . DB_PREFIX . "order as so ON uso.`order_id_opencart` = so.`order_id`
                LEFT JOIN " . DB_PREFIX . "upsmodule_shipping_tracking as ust ON uso.`id` = ust.`order_id`
                WHERE uso.status = 2
                ORDER BY `" . $data['sort'] . "` " . $data['order'] . "
                ";
            //sql
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
            //query
            $query = $this->db->query($sql);
            return $query->rows;
        } catch (Exception $err) {
            http_response_code(403);
            $this->response->redirect($this->url->link('error/not_found&user_token=' . $this->session->data['user_token']));
        }
    }

    /**
     * ModelExtensionUpsmoduleShipments getExportShipmentData
     *
     * @param string $list_tracking_id //The listTrackingId
     * @param string $order_by        //The orderBy
     *
     * @return $query
     */
    public function getExportShipmentData($list_tracking_id, $order_by)
    {
        //sql
        $sql = "
            SELECT s.`id` as shipment_id, s.`status`, s.`cod`, s.`shipping_fee`, s.`accessorial_service`,
            s.`shipping_service`, s.`shipment_number`, s.`order_value`, s.`address1`, s.`address2`, s.`address3`,
            s.`name`, s.`email`, s.`create_date`, s.`email`, s.`phone`, s.`city`, s.`country`, s.`state`, s.`postcode`,
            s.`access_point_id`, ust.`tracking_number`, ust.`package_detail`,
            o.`date_added`, o.`order_id`, o.`telephone`, o.`shipping_city`, o.`currency_code`,o.`shipping_address_1`,
            o.`shipping_address_2`, o.`shipping_zone`, o.`shipping_postcode`, o.`shipping_country`, o.`shipping_code`,
            o.`shipping_firstname`, o.`shipping_lastname`, o.`email` as customer_email,
            sv.`country_code`, sv.`service_type`, sv.`service_name`, sv.`service_symbol`,
            GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct, orpr.`total`,
            (ot.`value` * o.`currency_value`) as shippingFeeTotal,
            c.`name` as nameCountry,
            curr.`decimal_place`,
            (
            select  os.`name`
                from " . DB_PREFIX . "order_status as os where os.`order_status_id` = o.`order_status_id`
                limit 1
            ) namestatus
            FROM " . DB_PREFIX . "upsmodule_shipping_shipments as s
            LEFT JOIN " . DB_PREFIX . "upsmodule_shipping_tracking as ust ON ust.`shipment_number` =
                s.`shipment_number`
            LEFT JOIN " . DB_PREFIX . "upsmodule_open_orders as uso ON ust.`order_id` = uso.`id`
            LEFT JOIN " . DB_PREFIX . "order as o ON o.`order_id` = uso.`order_id_opencart`
            LEFT JOIN " . DB_PREFIX . "currency as curr ON curr.`currency_id` = o.`currency_id`
            LEFT JOIN " . DB_PREFIX . "upsmodule_shipping_services as sv ON sv.`id` = s.`shipping_service`
            LEFT JOIN " . DB_PREFIX . "order_product as orpr on orpr.`order_id` = o.`order_id`
            LEFT JOIN " . DB_PREFIX . "country as c on c.`iso_code_2` = s.`country`
            LEFT JOIN " . DB_PREFIX . "order_total as ot on ot.`order_id` = o.`order_id` AND ot.`code` = 'shipping'
            WHERE ust.`id` IN (" . $list_tracking_id . ") AND s.`id` = uso.`shipment_id` AND uso.`status` = 2 AND
            c.`iso_code_2` = s.`country`
            GROUP BY ust.`id`
            ORDER BY ust.`id` " . $order_by . "
        ";
        //query
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShipments getViewDetailShipment
     *
     * @param string $tracking_ids //The trackingIds
     *
     * @return $query
     */
    public function getViewDetailShipment($tracking_ids)
    {
        //sql
        $sql = "
            SELECT s.`id` as shipment_id, s.`status`, s.`cod`, s.`shipping_fee`, s.`accessorial_service`,
            s.`shipping_service`, s.`shipment_number`, s.`order_value`, s.`address1`, s.`address2`, s.`address3`,
            s.`name`, s.`email`, s.`create_date`, s.`email`, s.`phone`, s.`city`, s.`country`, s.`state`, s.`postcode`,
            o.`date_added`, o.`order_id`, o.`payment_country`, o.`payment_zone`, o.`shipping_city`, o.`currency_code`,
            o.`firstname`, o.`lastname`, o.`shipping_address_1`, o.`shipping_address_2`, o.`shipping_zone`,
            o.`shipping_postcode`, o.`shipping_country`,
            o.`payment_postcode`, o.`payment_code`, ust.`tracking_number`,
            GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct, ust.`package_detail`, ust.`tracking_number`,
            orpr.`total`, sv.`country_code`, sv.`service_type`, sv.`service_name`, sv.`service_symbol`,
            cu.`code`, cu.`symbol_left`, cu.`symbol_right`, cu.`decimal_place`,
            (
            select  os.`name`
                from " . DB_PREFIX . "order_status as os where os.`order_status_id` = o.`order_status_id`
                limit 1
            ) namestatus
            FROM " . DB_PREFIX . "upsmodule_shipping_shipments as s
            LEFT JOIN " . DB_PREFIX . "upsmodule_shipping_tracking as ust ON ust.shipment_number = s.`shipment_number`
            LEFT JOIN " . DB_PREFIX . "upsmodule_open_orders as uso ON uso.`id` = ust.`order_id`
            LEFT JOIN " . DB_PREFIX . "order as o ON o.`order_id` = uso.`order_id_opencart`
            LEFT JOIN " . DB_PREFIX . "order_product as orpr on orpr.`order_id` = o.`order_id`
            LEFT JOIN " . DB_PREFIX . "upsmodule_shipping_services as sv ON sv.`id` = s.`shipping_service`
            LEFT JOIN " . DB_PREFIX . "currency AS cu on cu.`currency_id` = o.`currency_id`
            WHERE s.`id` = uso.shipment_id AND uso.status = 2 AND ust.`id` = '" . $tracking_ids . "'";
        //query
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleShipments getListTrackingNumberByShipmentNumber
     *
     * @param string $shipment_number //The shipmentNumber
     *
     * @return $query
     */
    public function getListTrackingNumberByShipmentNumber($shipment_number)
    {
        //sql
        $sql = "
            SELECT ust.*,
            s.`id` as shipment_id,
            uso.`order_id_opencart` as order_id_cart
            FROM " . DB_PREFIX . "upsmodule_shipping_tracking as ust
            LEFT JOIN " . DB_PREFIX . "upsmodule_shipping_shipments as s ON ust.shipment_number = s.`shipment_number`
            LEFT JOIN " . DB_PREFIX . "upsmodule_open_orders as uso ON uso.`id` = ust.`order_id`
            WHERE ust.`shipment_number` = '" . $shipment_number . $this->_special_drop;
        //query
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments updateStatusCancelShipment
     *
     * @param string $order_id //The orderId
     *
     * @return $query
     */
    public function updateStatusCancelShipment($order_id)
    {
        //sql
        $sql = $this->_update . DB_PREFIX . "upsmodule_open_orders
        SET `status`= 1
        WHERE `id`= '" . $order_id . $this->_special_drop;
        //query
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments updateStatusOrder
     *
     * @param string $order_id //The orderId
     *
     * @return $query
     */
    public function updateStatusOrder($order_id)
    {
        //sql
        $sql = $this->_update . DB_PREFIX . "order
        SET `order_status_id`= 2
        WHERE `order_id` IN (" . $order_id . ")
        ";
        //query
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments updateStatusOrderViewDetail
     *
     * @param string $order_status_id //The orderStatusId
     * @param string $order_id       //The orderId
     *
     * @return $query
     */
    public function updateStatusOrderViewDetail($order_status_id, $order_id)
    {
        //sql
        $sql = $this->_update . DB_PREFIX . "order
        SET `order_status_id`= '" . $order_status_id . "'
        WHERE `order_id`= '" . $order_id . $this->_special_drop;
        //query
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments selectNameStatusOrderStatus
     *
     * @param string $name_status //The nameStatus
     *
     * @return $query
     */
    public function selectNameStatusOrderStatus($name_status)
    {
        //sql
        $sql = "
            SELECT name, order_status_id
            FROM " . DB_PREFIX . "order_status
            WHERE `name` = '" . $name_status . $this->_special_drop;
        //query
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleShipments getIdOpencart
     *
     * @param string $id_order //The idOrder
     *
     * @return $query
     */
    public function getIdOpencart($id_order)
    {
        //sql
        $sql ="
            SELECT *
            FROM " . DB_PREFIX . "upsmodule_open_orders
            WHERE `id` = '" . $id_order . $this->_special_drop;
        //query
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments deleteRowShipment
     *
     * @param string $shipment_number //The shipmentNumber
     *
     * @return $query
     */
    public function deleteRowShipment($shipment_number)
    {
        //sql
        $sql = "DELETE FROM " . DB_PREFIX . "upsmodule_shipping_shipments WHERE shipment_number = '" .
            $shipment_number . "' ";
        //query
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments deleteTracking
     *
     * @param string $shipment_number //The shipmentNumber
     *
     * @return $query
     */
    public function deleteTracking($shipment_number)
    {
        //sql
        $sql = "DELETE FROM " . DB_PREFIX . "upsmodule_shipping_tracking
        WHERE shipment_number = '" . $shipment_number . "' ";
        //query
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments deleteShipmentid
     *
     * @param string $order_id_opencart //The order_id_opencart
     *
     * @return null
     */
    public function deleteShipmentid($order_id_opencart)
    {
        //sql
        $sql ="UPDATE " . DB_PREFIX . "upsmodule_open_orders
        SET `shipment_id` = NULL
        where `order_id_opencart` = '" . $order_id_opencart . "' ";
        //query
        $query = $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleShipments createShipment
     *
     * @param string $shipment_number  //The shipmentNumber
     * @param string $shipping_service //The shippingService
     * @param string $accessorial     //The accessorial
     * @param string $shipping_fee     //The shippingFee
     * @param string $COD             //The COD
     * @param string $name            //The stanamete
     * @param string $state           //The state
     * @param string $phone           //The phone
     * @param string $address1        //The address1
     * @param string $address2        //The address2
     * @param string $address3        //The address3
     * @param string $city            //The city
     * @param string $postcode        //The postcode
     * @param string $country         //The country
     * @param string $email           //The email
     * @param string $order_value     //The order_value
     * @param string $access_point_id //The access_point_id
     *
     * @return $query
     */
    public function createShipment(
        $shipment_number,
        $shipping_service,
        $accessorial,
        $shipping_fee,
        $COD,
        $name,
        $state,
        $phone,
        $address1,
        $address2,
        $address3,
        $city,
        $postcode,
        $country,
        $email,
        $order_value,
        $access_point_id = ''
    ) {
        //sql
        $sql = "
        INSERT INTO ". DB_PREFIX . "upsmodule_shipping_shipments
        SET shipment_number = '" . $shipment_number . "',
            shipping_service = '" . $shipping_service . "',
            accessorial_service = '{$accessorial}',
            create_date = now(),
            status = 'Status not available',
            cod = '" . $COD . "',
            shipping_fee = '" . $shipping_fee . "',
            name = '" . $this->db->escape($name) . "',
            address1 = '" . $this->db->escape($address1) . "',
            address2 = '" . $this->db->escape($address2) . "',
            address3 = '" . $this->db->escape($address3) . "',
            state = '" . $this->db->escape($state) . "',
            postcode = '" . $this->db->escape($postcode) . "',
            city = '" . $this->db->escape($city) . "',
            country = '" . $this->db->escape($country) . "',
            phone = '" . $phone . "',
            email = '" . $email . "',
            order_value =  '" . $order_value . "',
            access_point_id = '" . $access_point_id . "'";
        //query
        $query = $this->db->query($sql);
        //region send to data manage
        //end
        return $this->db->getLastId();
    }

    /**
     * ModelExtensionUpsmoduleShipments getShipmentInfo
     *
     * @param string $shipment_number //The shipment_number
     *
     * @return null
     */
    public function getShipmentInfo($shipment_number)
    {
        //sql
        $sql = "
            SELECT ssm.id, ssm.shipment_number, ssm.cod, ssm.shipping_fee, ssm.order_value, ssm.shipping_service,
                ssm.address1, ssm.address2, ssm.address3, ssm.state, ssm.city, ssm.postcode, ssm.country,
                ss.service_type, ss.service_key, ss.service_name, ss.rate_code,
                group_concat(oo.order_id_opencart) as orderid,  ssm.`status`,
                (
                    select group_concat(op.quantity, ' x ', op.name)
                    FROM `". DB_PREFIX . "order_product` op
                    where FIND_IN_SET(op.order_id, group_concat(oo.order_id_opencart))
                ) as produc_name, DATE_FORMAT(od.date_added, '%Y-%m-%d') date_added,
                ss.id as service_id, ss.service_type, ssm.accessorial_service
                FROM `". DB_PREFIX . "upsmodule_shipping_shipments` ssm
                LEFT JOIN `". DB_PREFIX . "upsmodule_open_orders` oo on oo.shipment_id = ssm.id
                LEFT JOIN `". DB_PREFIX . "upsmodule_shipping_services` ss on ss.id = ssm.shipping_service
                LEFT JOIN `". DB_PREFIX . "order` od on od.order_id = oo.order_id_opencart

                WHERE ssm.shipment_number = '$shipment_number'
                group by ssm.id
                ;
            ";
        //query
        $query = $this->db->query($sql)->row;
        return (object)$query;
    }

    /**
     * ModelExtensionUpsmoduleShipments getTrackingInfo
     *
     * @param string $shipment_number //The shipment_number
     *
     * @return null
     */
    public function getTrackingInfo($shipment_number)
    {
        //sql
        $sql = "
            SELECT *
            FROM `". DB_PREFIX . "upsmodule_shipping_tracking` st
            WHERE st.shipment_number in ('{$shipment_number}')
            GROUP BY st.tracking_number
        ";
        //query
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments setLogShipment
     *
     * @param string $shipment_number //The shipment_number
     * @param string $server_url      //The server_url
     * @param string $server_response //The server_response
     *
     * @return null
     */
    public function setLogShipment($shipment_number, $server_url, $server_response)
    {
        //sql
        $sql = "
            INSERT INTO ". DB_PREFIX . "upsmodule_shipping_logs_api_mamage
                SET `shipment_number` = '" . $shipment_number . "',
                    `date_created` = now(),
                    `total` = `total`,
                    `server_url` = '$server_url',
                    `server_response` = '$server_response'
        ";
        //get db sql
        $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleShipments getTotalShipments
     *
     * @return null
     */
    public function getTotalShipments()
    {
        //query
        $query = $this->db->query("SELECT COUNT(1) AS total FROM " . DB_PREFIX . "upsmodule_shipping_tracking");
        return $query->row['total'];
    }

    /**
     * ModelExtensionUpsmoduleShipments updateStatus
     *
     * @param string $shipment_id //The shipmentId
     * @param string $status     //The Status
     *
     * @return null
     */
    public function updateStatus($shipment_id, $status)
    {
        //sql
        $sql = $this->_update . DB_PREFIX . "upsmodule_shipping_shipments
        SET `status`= '" . $status . "'
        WHERE `id`= '" . $shipment_id . $this->_special_drop;
        //query
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShipments getPackageStatus
     *
     * @param string $tracking_number //The trackingNumber
     *
     * @return $tracking_status
     */
    public function getPackageStatus($tracking_number)
    {
        //Load model
        $this->load->model($this->_base_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //LINK_API
        $this->load->model($constants->link_api_model);
        //license
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        //trackingResponse
        $tracking_response = $this->callUpsApi('Track', ['InquiryNumber' => $tracking_number], $license);
        //check trackingResponse
        if ($tracking_response && (!isset($tracking_response->Fault))) {
            $shipment_package_activity = new \stdClass();
            //check Package
            if (is_array($tracking_response->TrackResponse->Shipment->Package)) {
                //check item
                foreach ($tracking_response->TrackResponse->Shipment->Package as $item) {
                    //check trackingNumber
                    if ($item->TrackingNumber == $tracking_number) {
                        //shipmentPackageActivity
                        $shipment_package_activity = $item->Activity;
                        break;
                    }
                }
            } else {
                //shipmentPackageActivity
                $shipment_package_activity = $tracking_response->TrackResponse->Shipment->Package->Activity;
            }
            //trackingStatus
            $tracking_status = $this->getTrackingStatusCode($shipment_package_activity);
            //check trackingStatus
            if ($tracking_status != null) {
                //Description
                return $tracking_status->Description;
            }
        }

        return '';
    }

    /**
     * ModelExtensionUpsmoduleShipments getTrackingStatusCode
     *
     * @param string $shipment_package_activity //The shipmentPackageActivity
     *
     * @return $shipment_package_activity
     */
    public function getTrackingStatusCode($shipment_package_activity)
    {
        //check shipmentPackageActivity
        if (is_array($shipment_package_activity) && isset($shipment_package_activity)) {
            //keyRecentestDate
            $key_recentest_date = 0;
            //shipmentPackageActivityDateTime
            $shipment_package_activity_date_time = strtotime(
                $shipment_package_activity[0]->Date . ' ' . $shipment_package_activity[0]->Time
            );
            //check shipmentPackageActivity
            foreach ($shipment_package_activity as $key => $item) {
                //itemTime
                $item_time = strtotime($item->Date . ' ' . $item->Time);
                //check shipmentPackageActivityDateTime
                if ($shipment_package_activity_date_time < $item_time) {
                    //shipmentPackageActivityDateTime
                    $shipment_package_activity_date_time = $item_time;
                    //keyRecentestDate
                    $key_recentest_date = $key;
                }
            }
            //return Status
            return $shipment_package_activity[$key_recentest_date]->Status;
        } else {
            //return Status
            return $shipment_package_activity->Status;
        }
    }

    /**
     * ModelExtensionUpsmoduleShipments voidShipmentByShipmentNumber
     *
     * @param string $shipment_number //The shipmentNumber
     *
     * @return null
     */
    public function voidShipmentByShipmentNumber($shipment_number)
    {
        //Load model
        $this->load->model($this->_base_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //LINK_API
        $this->load->model($constants->link_api_model);
        //data
        $data = [];
        $data['ShipmentIdentificationNumber'] = $shipment_number;
        //license
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        //shipmentResponse
        $shipment_response = $this->callUpsApi('Void', $data, $license);
        $err_message = '';
        //check shipmentResponse
        if (!empty($shipment_response) && !isset($shipment_response->Fault)) {
            $shipment_status_code = $shipment_response->VoidShipmentResponse->Response->ResponseStatus->Code;
            //check shipmentStatusCode
            if ($shipment_status_code == '1') {
                //message
                return [$constants->check => true, $constants->message => ''];
            }
        } else {
            //check shipmentResponse
            if ($shipment_response
                && isset($shipment_response->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode->Code)
                && $shipment_response->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode->Code == '190117'
            ) {
                //message
                return [$constants->check => true, $constants->message => ''];
            } else {
                //Description
                $err_message = $shipment_response->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode->Description;
                //err_message
                return [$constants->check => false, $constants->message => $err_message];
            }
        }
        return [$constants->check => false, $constants->message => 'errors'];
    }

    /**
     * ModelExtensionUpsmoduleShipments callUpsApi
     *
     * @param string $method  //The method
     * @param string $data    //The data
     * @param string $license //The license
     *
     * @return $response
     */
    public function callUpsApi($method, $data, $license)
    {
        //Load model
        $this->load->model('extension/upsmodule/base');
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $commonInfo = $this->model_extension_upsmodule_base->getCommonInfo();
        include_once"$constants->link_api_ups";
        //get api
        $Api = new Ups();
        $Api->setCommonApiInfo($commonInfo);
        $response = null;
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($method);
        switch ($method) {
            case $constants->Track:
                $response = json_decode($Api->statusShipment($data, $license));
                break;
            case $constants->Void:
                $response = json_decode($Api->cancelShipment($data, $license));
                break;
            default:
                break;
        }
        $data_api = $Api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);
        return $response;
    }

    /**
     * ModelExtensionUpsmoduleShipments unlinkFile
     *
     * @param string $link //The link
     *
     * @return $response
     */
    public function unlinkFile($link)
    {
        //file_exists($link)
        if (file_exists($link)) {
            // unlink($link)
            unlink($link);
        }
    }

    /**
     * ModelExtensionUpsmoduleShipments exportCSVFile
     *
     * @param string $records //The records
     *
     * @return null
     */
    function exportCsvFile($records)
    {
        //fh
        $fh = fopen('php://output', 'w');
        //fprintf
        fprintf($fh, chr(0xEF) . chr(0xBB) . chr(0xBF));
        //heading
        $heading = false;
        //check records
        if (!empty($records)) {
            //row
            foreach ($records as $row) {
                //heading
                if (!$heading) {
                    //fwrite
                    fwrite($fh, implode(",", $row));
                    $heading = true;
                } else {
                    fwrite($fh, "\n" . implode(",", $row));
                }
            }
            fclose($fh);
        }
    }

    /**
     * ModelExtensionUpsmoduleShipments getListCountry
     *
     * @return $query
     */
    public function getListCountry()
    {
        //sql
        $sql ="
            SELECT `name`, `iso_code_2`
            FROM " . DB_PREFIX . "country";
            //query
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShipments checkDateShipments
     *
     * @return $query
     */
    public function checkDateShipments()
    {
        //sql
        $sql = "
            SELECT `id`, `create_date`
            FROM " . DB_PREFIX . "upsmodule_shipping_shipments";
            //query
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShipments checkDeleteDateShipments
     *
     * @param string $order_id //The order_id
     *
     * @return $query
     */
    public function checkDeleteDateShipments($order_id)
    {
        //sql
        $sql = "
            UPDATE " . DB_PREFIX . "upsmodule_open_orders
            SET `status` = 5
            WHERE `shipment_id` IN(" . $order_id . ")";
            //query
        $query = $this->db->query($sql);
    }
}
