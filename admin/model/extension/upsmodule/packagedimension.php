<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmodulePackagedimension file
 *
 * @category Packagedimension_Model
 */

class ModelExtensionUpsmodulePackagedimension extends Model
{
    private $_package_id = 'package_id';

    /**
     * ModelExtensionUpsmodulePackagedimension addPackageDimension
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $data //The data
     *
     * @return $query
     */
    public function addPackageDimension($data)
    {
        $sql = "INSERT INTO " . DB_PREFIX . "upsmodule_package_default
        SET package_name = '" . $this->db->escape($data['namePackage']) . "',
            weight = '" . (float)$data['weight'] . "',
            unit_weight = '" . $data['weightunit'] . "',
            length = '" .(float)$data['length'] . "',
            width = '" . (float)$data['width'] . "',
            height = '" . (float)$data['height'] . "',
            unit_dimension = '" . $this->db->escape($data['lengthunit']) . "'
            ";
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension addPackageDefault
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $list_package //The data
     *
     * @return $query
     */
    public function addPackageDefault($list_package)
    {
        $query = false;
        $valueArray = [];
        $package_name = 'Package_';
        $package_index = 1;
        foreach ($list_package as $package) {
            $package_name .= $package_index;
            $valueArray[] = "('" . $package_name . "', '"
                . $package['weight'] . "', '" . $package['unit_weight'] . "', '"
                . $package['length'] . "', '" . $package['width'] . "', '" . $package['height'] . "', '"
                . $this->db->escape($package['unit_dimension']) . "', '"
                . $this->db->escape($package['number_of_item']) . "')";
            $package_index ++;
        }
        if (count($valueArray) > 0) {
            $this->db->query("TRUNCATE " . DB_PREFIX . "upsmodule_package_default");
            $sql = "INSERT INTO " . DB_PREFIX . "upsmodule_package_default "
                . "(`package_name`, `weight`, `unit_weight`, `length`, `width`, `height`, `unit_dimension`, `package_item`) "
                . "VALUE ";
            $sql .= implode(', ', $valueArray);
            $sql .= ';';
            $query = $this->db->query($sql);
        }
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension addPackageDimension
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $list_package //The data
     *
     * @return $query
     */
    public function addProductDimension($list_package)
    {
        $query = false;
        $valueArray = [];
        foreach ($list_package as $package) {
            $valueArray[] = "('" . $this->db->escape($package['package_name']) . "', '"
                . $package['weight'] . "', '" . $package['unit_weight'] . "', '"
                . $package['length'] . "', '" . $package['width'] . "', '" . $package['height'] . "', '"
                . $this->db->escape($package['unit_dimension']) . "')";
        }
        if (count($valueArray) > 0) {
            $this->db->query("TRUNCATE " . DB_PREFIX . "upsmodule_product_dimension");
            $sql = "INSERT INTO " . DB_PREFIX . "upsmodule_product_dimension "
                . "(`package_name`, `weight`, `unit_weight`, `length`, `width`, `height`, `unit_dimension`) "
                . "VALUE ";
            $sql .= implode(', ', $valueArray);
            $sql .= ';';
            $query = $this->db->query($sql);
        }
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension addFallbackRate
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $list_fallback_rate //The data
     *
     * @return $query
     */
    public function addFallbackRate($list_fallback_rate)
    {
        $query = false;
        $valueArray = [];
        foreach ($list_fallback_rate as $fallback_rate) {
            $valueArray[] = "('ADD', '" . $fallback_rate['service_id'] . "', '" . $fallback_rate['rate'] . "')";
        }
        if (count($valueArray) > 0) {
            $this->db->query("TRUNCATE " . DB_PREFIX . "upsmodule_fallback_rates");
            $sql = "INSERT INTO " . DB_PREFIX . "upsmodule_fallback_rates (`service_type`, `service_id`, `fallback_rate`) VALUE ";
            $sql .= implode(', ', $valueArray);
            $sql .= ';';
            $query = $this->db->query($sql);
        }
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension listPackageDimension
     * <AUTHOR> <<EMAIL>>
     *
     * @return $query
     */
    public function listDefaultPackage()
    {
        $sql = "
            SELECT package_id, package_name, weight, unit_weight, length, width, height, unit_dimension, package_item
            FROM " . DB_PREFIX . "upsmodule_package_default oc_package_default
            ORDER BY package_id ASC
            ";
        $query = $this->db->query($sql);
        if (empty($query->rows)) {
            $tmpRow = [
                'package_id' => '',
                'package_name' => '',
                'weight' => '',
                'unit_weight' => '',
                'length' => '',
                'width' => '',
                'height' => '',
                'unit_dimension' => '',
                'package_item' => ''
            ];
            return [ $tmpRow ];
        }
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension listProductDimension
     * <AUTHOR> <<EMAIL>>
     *
     * @return $query
     */
    public function listProductDimension()
    {
        $sql = "
            SELECT package_id, package_name, weight, unit_weight, length, width, height, unit_dimension
            FROM " . DB_PREFIX . "upsmodule_product_dimension product_dimension
            ORDER BY package_id ASC
            ";
        $query = $this->db->query($sql);
        if (empty($query->rows)) {
            $tmpRow = [
                'package_id' => '',
                'package_name' => '',
                'weight' => '',
                'unit_weight' => '',
                'length' => '',
                'width' => '',
                'height' => '',
                'unit_dimension' => ''
            ];
            return [ $tmpRow ];
        }
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension listBackupRate
     * <AUTHOR> <<EMAIL>>
     *
     * @return $query
     */
    public function listBackupRate()
    {
        $sql = "
            SELECT service_id, fallback_rate
            FROM " . DB_PREFIX . "upsmodule_fallback_rates fallback_rates
            ORDER BY id ASC
            ";
        $query = $this->db->query($sql);
        if (empty($query->rows)) {
            $tmpRow = [
                'service_id' => '',
                'fallback_rate' => ''
            ];
            return [ $tmpRow ];
        }
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension listShippingService
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $country_code //The data
     *
     * @return $query
     */
    public function listShippingService($country_code)
    {
        $sql = "
            SELECT id, service_key, rate_code, service_name, service_symbol
            FROM " . DB_PREFIX . "upsmodule_shipping_services shipping_services
            WHERE country_code='" . $country_code . "'
            AND service_type='ADD' AND service_selected = '1'
            ORDER BY id ASC
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension getListPackageSelected
     * @author: UPS <<EMAIL>>
     *
     * @param int $id //The id
     *
     * @return $query
     */
    public function getListPackageSelected($id)
    {
        $sql = "
            SELECT * FROM " . DB_PREFIX . "upsmodule_package_default
            WHERE `package_id` = '" . $id ."' ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension deletePackage
     * <AUTHOR> <<EMAIL>>
     *
     * @param int $id //The id
     *
     * @return $query
     */
    public function deletePackage($id)
    {
        $sql = "DELETE FROM " . DB_PREFIX . "upsmodule_package_default WHERE package_id = '" . $id . "' ";
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension getPackage
     * <AUTHOR> <<EMAIL>>
     *
     * @param int $id //The id
     *
     * @return $query
     */
    public function getPackage($id)
    {
        $sql = "SELECT * FROM " . DB_PREFIX . "upsmodule_package_default WHERE package_id = '" . $id . "' ";
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension getPackageDefault
     * <AUTHOR> <<EMAIL>>
     *
     * @return $query
     */
    public function getPackageDefault()
    {
        $sql = "SELECT * FROM " . DB_PREFIX . "upsmodule_package_default limit 1";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension editPackage
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $data //The data
     *
     * @return $query
     */
    public function editPackage($data)
    {
        $sql = "UPDATE " . DB_PREFIX . "upsmodule_package_default
            SET `package_name`='". $this->db->escape($data['package_name']) ."',
                `weight`='". $data['weight'] ."',`unit_weight`='". $data['unit_weight'] ."',
                `length`='". $data['length'] ."',`width`='". $data['width'] ."',
                `height`='". $data['height'] ."',`unit_dimension`='". $data['unit_dimension']. "'
                WHERE `package_id`='". $data[$this->_package_id] ."'";
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension checkPackage
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $package_name //The package_name
     *
     * @return $query
     */
    public function checkPackage($package_name)
    {
        $sql = "SELECT `package_id`
                    FROM " . DB_PREFIX . "upsmodule_package_default
                    WHERE `package_name` = '" . $this->db->escape(trim($package_name))  . "'
                    ";
        $query = $this->db->query($sql)->row;
        $package_id = 0;
        if (!empty($query[$this->_package_id])) {
            $package_id = $query[$this->_package_id];
        }
        //Fist pack
        $sql2 = "SELECT `package_id`
                    FROM " . DB_PREFIX . "upsmodule_package_default
                    ORDER BY package_id asc;
                    ";
        $query2 = $this->db->query($sql2)->row;
        if (empty($query2[$this->_package_id])) {
            $checkPackage = false;
        } else {
            if ($query2[$this->_package_id] == $package_id) {
                $checkPackage = true;
            } else {
                $checkPackage = false;
            }
        }
        return $checkPackage;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension checkNameExits
     * <AUTHOR> <<EMAIL>>
     *
     * @param string $name_pkg   //The name_pkg
     * @param int    $package_id //The package_id
     *
     * @return $query
     */
    public function checkNameExits($name_pkg, $package_id = '')
    {
        if (empty($package_id)) {
            $sql = "SELECT `package_name` FROM " . DB_PREFIX . "upsmodule_package_default
            WHERE `package_name` = '" . $this->db->escape(trim($name_pkg)) . "'";
        } else {
            $sql = "SELECT `package_name`, `package_id` FROM " . DB_PREFIX . "upsmodule_package_default
            WHERE `package_name` = '" . $this->db->escape(trim($name_pkg))  . "' AND `package_id` <> '" .
            $package_id ."' ";
        }
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension countPkg
     * <AUTHOR> <<EMAIL>>
     *
     * @return $query
     */
    public function countPkg()
    {
        $sql = " SELECT COUNT(*) AS total FROM " . DB_PREFIX . "upsmodule_package_default";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension getListCheckAccessorial
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckAccessorial()
    {
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_menu_COD'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmodulePackagedimension updateCheckPackageDimension
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function updateCheckPackageDimension()
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_package_dimensions'"
        );
        $this->cache->delete('packagedimension');
    }

    // entry package to openorders

    public function setDefaultPackageDimension($listDefaultPackage, $numberOfCartItem)
    {
        // Set variable
        $this->listDefaultPackage = $listDefaultPackage;
        $this->numberOfCartItem = $numberOfCartItem;
        // Check empty array data
        if (empty($this->listDefaultPackage)) {
            return;
        }
        $packageIndex = 0;
        $numberOfItem = 1;
        // Set package index (get max setting number < cart number)
        foreach ($this->listDefaultPackage as $key => $defaultPackage) {
            if (isset($defaultPackage['package_item']) && $defaultPackage['package_item'] <= $numberOfCartItem && $numberOfItem <= $defaultPackage['package_item']) {
                $packageIndex = $key;
                $numberOfItem = $defaultPackage['package_item'];
            }
        }
        $countItem = count($this->listDefaultPackage);
        if (0 < $countItem && isset($this->listDefaultPackage[$countItem - 1]['package_item']) && $numberOfCartItem < $this->listDefaultPackage[$countItem - 1]['package_item']) {
            $packageIndex = $countItem - 1;
        }
        // Set package dimension
        $this->pkgLength = $this->listDefaultPackage[$packageIndex]['length'];
        $this->pkgWidth  = $this->listDefaultPackage[$packageIndex]['width'];
        $this->pkgHeight = $this->listDefaultPackage[$packageIndex]['height'];
        $this->pkgWeight = $this->listDefaultPackage[$packageIndex]['weight'];
        $this->description = 'Get default package dimension';
    }

    public function getPackageSettingType()
    {
        $sql = "SELECT * FROM " . DB_PREFIX . "upsmodule_setting WHERE `key` = 'ups_shipping_package_setting_type'";
        $query = $this->db->query($sql)->row;
        if (array_key_exists("value", $query)) {
            return $query['value'];
        }
        return "";
    }
    public function getListPackageByItems()
    {
        $sql = "SELECT * FROM " . DB_PREFIX . "upsmodule_package_default order by package_item desc";
        $query = $this->db->query($sql);
        return $query->rows;
    }
    public function getListDefaultPackage()
    {
        $sql = "SELECT * FROM " . DB_PREFIX . "upsmodule_package_default order by package_id asc";
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    //***************
}
