<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleCashondelivery file
 *
 * @category Cashondelivery_Model
 */

class ModelExtensionUpsmoduleCashondelivery extends Model
{
    /**
     * ModelExtensionUpsmoduleCashondelivery getListCheckShippingService
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckShippingService()
    {
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_menu_shipping_service'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleCashondelivery updateCheckCOD
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function updateCheckCod()
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_COD'"
        );
        $this->cache->delete('cashondelivery');
    }

    /**
     * ModelExtensionUpsmoduleCashondelivery updateCheckOption
     * UPS <<EMAIL>>
     *
     * @param string $value //The value
     *
     * @return null
     */
    public function updateCheckOption($value)
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = " . $value . "
            where `key` = 'ups_shipping_cod_option_active'"
        );
        $this->cache->delete('cashondelivery');
    }

    /**
     * ModelExtensionUpsmoduleCashondelivery getCheckOption
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getCheckOption()
    {
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_cod_option_active'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }
}
