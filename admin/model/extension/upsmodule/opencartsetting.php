<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleOpencartsetting file
 *
 * @category Opencartsetting_Model
 */

class ModelExtensionUpsmoduleOpencartsetting extends Model
{
    /**
     * ModelExtensionUpsmoduleCountry getCashOnDelivery
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getCashOnDelivery()
    {
        $query = $this->db->query(
            "SELECT  *
            FROM " . DB_PREFIX . "setting ocs
            WHERE ocs.key = 'payment_cod_status'"
        );
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getAdminLanguage
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getAdminLanguage()
    {
        $query = $this->db->query(
            "SELECT  *
            FROM " . DB_PREFIX . "setting ocs
            WHERE ocs.key = 'config_admin_language'"
        );
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getCountryCode
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getCountryCode()
    {
        $query = $this->db->query(
            "SELECT  `setting_id`, `key`, `value`
            FROM " . DB_PREFIX . "upsmodule_setting ocs
            WHERE ocs.key = 'ups_shipping_country_code'"
        );
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getTotalCountrys
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getTotalCountrys()
    {
        $sql = "
                    SELECT country_id, name, iso_code_2
                    FROM " . DB_PREFIX . "country oc_set
                    ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleCountry getCountryCodeById
     * @author: UPS <<EMAIL>>
     *
     * @param int $id //The id
     *
     * @return $query
     */
    public function getCountryCodeById($id)
    {
        $sql = "
                    SELECT iso_code_2
                    FROM " . DB_PREFIX . "country oc_set
                    WHERE oc_set.country_id = '" . $id . "'";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getCountryByCountryCode
     * @author: UPS <<EMAIL>>
     *
     * @param string $country_code //The country_code
     *
     * @return $query
     */
    public function getCountryByCountryCode($country_code)
    {
        $sql = "
                    SELECT name, iso_code_2
                    FROM " . DB_PREFIX . "country oc_set
                    WHERE oc_set.iso_code_2 = '" . $country_code . "'";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getStateByZoneId
     * @author: UPS <<EMAIL>>
     *
     * @param int $zone_id //The zone_id
     *
     * @return $query
     */
    public function getStateByZoneId($zone_id)
    {
        $sql = "
                    SELECT *
                    FROM " . DB_PREFIX . "zone ocz
                    WHERE ocz.zone_id = '" . $zone_id . "'";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getStateByStateCode
     * @author: UPS <<EMAIL>>
     *
     * @param string $state_code   //The state_code
     * @param string $country_code //The country_code
     *
     * @return $query
     */
    public function getStateByStateCode($state_code, $country_code)
    {
        $sql = "
                    SELECT ocz.*
                    FROM " . DB_PREFIX . "zone ocz
                    LEFT JOIN " . DB_PREFIX . "country AS occ on ocz.`country_id` = occ.`country_id`
                    WHERE ocz.code = '" . $state_code . "'
                    AND occ.iso_code_2 = '" . $country_code . "'";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getCodeZone
     * @author: UPS <<EMAIL>>
     *
     * @param int $id //The id
     *
     * @return $query
     */
    public function getCodeZone($id)
    {
        $sql = "
                    SELECT `code`
                    FROM " . DB_PREFIX . "zone ocs
                    WHERE ocs.zone_id = '" . $id . "'";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry updateOrderOpencartStatus
     * @author: UPS <<EMAIL>>
     *
     * @param int $order_id  //The order_id
     * @param int $status_id //The status_id
     *
     * @return $query
     */
    public function updateOrderOpencartStatus($order_id, $status_id)
    {
        $sql = "
                UPDATE " . DB_PREFIX . "order
                SET `order_status_id` = '". $status_id ."', `date_modified` = now()
                WHERE `order_id`='". $order_id ."'";
        $query = $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleCountry addOrderHistoryOpencart
     * @author: UPS <<EMAIL>>
     *
     * @param int $order_id        //The order_id
     * @param int $order_status_id //The order_status_id
     *
     * @return null
     */
    public function addOrderHistoryOpencart($order_id, $order_status_id)
    {
        $sql = "
                INSERT INTO " . DB_PREFIX . "order_history
                SET `order_id`='". $order_id ."',
                    `order_status_id` = '". $order_status_id ."',
                    `date_added` = now()";
        $query = $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleCountry getState
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getState()
    {
        $sql = "
                    SELECT *
                    FROM " . DB_PREFIX . "zone ocs";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleCountry getAccountDefault
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getAccountDefault()
    {
        $query = $this->db->query(
            "SELECT  `key`, `value`
            FROM " . DB_PREFIX . "upsmodule_setting ocs
            WHERE ocs.key = 'ups_shipping_choose_account_number_ap'
            OR ocs.key = 'ups_shipping_choose_account_number_add'"
        );
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleCountry getCutOffTime
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getCutOffTime()
    {
        $query = $this->db->query(
            "SELECT  `key`, `value`
            FROM " . DB_PREFIX . "upsmodule_setting ocs
            WHERE ocs.key = 'ups_shipping_cut_off_time'"
        );
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getTime
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getTime()
    {
        $query = $this->db->query(
            "SELECT now() as date"
        );
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getTime
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function updateAdminLanguage($language_code)
    {
        $this->db->query("
            UPDATE " . DB_PREFIX . "setting ocs
            SET ocs.value = '{$language_code}'
            WHERE ocs.key = 'config_admin_language'");
    }

    /**
     * ModelExtensionUpsmoduleCountry getTime
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function setEnglishToDefaultLanguage($page, $link)
    {
        //setup default language to english
        $this->load->model("extension/upsmodule/country");
        $get_admin_language = (object) $this->getAdminLanguage();
        $country_code = (object) $this->model_extension_upsmodule_country->getCountryCode();
        if (strtolower($country_code->value) == "pl") {
            if ($get_admin_language->value != 'en-gb' && $get_admin_language->value != 'pl-PL') {
                //set to en-gb
                $this->updateAdminLanguage("en-gb");
                $this->cache->delete($page);
                if ($page != 'country') {
                    $this->response->redirect(
                        $this->url->link($link, 'user_token=' . $this->session->data['user_token'], true)
                    );
                }
            }
        }
        if (strtolower($country_code->value) == "us") {
            if ($get_admin_language->value != 'en-gb' && $get_admin_language->value != 'en-US') {
                //set to en-gb
                $this->updateAdminLanguage("en-gb");
                $this->cache->delete($page);
                if ($page != 'country') {
                    $this->response->redirect(
                        $this->url->link($link, 'user_token=' . $this->session->data['user_token'], true)
                    );
                }
            }
        }
    }
}
