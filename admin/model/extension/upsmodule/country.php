<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleCountry file
 *
 * @category Country_Model
 */

class ModelExtensionUpsmoduleCountry extends Model
{
    /**
     * ModelExtensionUpsmoduleCountry getTotalCountrys
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getTotalCountrys()
    {
        $sql = "
            SELECT country_id, name, trim(iso_code_2) iso_code_2
            FROM " . DB_PREFIX . "country oc_set
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleBase getListCountry
     * @author: UPS <<EMAIL>>
     *
     * @return $object
     */
    public function getListCountry()
    {
        $this->load->language("extension/upsmodule/translate");
        $country = new \stdClass();
        $country->AT = $this->language->get("text_country_austria");
        $country->BE = $this->language->get("text_country_belgium");
        // $country->BG = $this->language->get("text_country_bulgaria");
        // $country->HR = $this->language->get("text_country_croatia");
        // $country->CY = $this->language->get("text_country_cyprus");
        $country->CZ = $this->language->get("text_country_czechrepublic");
        $country->DK = $this->language->get("text_country_denmark");
        // $country->EE = $this->language->get("text_country_estonia");
        $country->FI = $this->language->get("text_country_finland");
        $country->FR = $this->language->get("text_country_france");
        $country->DE = $this->language->get("text_country_germany");
        $country->GR = $this->language->get("text_country_greece");
        $country->HU = $this->language->get("text_country_hungary");
        $country->IS = $this->language->get("text_country_iceland");
        $country->IE = $this->language->get("text_country_ireland");
        $country->IT = $this->language->get("text_country_italy");
        $country->JE = $this->language->get("text_country_jersey");
        // $country->LV = $this->language->get("text_country_latvia");
        // $country->LT = $this->language->get("text_country_lithuania");
        $country->LU = $this->language->get("text_country_luxembourg");
        // $country->MT = $this->language->get("text_country_malta");
        $country->NL = $this->language->get("text_country_netherlands");
        $country->NO = $this->language->get("text_country_norway");
        $country->PL = $this->language->get("text_country_poland");
        $country->PT = $this->language->get("text_country_portugal");
        $country->RO = $this->language->get("text_country_romania");
        // $country->SK = $this->language->get("text_country_slovakia");
        $country->SI = $this->language->get("text_country_slovenia");
        $country->ES = $this->language->get("text_country_spain");
        $country->SE = $this->language->get("text_country_sweden");
        $country->GB = $this->language->get("text_country_united_kingdom");
        $country->CH = $this->language->get("text_country_switzerland");
        // $country->RS = $this->language->get("text_country_serbia");
        $country->TR = $this->language->get("text_country_turkey");
        
        return (array) $country;
    }

    /**
     * ModelExtensionUpsmoduleCountry updateCountry
     * @author: UPS <<EMAIL>>
     *
     * @param string $value //The value
     *
     * @return $query
     */
    public function updateCountry($value)
    {
        $sql = "
            UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '" . trim($value) . "'
            WHERE `key` = 'ups_shipping_country_code'";
        $query = $this->db->query($sql);
        $sql = "
            UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = 1
            WHERE `key` = 'ups_shipping_menu_country'";
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleCountry getListStateByCountryCode
     * @author: UPS <<EMAIL>>
     *
     * @param string $country_code //The Country Code
     *
     * @return $list_state
     */
    public function getListStateByCountryCode($country_code)
    {
        $sql = "
            SELECT ocz.code, ocz.name FROM " . DB_PREFIX . "zone ocz
            LEFT JOIN " . DB_PREFIX . "country occ
            ON ocz.country_id = occ.country_id
            WHERE occ.iso_code_2 = '" . trim($country_code) . "'";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleCountry getStateName
     * @author: UPS <<EMAIL>>
     *
     * @param string $state_code //The State Code
     *
     * @return $state_name
     */
    public function getStateName($state_code, $country_code)
    {
        $sql = "
            SELECT ocz.name FROM " . DB_PREFIX . "zone ocz
            LEFT JOIN " . DB_PREFIX . "country occ
            ON ocz.country_id = occ.country_id
            WHERE ocz.code = '" . trim($state_code) . "'
            AND occ.iso_code_2 = '" . trim($country_code) . "'";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry getCountryCode
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getCountryCode()
    {
        $query = $this->db->query("
            SELECT  `setting_id`, `key`, `value`
            FROM " . DB_PREFIX . "upsmodule_setting ocs
            WHERE ocs.key = 'ups_shipping_country_code'");
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleCountry checkValueCountry
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function checkValueCountry()
    {
        $sql = "
            SELECT `value`
            FROM " . DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_accept_term_condition'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleCountry getStateCountryName
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getStateCountryName()
    {
        $sql = "
            SELECT occ.iso_code_2 as countryCode, ocz.code as stateCode, ocz.name as StateName
            FROM `" . DB_PREFIX . "country` AS occ
            Join " . DB_PREFIX . "zone AS ocz ON occ.country_id = ocz.country_id
            ORDER BY `iso_code_2` DESC
                ";
        $query = $this->db->query($sql);
        return $query->rows;
    }
}
