<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmodulePluginmanage file
 *
 * @category Pluginmanage_Model
 */

class ModelExtensionUpsmodulePluginmanage extends Model
{
    protected $date_format_ymd_time = "Y-m-d H:i:s";
    private $_table_name = "upsmodule_shipping_logs_api_mamage";
    public $pm_api_remove_account = "ups_shipping_update_merchant_status_remove_account";
    public $pm_api_add_account = "ups_shipping_transfer_merchant_info_by_user";
    public $pm_api_merchant_info = "ups_shipping_transfer_merchant_info";
    public $pm_api_accessorial = "ups_shipping_transfer_accessorial";
    public $pm_api_shipping_service = "ups_shipping_transfer_shipping_service";
    public $pm_api_delivery_rate = "ups_shipping_transfer_delivery_rate";
    public $pm_api_default_package = "ups_shipping_transfer_default_package";
    public $pm_api_created_shipment = "ups_shipping_transfer_shipment";
    public $pm_api_update_shipment_status = "ups_shipping_update_shipment_status";
    public $pm_api_activated_plugin = "ups_shipping_activated_plugin";
    public $pm_api_deactivated_plugin = "ups_shipping_deactivated_plugin";
    private $_api_manage = '../system/library/upsmodule/API/Manage.php';
    private $_base_model = 'extension/upsmodule/base';

    /**
     * ModelExtensionUpsmodulePluginmanage saveLogRetry
     * @author: UPS <<EMAIL>>
     *
     * @param array $data_array //The data_array
     * @param string $key_id //The Key ID
     *
     * @return bool
     */
    public function saveLogRetry($data_array, $key_id = "id")
    {
        $tmp_fields = [];
        $tmp_values = [];
        $tmp_duplicate = [];
        foreach ($data_array as $key => $value) {
            if ($key_id != $key) {
                $value_format = $this->db->escape($value);
                $tmp_duplicate[] = "`{$key}`='{$value_format}'";
                $tmp_values[] = "'{$value_format}'";
            } else {
                $check_id = intval($value);
                if (empty($check_id)) {
                    $check_id = "NULL";
                }
                $tmp_values[] = "{$check_id}";
            }
            $tmp_fields[] = "`{$key}`";
        }
        $strFields = implode(",", $tmp_fields);
        $strValues = implode(",", $tmp_values);
        $str_duplicate = implode(",", $tmp_duplicate);
        $sql = "
            INSERT INTO " . DB_PREFIX . $this->_table_name .
            "({$strFields}) VALUES ({$strValues})" .
            " ON DUPLICATE KEY UPDATE {$str_duplicate}";
        try {
            $this->db->query($sql);
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * ModelExtensionUpsmodulePluginmanage deleteLogRetry
     * @author: UPS <<EMAIL>>
     *
     * @param string $id //The id
     * @param string $key_id //The key id
     *
     * @return none
     */
    public function deleteLogRetry($id, $key_id = "id")
    {
        if (empty($id) || intval($id) <= 0) {
            return false;
        }

        try {
            $id = intval($id);
            $sql = "
            DELETE FROM " . DB_PREFIX . $this->_table_name . "
            WHERE `{$key_id}`='{$id}'";
            $this->db->query($sql);
            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * ModelExtensionUpsmodulePluginmanage updateLogRetry
     * @author: UPS <<EMAIL>>
     *
     * @param array $data_array //The data_array
     * @param array $conditions //The list conditions
     *
     * @return none
     */
    public function updateLogRetry($id)
    {
        $sql = "
            UPDATE " . DB_PREFIX . $this->_table_name . "
            SET count_retry = count_retry + 1
            WHERE `id`={$id}";

        try {
            return $this->db->query($sql);
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * ModelExtensionUpsmodulePluginmanage getListMethodCallApi
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListMethodCallApi()
    {
        $sql = "
            SELECT *
            FROM ". DB_PREFIX . $this->_table_name . "
            WHERE `count_retry` <= 5
        ";
        $query = $this->db->query($sql)->rows;
        return $query;
    }

    /**
     * ModelExtensionUpsmodulePluginmanage checkApiManager
     * @author: UPS <<EMAIL>>
     *
     * @param string $response    //The response
     * @param string $id          //The id
     *
     * @return $query
     */
    public function checkApiManager($response, $id = 0)
    {
        if (isset($response->data) && $response->data === true) {
            $this->deleteLogRetry($id);
        } else {
            if (isset($response->error->errorCode) && intval($response->error->errorCode) === 401) {
                $this->load->model("extension/upsmodule/base");
                $this->load->model("extension/upsmodule/apiModel");
                $license = (object) $this->model_extension_upsmodule_apiModel->getLicense();
                $this->model_extension_upsmodule_base->doRegisteredPluginToken($license);
            } else {
                $this->updateCountRetry($id);
            }
        }
    }

    /**
     * ModelExtensionUpsmodulePluginmanage updateLogFailManager
     *
     * @param string $id //The id
     *
     * @return null
     */
    private function updateCountRetry($id)
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . $this->_table_name .
            "SET `count_retry` = `count_retry` + 1
                WHERE `id` = '{$id}'"
        );
    }

    private function loadApiManageResource()
    {
        // Common api info
        $this->load->model($this->_base_model);
        $commonApiInfo = $this->model_extension_upsmodule_base->getCommonInfo();
        // Include manage api
        include_once"$this->_api_manage";
        $apiManage = new Manage();
        // Set common api info
        $apiManage->setCommonApiInfo($commonApiInfo);
        return $apiManage;
    }

    /**
     * ControllerExtensionUpsmodulePluginmanager updateMerchantStatus
     * @param object $api //api manage
     * @param string $account_number //account number
     * @param int $status acount status
     * @param string $retry_id //log manage id
     *
     * @return null
     */
    private function doUpdateMerchantStatus($api, $account_number, $status, $count = 1)
    {
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        if (!empty($account_number)) {
            $data->account_number = $account_number;
        }
        $data->status = $status;
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->UpdateMerchantStatus);
        try {
            $response = $api->updateMerchantStatus($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);

        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->doUpdateMerchantStatus($api, $account_number, $status, $count);
        }
        return $response;
    }

    /**
     * ControllerExtensionUpsmodulePluginmanager transferMerchantInfoByUser
     * @param object $api //api manage
     * @param string $account_number //account number
     * @param string $retry_id //log manage id
     *
     * @return null
     */
    private function doTransferMerchantInfoByUser($api, $account_number, $count = 1)
    {
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        $account_list = $this->model_extension_upsmodule_base->getAccountList($account_number);
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        $data->account_list = $account_list;
        $data->package = $this->model_extension_upsmodule_base->getPackageTransferMerchantInfo();
        $data->version = $constants->txt_extension_version;
        $data->joining_date = date("m/d/Y");
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->TransferMerchantInfoByUsser);
        try {
            $response = $api->transferMerchantInfoByUsser($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);

        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->doTransferMerchantInfoByUser($api, $account_number, $count);
        }
        return $response;
    }

    /**
     * ControllerExtensionUpsmodulePluginmanager transferMerchantInfo
     * @param object $api //api manage
     * @param int $option //account number
     * @param int $retry_id //log manage id
     *
     * @return null
     */
    private function doTransferMerchantInfo($api, $option, $count = 1)
    {
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        $account_list = null;
        if ($option == 1) {
            $account_list = $this->model_extension_upsmodule_base->getAccountList('', 1);
        } else {
            $account_list = $this->model_extension_upsmodule_base->getAccountList('', 0);
        }
        $data->account_list = $account_list;
        $data->service = $this->model_extension_upsmodule_base->getServiceList();
        $data->delivery_service = $this->model_extension_upsmodule_base->getDeliveryService();
        $data->package = $this->model_extension_upsmodule_base->getPackageTransferMerchantInfo();
        $data->accessorials = [];
        $data->version = $constants->txt_extension_version;
        $data->joining_date = date("m/d/Y");
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->TransferMerchantInfo);
        try {
            $response = $api->transferMerchantInfo($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);

        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->doTransferMerchantInfo($api, $option, $count);
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmodulePluginmanage updateMerchantStatusRemoveAccount
     * @author: UPS <<EMAIL>>
     *
     * @param string $account_number //The account_number
     *
     * @return null
     */
    public function updateMerchantStatusRemoveAccount($account_number)
    {
        $api = $this->loadApiManageResource();
        $this->doUpdateMerchantStatus($api, $account_number, 20);
    }

    /**
     * ModelExtensionUpsmodulePluginmanage transferMerchantInfoByUser
     * @author: UPS <<EMAIL>>
     *
     * @param string $account_number //The account_number
     *
     * @return null
     */
    public function transferMerchantInfoByUser($account_number)
    {
        $api = $this->loadApiManageResource();
        $this->doTransferMerchantInfoByUser($api, $account_number);
    }

    /**
     * ModelExtensionUpsmodulePluginmanage transferMerchantInfo
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function transferMerchantInfo()
    {
        $api = $this->loadApiManageResource();
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->language("extension/upsmodule/translate");
        $version = $constants->txt_extension_version;
        $response = $this->doTransferMerchantInfo($api, 1);
        if ($response) {
            if (!isset($response->error)) {
                $this->doTransferMerchantInfo($api, 0);
                $this->model_extension_upsmodule_base->saveOptionSetting("ups_shipping_check_manage", 1);
                $this->model_extension_upsmodule_base->saveOptionSetting("ups_shipping_version_plugin", $version);
            }
        }
    }

    /**
     * ModelExtensionUpsmodulePluginmanage transferAccessorials
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function transferAccessorials($count = 1)
    {
        $api = $this->loadApiManageResource();
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        $data->accessorials = $this->model_extension_upsmodule_base->getAccessorial();
        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->TransferAccessorials);
        try {
            $response = $api->transferAccessorials($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);

        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->transferShippingService($count);
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmodulePluginmanage transferAccessorials
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function transferShippingService($count = 1)
    {
        $api = $this->loadApiManageResource();
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        $data->delivery_service = $this->model_extension_upsmodule_base->getServiceList();
        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->TransferShippingServices);
        try {
            $response = $api->transferShippingServices($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);
        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->transferShippingService($count);
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmodulePluginmanage transferDeliveryRates
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function transferDeliveryRates($count = 1)
    {
        $api = $this->loadApiManageResource();
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        $data->delivery_service = $this->model_extension_upsmodule_base->getDeliveryService();
        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->TransferDeliveryRates);
        try {
            $response = $api->transferDeliveryRates($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);
        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->transferDeliveryRates($count);
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmodulePluginmanage transferDefaultPackage
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function transferDefaultPackage($count = 1)
    {
        $api = $this->loadApiManageResource();
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //create request data
        $merchantKey = $this->getMerchantKey();
        $packageSetting = $this->model_extension_upsmodule_base->getPackageSetting($merchantKey);

        $token = $this->getRegisteredPluginToken();
        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->TransferDefaultPackage);
        try {
            $requestParam = $packageSetting->packageDimesion;
            if ($packageSetting->packageSettingType == 1) {
                $response = $api->transferDefaultPackage($requestParam, $token);
            } elseif ($packageSetting->packageSettingType == 2) {
                $response = $api->transferDefaultPackageRate($requestParam, $token);
            }
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);

        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->transferDefaultPackage($count);
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmodulePluginmanage transferShipment
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function transferShipment(
        $shipment_info = false,
        $account_number = '',
        $accessorial = false,
        $package = false,
        $count = 1
    ) {
        $api = $this->loadApiManageResource();
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        $data->account_number = $account_number;
        $shipment = null;
        if (is_array($shipment_info)) {
            $shipment = (object) $shipment_info;
        } else {
            $shipment = $shipment_info;
        }
        $data->shipment_id = $shipment->shipment_number;
        $data->fee = $shipment->shipping_fee;
        $data->revenue = $shipment->shipping_fee;
        $address_list = [
            $shipment->address1,
            $shipment->address2,
            $shipment->address3
        ];
        $address_list = array_filter($address_list);
        $data->address = implode(', ', $address_list);
        $data->postal_code = $shipment->postcode;
        $data->city = $shipment->city;
        $data->country = $shipment->country;
        $data->service_type = $shipment->service_type;
        $data->service_code = $shipment->rate_code;
        $data->service_name = $shipment->service_name;
        $data->is_cash_on_delivery = (bool) $shipment->cod;
        $data->products = explode(',', $shipment->produc_name);
        //accessorials
        $data->accessorials = $accessorial;

        $data->order_date = $shipment->date_added;
        $data->status = $shipment->status;

        //package
        $data->packages = $package;

        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->TransferShipments);
        try {
            $response = $api->transferShipments($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);

        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->transferShipment(
                $shipment_info,
                $account_number,
                $accessorial,
                $package,
                $count
            );
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmodulePluginmanage updateShipmentStatus
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function updateShipmentStatus($shipments, $count = 1)
    {
        $api = $this->loadApiManageResource();
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        $data->shipment = $shipments;
        //save log before call api
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->UpdateShipmentStatus);
        try {
            $response = $api->updateShipmentsStatus($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);

        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->updateShipmentStatus($shipments, $count);
        }
        return $response;
    }

    /**
     * ModelExtensionUpsmodulePluginmanage transferShipment
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function activatedPlugin()
    {
        $api = $this->loadApiManageResource();
        $account_number = "";
        $this->doUpdateMerchantStatus($api, $account_number, 10);
    }

    /**
     * ModelExtensionUpsmodulePluginmanage deactivatedPlugin
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function deactivatedPlugin()
    {
        $api = $this->loadApiManageResource();
        $account_number = "";
        $this->doUpdateMerchantStatus($api, $account_number, 20);
    }

    /**
     * ModelExtensionUpsmodulePluginmanage deactivatedPlugin
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function upgradePluginVersion()
    {
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        $api = $this->loadApiManageResource();
        $version = $constants->txt_extension_version;
        $this->doUpgradePluginVersion($api, $version);
    }

    /**
     * ControllerExtensionUpsmodulePluginmanager updateMerchantStatus
     * @param object $api //api manage
     * @param string $account_number //account number
     * @param int $status acount status
     * @param string $retry_id //log manage id
     *
     * @return null
     */
    private function doUpgradePluginVersion($api, $version, $count = 1)
    {
        //load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //create request data
        $data = new \stdClass();
        $data->merchant_key = $this->getMerchantKey();
        $data->version = $version;
        $data->upsmodule_token = $this->getRegisteredPluginToken();
        //save log before call api

        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($constants->UpgradePluginVersion);
        try {
            $response = $api->upgradePluginVersion($data);
        } catch (Exception $ex) {
            $response = false;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);

        //check expired token key.
        if (isset($response->error->errorCode) && intval($response->error->errorCode) == 401 && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getNewRegisteredPluginToken();
            //recall api.
            $response = $this->doUpgradePluginVersion($api, $version, $count);
        }
        return $response;
    }

    /**
     * ControllerExtensionUpsmodulePluginmanager getMerchantKey
     *
     * @return null
     */
    private function getMerchantKey()
    {
        //load model
        $this->load->model($this->_base_model);
        $merchant_key = $this->model_extension_upsmodule_base->getSettingByKey('ups_shipping_merchant_key');
        return $merchant_key;
    }

    /**
     * ControllerExtensionUpsmodulePluginmanager getMerchantKey
     *
     * @return null
     */
    private function getCountryCode()
    {
        //load model
        $this->load->model($this->_base_model);
        $countryCode = $this->model_extension_upsmodule_base->getSettingByKey('ups_shipping_country_code');
        return $countryCode;
    }

    /**
     * ControllerExtensionUpsmodulePluginmanager getRegisteredPluginToken
     *
     * @return null
     */
    private function getRegisteredPluginToken()
    {
        //load model
        $this->load->model($this->_base_model);
        $upsmodule_token = $this->model_extension_upsmodule_base->getSettingByKey('ups_shipping_registered_plugin_token');
        return $upsmodule_token;
    }
}
