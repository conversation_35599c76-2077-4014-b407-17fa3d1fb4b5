<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleAbout file
 *
 * @category About_Model
 */

class ModelExtensionUpsmoduleAbout extends Model
{
    /**
     * ModelExtensionUpsmoduleAbout getListLogApi
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListLogApi($data)
    {
        $sql = "
            SELECT *
            FROM " .DB_PREFIX . "upsmodule_shipping_logs_api
            ORDER BY `" . $data->sort . "` " . $data->order . "
            LIMIT " . $data->start . ", " . $data->limit;
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleAbout getListLogApiById
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListLogApiById($idLogApi)
    {
        $sql = "
            SELECT *
            FROM " .DB_PREFIX . "upsmodule_shipping_logs_api
            WHERE id = '{$idLogApi}'
            ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleAbout getTotalLog
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getTotalLog()
    {
        $query = $this->db->query(
            "SELECT COUNT(1) AS total
            FROM " . DB_PREFIX . "upsmodule_shipping_logs_api"
        );
        return $query->row['total'];
    }
}
