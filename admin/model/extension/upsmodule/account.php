<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleAccount file
 *
 * @category Account_Model
 */

class ModelExtensionUpsmoduleAccount extends Model
{
    private $_title = 'title';
    private $_customer_name = 'customer_name';
    private $_company = 'company';
    private $_email = 'email';
    private $_phone_number = 'phone_number';
    private $_address_type = 'address_type';
    private $_address_1 = 'address_1';
    private $_address_2 = 'address_2';
    private $_address_3 = 'address_3';
    private $_post_code = 'post_code';
    private $_city = 'city';
    private $_state_province_code = 'state_province_code';
    private $_country_code = 'country_code';
    private $_account_type = 'account_type';
    private $_ups_account_name = 'ups_account_name';
    private $_ups_account_number = 'ups_account_number';
    private $_ups_invoice_number = 'ups_invoice_number';
    private $_ups_invoice_amount = 'ups_invoice_amount';
    private $_ups_account_vatnumber = 'ups_account_vatnumber';
    private $_ups_account_promocode = 'ups_account_promocode';
    private $_ups_currency = 'ups_currency';
    private $_ups_invoice_date = 'ups_invoice_date';
    private $_account_default = 'account_default';
    private $_device_identity = 'device_identity';
    private $_business_name = 'business_name';
    private $_select_from = '
        SELECT * FROM ';

    /**
     * ModelExtensionUpsmoduleAccount saveAccount
     *
     * @param string $data //The data
     *
     * @return null
     */
    public function saveAccount($data)
    {
        //check empty _ups_account_name
        if (empty($data[$this->_ups_account_name])) {
            $data[$this->_ups_account_name] = null;
        }
        //check empty _ups_account_number
        if (empty($data[$this->_ups_account_number])) {
            $data[$this->_ups_account_number] = null;
        }
        //check empty _ups_invoice_number
        if (empty($data[$this->_ups_invoice_number])) {
            $data[$this->_ups_invoice_number] = null;
        }
        //check empty _ups_invoice_amount
        if (empty($data[$this->_ups_invoice_amount])) {
            $data[$this->_ups_invoice_amount] = null;
        }
        //check empty _ups_currency
        if (empty($data[$this->_ups_currency])) {
            $data[$this->_ups_currency] = null;
        }
        //check empty _ups_invoice_date
        if (empty($data[$this->_ups_invoice_date])) {
            $data[$this->_ups_invoice_date] = null;
        }
        //check empty _ups_account_vatnumber
        if (empty($data[$this->_ups_account_vatnumber])) {
            $data[$this->_ups_account_vatnumber] = null;
        }
        //check empty _ups_account_promocode
        if (empty($data[$this->_ups_account_promocode])) {
            $data[$this->_ups_account_promocode] = null;
        }
        //check empty _account_default
        if (empty($data[$this->_account_default])) {
            $data[$this->_account_default] = null;
        }
        //check empty _device_identity
        if (empty($data[$this->_device_identity])) {
            $data[$this->_device_identity] = null;
        }
        //check empty _business_name
        if (empty($data[$this->_business_name])) {
            $data[$this->_business_name] = null;
        }
        //check empty statecode
        if (empty($data[$this->_state_province_code])) {
            $data[$this->_state_province_code] = null;
        }
        //INSERT INTO oc_upsmodule_account
        $sql = "
            INSERT INTO ". DB_PREFIX . "upsmodule_account
            SET title = '". $this->db->escape($data[$this->_title]) ."',
                fullname = '". $this->db->escape($data[$this->_customer_name]) ."',
                company = '". $this->db->escape($data[$this->_company]) ."',
                email = '". $this->db->escape($data[$this->_email]) ."',
                phone_number = '". $this->db->escape($data[$this->_phone_number]) ."',
                address_type = '". $this->db->escape($data[$this->_address_type]) ."',
                address_1 = '". $this->db->escape($data[$this->_address_1]) ."',
                address_2 = '". $this->db->escape($data[$this->_address_2]) ."',
                address_3 = '". $this->db->escape($data[$this->_address_3]) ."',
                post_code = '". $this->db->escape($data[$this->_post_code]) ."',
                city = '". $this->db->escape($data[$this->_city]) ."',
                state_province_code = '". $this->db->escape($data[$this->_state_province_code]) ."',
                country = '". $this->db->escape($data[$this->_country_code]) ."',
                account_type = '". $this->db->escape($data[$this->_account_type]) ."',
                ups_account_name = '". $this->db->escape($data[$this->_ups_account_name]) ."',
                ups_account_number = '". $this->db->escape($data[$this->_ups_account_number]) ."',
                ups_invoice_number = '". $this->db->escape($data[$this->_ups_invoice_number]) ."',
                ups_invoice_amount = '". $this->db->escape($data[$this->_ups_invoice_amount]) ."',
                ups_currency = '". $this->db->escape($data[$this->_ups_currency]) ."',
                ups_invoice_date = '". $this->db->escape($data[$this->_ups_invoice_date]) ."',
                ups_account_vatnumber = '". $this->db->escape($data[$this->_ups_account_vatnumber]) ."',
                ups_account_promocode = '". $this->db->escape($data[$this->_ups_account_promocode]) ."',
                device_identity = '" . $this->db->escape($data[$this->_device_identity])  . "',
                account_default = '". $this->db->escape($data[$this->_account_default]) ."',
                business_name = '". $this->db->escape($data[$this->_business_name]) ."'";
        $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleAccount getListAccount
     *
     * @return $query
     */
    public function getListAccount()
    {
        //getListAccount
        $sql = "" .
            $this->_select_from . "" . DB_PREFIX . "upsmodule_account";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleAccount getAccountDefault
     *
     * @return $query
     */
    public function getAccountDefault()
    {
        //getAccountDefault
        $sql = "" .
            $this->_select_from . "" . DB_PREFIX . "upsmodule_account acc
            WHERE acc.account_default = 1";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleAccount deleteAccount
     *
     * @param string $account_id //The account_id
     *
     * @return $query
     */
    public function deleteAccount($account_id)
    {
        //deleteAccount
        $sql = "
            DELETE FROM " . DB_PREFIX . "upsmodule_account
            WHERE account_id = " . $account_id;
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleAccount checkAccount
     *
     * @param string $ups_account_number //The ups_account_number
     *
     * @return $query
     */
    public function checkAccount($ups_account_number)
    {
        //checkAccount
        $sql = "" .
            $this->_select_from . "" . DB_PREFIX . "upsmodule_account acc
            WHERE acc.ups_account_number = '" . $ups_account_number . "'";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleAccount getInfoAccount
     *
     * @param int $id //The id
     *
     * @return $query
     */
    public function getInfoAccount($id)
    {
        //getInfoAccount
        $sql = "" .
            $this->_select_from . "" . DB_PREFIX . "upsmodule_account acc
            WHERE acc.account_id = " . $id;
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleAccount getListCheckTermcondition
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckTermcondition()
    {
        //getListCheckTermcondition
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_menu_term_condition'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleAccount updateCheckAccount
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function updateCheckAccount()
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_account'"
        );
        $this->cache->delete('account');
    }

    /**
     * ModelExtensionUpsmoduleAccount getLanguageCountry
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getLanguageCountry()
    {
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "setting
            WHERE `key` = 'config_admin_language'
            ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleAccount checkLicense
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function checkLicense()
    {
        $sql = "
            SELECT `LanguageCode`, `AccessLicenseNumber`, `AccessLicenseText`
            FROM " .DB_PREFIX . "upsmodule_shipping_license
            ";
        $query = $this->db->query($sql);
        return $query->row;
    }
}
