<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleAccessorial file
 *
 * @category Accessorial_Model
 */

class ModelExtensionUpsmoduleAccessorial extends Model
{
    /**
     * ModelExtensionUpsmoduleAccessorial getListAccessorial
     *
     * @return $query
     */
    public function getListAccessorial()
    {
        //select
        $sql = "
            SELECT id, accessorial_key, accessorial_name, accessorial_code, show_config, show_shipping
            FROM " . DB_PREFIX . "upsmodule_accessorial oc_accessorial
            WHERE show_config = 1
            ";
        //get query
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleAccessorial updateListAccessorial
     *
     * @param int    $id            //The id
     * @param string $show_shipping //The show_shipping
     *
     * @return $query
     */
    public function updateListAccessorial($id, $show_shipping)
    {
        //update
        $sql = "UPDATE " . DB_PREFIX . "upsmodule_accessorial
            SET show_shipping = '" . $show_shipping . "'
            WHERE id = '" . (int)$id . "'";
        //get query
        $query = $this->db->query($sql);
        $this->cache->delete('accessorial');
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleAccessorial getListAllAccessorial
     *
     * @return $query
     */
    public function getListAllAccessorial()
    {
        //select
        $sql = "
            SELECT id, accessorial_key, accessorial_name, accessorial_code, show_config, show_shipping
            FROM " . DB_PREFIX . "upsmodule_accessorial oc_accessorial
            ";
        //get query
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleAccessorial getListCheckCOD
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckCod()
    {
        //select
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_menu_COD'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleAccessorial updateCheckAccessorial
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function updateCheckAccessorial()
    {
        //update
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_accessorial'"
        );
        $this->cache->delete('accessorial');
    }
}
