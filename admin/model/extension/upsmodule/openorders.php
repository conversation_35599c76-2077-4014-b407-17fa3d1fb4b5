<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleOpenorders file
 *
 * @category Openorders_Model
 */

class ModelExtensionUpsmoduleOpenorders extends Model
{
    private $_upsop = 'upsmodule_open_orders as upsop LEFT JOIN ';
    private $_opor = 'order AS opor on opor.`order_id` = upsop.`order_id_opencart` LEFT JOIN ';
    private $_orpr = 'order_product AS orpr on opor.`order_id` = orpr.`order_id` LEFT JOIN ';
    private $_upssh = 'upsmodule_shipping_services AS upssh on upssh.`id` = upsop.`shipping_service` LEFT JOIN ';
    private $_open_close = "";
    private $_open_dropdown = "
    ";
    private $_open_orders = 'openorders';
    private $_orst = "order_status AS orst on orst.`order_status_id` = opor.`order_status_id` LEFT JOIN ";
    private $_cu = "currency AS cu on cu.`currency_id` = opor.`currency_id`
            WHERE upsop.`status` = 1 AND upsop.`id` IN (";
    /**
     * ModelExtensionUpsmoduleOpenorders getListSetting
     * UPS <<EMAIL>>
     *
     * @param string $data //The data
     *
     * @return $query
     */
    public function getListSetting($data = [])
    {
        try {
            $sql = "
                SELECT upsop.*,
                opor.`order_id`, opor.`firstname`, opor.`lastname`, opor.`email`, opor.`telephone`,
                opor.`shipping_company`, opor.`shipping_address_1`, opor.`shipping_address_2`, opor.`shipping_city`,
                opor.`shipping_postcode`, opor.`shipping_country`, opor.`shipping_zone`,
                DATE_FORMAT(opor.`date_added`, '%b %d, %Y') as datesort, DATE_FORMAT(opor.`date_added`, '%T') as timesort,
                opor.`payment_code`, opor.`currency_code`, opor.`total`, opor.`shipping_country_id`,
                SUM(orpr.`total`) as totalProduct, GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct,
                upssh.`service_name`, upssh.`service_symbol`, upssh.`service_type`,
                orst.`name`, CONCAT(upssh.`service_type`, ' ', upssh.`service_name`) sort_service,
                CONCAT(upsop.`ap_country`, ' ', upsop.`ap_city`, ' ', upsop.`ap_address1`, ' ', opor.`shipping_country`,
                ' ', opor.`shipping_city`, ' ', opor.`shipping_address_1`, ' ', opor.`shipping_address_2`) sort_address,opor.shipping_method
                FROM " . DB_PREFIX . $this->_open_close . $this->_upsop . $this->_open_dropdown
                . DB_PREFIX . $this->_open_close . $this->_opor . $this->_open_dropdown
                . DB_PREFIX . $this->_open_close . $this->_orpr . $this->_open_dropdown
                .  DB_PREFIX . $this->_open_close . $this->_upssh . $this->_open_dropdown
                .  DB_PREFIX . "order_status AS orst on orst.`order_status_id` = opor.`order_status_id`
                WHERE upsop.`status` = 1 AND orst.`order_status_id` = opor.`order_status_id`
                AND orst.`language_id` = opor.`language_id`
                GROUP BY orpr.`order_id`
                ORDER BY `" . $data['sort'] . "` " . $data['order'] . $this->_open_dropdown;
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
            $query = $this->db->query($sql);
            return $query->rows;
        } catch (Exception $err) {
            http_response_code(403);
            $this->response->redirect($this->url->link('error/not_found&user_token=' . $this->session->data['user_token']));
        }
    }

    /**
     * ModelExtensionUpsmoduleOpenorders getExportAllData
     * UPS <<EMAIL>>
     *
     * @param string $order_by //The orderBy
     *
     * @return $query
     */
    public function getExportAllData($order_by)
    {
        $sql = "
            SELECT upsop.*,
            opor.`order_id`, opor.`firstname`, opor.`lastname`, opor.`email`, opor.`telephone`,
            opor.`shipping_company`, opor.`shipping_address_1`, opor.`shipping_address_2`, opor.`shipping_city`,
            opor.`shipping_postcode`, opor.`shipping_country`, opor.`shipping_zone`, opor.`currency_value`,
            DATE_FORMAT(opor.`date_added`, '%b %d, %Y') as datesort, DATE_FORMAT(opor.`date_added`, '%T') as timesort,
            opor.`payment_code`, opor.`currency_code`, opor.`total`, opor.`shipping_country_id`,
            SUM(orpr.`total` * opor.`currency_value`) as totalProduct, cu.`decimal_place`,
            GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct, upssh.`service_name`,
            upssh.`service_symbol`, upssh.`service_type`, opor.`shipping_firstname`, opor.`shipping_lastname`,
            orst.`name`, (opor.`total` * opor.`currency_value`) as totalAll
            FROM " . DB_PREFIX . $this->_open_close . $this->_upsop . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_opor . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_orpr . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_upssh . $this->_open_dropdown
            .  DB_PREFIX . $this->_orst
            . DB_PREFIX . "currency AS cu on cu.`currency_id` = opor.`currency_id`
            WHERE upsop.`status` = 1 AND orst.`order_status_id` = opor.`order_status_id`
            AND orst.`language_id` = opor.`language_id`
            GROUP BY orpr.`order_id`
            ORDER BY upsop.`id` " . $order_by . $this->_open_dropdown;
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleOpenorders getExportOrderData
     * UPS <<EMAIL>>
     *
     * @param int    $order_ids //The orderIds
     * @param string $order_by  //The orderBy
     *
     * @return $query
     */
    public function getExportOrderData($order_ids, $order_by)
    {
        $sql = "
            SELECT upsop.*,
            opor.`order_id`, opor.`firstname`, opor.`lastname`, opor.`email`, opor.`telephone`,
            opor.`shipping_company`, opor.`shipping_address_1`, opor.`shipping_address_2`, opor.`shipping_city`,
            opor.`shipping_postcode`, opor.`shipping_country`, opor.`shipping_zone`,
            DATE_FORMAT(opor.`date_added`, '%b %d, %Y') as datesort, DATE_FORMAT(opor.`date_added`, '%T') as timesort,
            opor.`payment_code`, opor.`currency_code`, opor.`total`, opor.`shipping_country_id`, opor.`currency_value`,
            SUM(orpr.`total` * opor.`currency_value`) as totalProduct, cu.`decimal_place`,
            GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct, upssh.`service_name`,
            upssh.`service_symbol`, upssh.`service_type`, opor.`shipping_firstname`, opor.`shipping_lastname`,
            orst.`name`, (opor.`total` * opor.`currency_value`) as totalAll
            FROM " . DB_PREFIX . $this->_open_close . $this->_upsop . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_opor . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_orpr . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_upssh . $this->_open_dropdown
            .  DB_PREFIX . $this->_orst
            . DB_PREFIX . $this->_cu . $order_ids . ")
            AND orst.`order_status_id` = opor.`order_status_id` AND orst.`language_id` = opor.`language_id`
            GROUP BY orpr.`order_id`
            ORDER BY upsop.`id` " . $order_by . $this->_open_dropdown;
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleOpenorders updateStatusArchivedOrders
     * UPS <<EMAIL>>
     *
     * @param int $order_ids //The orderIds
     *
     * @return null
     */
    public function updateStatusArchivedOrders($order_ids)
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_open_orders
            SET `status` = 3
            WHERE `id` IN (" . $order_ids . ")"
        );
            $this->cache->delete($this->_open_orders);
    }

    /**
     * ModelExtensionUpsmoduleOpenorders updateDateArchivedOrders
     * UPS <<EMAIL>>
     *
     * @param int $order_ids //The orderIds
     *
     * @return null
     */
    public function updateDateArchivedOrders($order_ids)
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_open_orders
            SET `archive_orders` = CURRENT_DATE()
            WHERE `id` IN (" . $order_ids . ")"
        );
            $this->cache->delete($this->_open_orders);
    }

    /**
     * ModelExtensionUpsmoduleOpenorders getMultiDetailOrder
     * UPS <<EMAIL>>
     *
     * @param int $order_ids //The orderIds
     *
     * @return $query
     */
    public function getMultiDetailOrder($order_ids)
    {
        $sql = "
            SELECT upsop.*,
            opor.`order_id`, opor.`firstname`, opor.`lastname`, opor.`email`, opor.`telephone`,
            opor.`shipping_company`, opor.`shipping_address_1`, opor.`shipping_address_2`, opor.`shipping_city`,
            opor.`shipping_postcode`, opor.`shipping_country`, opor.`shipping_country_id`, opor.`shipping_country_id`,
            opor.`shipping_zone`, opor.`shipping_zone_id`, opor.`date_added`, opor.`shipping_method`,
            opor.`payment_code`, opor.`currency_code`, opor.`total`, opor.`currency_value`,
            SUM(orpr.`total`) as totalProduct, GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct,
            upssh.`service_name`, upssh.`service_symbol`, upssh.`service_type`, upssh.`country_code`,
            upssh.`rate_code`, upssh.`id` as idservice, opor.`shipping_firstname`, opor.`shipping_lastname`,
            orst.`name`, (opor.`total` * opor.`currency_value`) as totalAll,
            cu.`code`, cu.`symbol_left`, cu.`symbol_right`, cu.`decimal_place`
            FROM " . DB_PREFIX . $this->_open_close . $this->_upsop . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_opor . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_orpr . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_upssh . $this->_open_dropdown
            .  DB_PREFIX . $this->_orst
            . DB_PREFIX . $this->_cu . $order_ids . ")
            AND orst.`order_status_id` = opor.`order_status_id` AND orst.`language_id` = opor.`language_id`
            GROUP BY orpr.`order_id`
            ORDER BY upsop.`id` ASC
                ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleOpenorders getDetailOrderData
     * UPS <<EMAIL>>
     *
     * @param int $order_ids //The orderIds
     *
     * @return $query
     */
    public function getDetailOrderData($order_ids)
    {
        $sql = "
            SELECT upsop.*,
            opor.`order_id`, opor.`firstname`, opor.`lastname`, opor.`email`, opor.`telephone`,
            opor.`shipping_company`, opor.`shipping_address_1`, opor.`shipping_address_2`, opor.`shipping_city`,
            opor.`shipping_postcode`, opor.`shipping_country`, opor.`shipping_country_id`, opor.`shipping_country_id`,
            opor.`shipping_zone`, opor.`shipping_zone_id`, opor.`date_added`, opor.`shipping_method`,
            opor.`payment_code`, opor.`currency_code`, opor.`total`, opor.`currency_value`,
            SUM(orpr.`total`) as totalProduct, GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct,
            upssh.`service_name`, upssh.`service_symbol`, upssh.`service_type`, upssh.`country_code`,
            upssh.`rate_code`, upssh.`id` as idservice, opor.`shipping_firstname`, opor.`shipping_lastname`,
            orst.`name`, (opor.`total` * opor.`currency_value`) as totalAll,
            cu.`code`, cu.`symbol_left`, cu.`symbol_right`, cu.`decimal_place`
            FROM " . DB_PREFIX . $this->_open_close . $this->_upsop . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_opor . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_orpr . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_upssh . $this->_open_dropdown
            .  DB_PREFIX . $this->_orst
            . DB_PREFIX . $this->_cu . $order_ids . ")
            AND orst.`order_status_id` = opor.`order_status_id` AND orst.`language_id` = opor.`language_id`
            GROUP BY orpr.`order_id`
            ORDER BY upsop.`id` ASC
                ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    public function getDetailOrderDataForced($order_ids)
    {
        $sql = "
            SELECT upsop.*,
            opor.`order_id`, opor.`firstname`, opor.`lastname`, opor.`email`, opor.`telephone`,
            opor.`shipping_company`, opor.`shipping_address_1`, opor.`shipping_address_2`, opor.`shipping_city`,
            opor.`shipping_postcode`, opor.`shipping_country`, opor.`shipping_country_id`, opor.`shipping_country_id`,
            opor.`shipping_zone`, opor.`shipping_zone_id`, opor.`date_added`, opor.`shipping_method`,
            opor.`payment_code`, opor.`currency_code`, opor.`total`, opor.`currency_value`,
            SUM(orpr.`total`) as totalProduct, GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct,
            upssh.`service_name`, upssh.`service_symbol`, upssh.`service_type`, upssh.`country_code`,
            upssh.`rate_code`, upssh.`id` as idservice, opor.`shipping_firstname`, opor.`shipping_lastname`,
            orst.`name`, (opor.`total` * opor.`currency_value`) as totalAll,
            cu.`code`, cu.`symbol_left`, cu.`symbol_right`, cu.`decimal_place`
            FROM " . DB_PREFIX . $this->_open_close . $this->_upsop . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_opor . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_orpr . $this->_open_dropdown
            .  DB_PREFIX . $this->_open_close . $this->_upssh . $this->_open_dropdown
            .  DB_PREFIX . $this->_orst
            . DB_PREFIX . "currency AS cu on cu.`currency_id` = opor.`currency_id`
            WHERE upsop.`order_id_opencart` IN (" . $order_ids . ")
            AND orst.`order_status_id` = opor.`order_status_id` AND orst.`language_id` = opor.`language_id`
            GROUP BY orpr.`order_id`
            ORDER BY upsop.`id` ASC
                ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    public function getTrackingNumberByShipmentId($shipment_id)
    {
        $sql = "
            SELECT `shipment_number`
            FROM " . DB_PREFIX . "upsmodule_shipping_tracking
            WHERE `id` = '" . $shipment_id . "'
        ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleOpenorders getTotalNews
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getTotalNews()
    {
        $query = $this->db->query(
            "SELECT COUNT(1) AS total
            FROM " . DB_PREFIX . "upsmodule_open_orders Where status = 1"
        );
        return $query->row['total'];
    }

    /**
     * ModelExtensionUpsmoduleOpenorders updateStatusOrder
     * @author: UPS <<EMAIL>>
     *
     * @param int $order_id    //The order_id
     * @param int $shipment_id //The shipment_id
     *
     * @return $query
     */
    public function updateStatusOrder($order_id, $shipment_id)
    {
        $sql = "
                UPDATE " . DB_PREFIX . "upsmodule_open_orders
                SET `status` = 2, `shipment_id` = '" . $shipment_id . "'
                WHERE `id` IN(" . $order_id . ")";
        $this->db->query($sql);
        $select = "
                SELECT `order_id_opencart` FROM " . DB_PREFIX . "upsmodule_open_orders
                WHERE `id` IN(" . $order_id . ")";
        $select = $this->db->query($select);
        return $select->rows;
    }

    public function updateStatusOrderoid($order_id, $shipment_id)
    {
        $sql = "
                UPDATE " . DB_PREFIX . "upsmodule_open_orders
                SET `status` = 2, `shipment_id` = '" . $shipment_id . "'
                WHERE `order_id_opencart` IN(" . $order_id . ")";
        $this->db->query($sql);
        $select = "
                SELECT `order_id_opencart` FROM " . DB_PREFIX . "upsmodule_open_orders
                WHERE `order_id_opencart` IN(" . $order_id . ")";
        $select = $this->db->query($select);
        return $select->rows;
    }

    /**
     * ModelExtensionUpsmoduleOpenorders getListCheckBillingPreference
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckBillingPreference()
    {
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_menu_billling_preference'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleOpenorders checkDateArchivedOrders
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function checkDateArchivedOrders()
    {
        $sql = "
            SELECT `id`, `archive_orders`, `date_created`
            FROM " . DB_PREFIX . "upsmodule_open_orders";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleOpenorders checkDateArchivedOrders
     * UPS <<EMAIL>>
     *
     * @param int $order_id //The order_id
     *
     * @return null
     */
    public function checkUpdateDateArchivedOrders($order_id)
    {
        $sql = "
            UPDATE " . DB_PREFIX . "upsmodule_open_orders
            SET `status` = 3, `archive_orders` = CURRENT_DATE()
            WHERE `id` IN(" . $order_id . ")";
        $query = $this->db->query($sql);
        $this->cache->delete($this->_open_orders);
    }

    /**
     * ModelExtensionUpsmoduleOpenorders checkOderShipment
     * @author: UPS <<EMAIL>>
     *
     * @param int $order_id //The order_id
     *
     * @return $query
     */
    public function checkOderShipment($order_id)
    {
        $sql = "
            select group_concat(od.order_id_opencart) id
            FROM `" . DB_PREFIX . "upsmodule_open_orders` od
            LEFT JOIN  `" . DB_PREFIX . "upsmodule_shipping_shipments` sm on sm.id = od.shipment_id
            WHERE od.id in (" . $order_id . ")
            AND sm.id > 0;
            ";
        $query = $this->db->query($sql)->row;
        if (!empty($query['id'])) {
            return $query['id'];
        } else {
            return '';
        }
    }
}
