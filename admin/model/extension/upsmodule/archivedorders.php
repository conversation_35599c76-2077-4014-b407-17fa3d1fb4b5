<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleArchivedorders file
 *
 * @category Archivedorders_Model
 */

class ModelExtensionUpsmoduleArchivedorders extends Model
{
    private $_upsop = 'upsmodule_open_orders as upsop LEFT JOIN ';
    private $_opor = 'order AS opor on opor.`order_id` = upsop.`order_id_opencart` LEFT JOIN ';
    private $_orpr = 'order_product AS orpr on opor.`order_id` = orpr.`order_id` LEFT JOIN ';
    private $_upssh = 'upsmodule_shipping_services AS upssh on upssh.`id` = upsop.`shipping_service` LEFT JOIN ';
    private $_archived_dropdown = "
    ";
    private $_archived_close = "";

    /**
     * ModelExtensionUpsmoduleArchivedorders getListSetting
     * UPS <<EMAIL>>
     *
     * @param string $data //The data
     *
     * @return $query
     */
    public function getListSetting($data = [])
    {
        //get getListSetting
        try {
            $sql = "
                SELECT upsop.*,
                opor.`order_id`, opor.`firstname`, opor.`lastname`, opor.`email`, opor.`telephone`,
                opor.`shipping_company`, opor.`shipping_address_1`, opor.`shipping_address_2`, opor.`shipping_city`,
                opor.`shipping_postcode`, opor.`shipping_country`, opor.`shipping_zone`,
                DATE_FORMAT(opor.`date_added`, '%b %d, %Y') as datesort, DATE_FORMAT(opor.`date_added`, '%T') as timesort,
                opor.`payment_code`, opor.`currency_code`, opor.`total`, opor.`shipping_country_id`,
                SUM(orpr.`total`) as totalProduct, GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct,
                upssh.`service_name`, upssh.`service_symbol`, upssh.`service_type`,
                orst.`name`, CONCAT(upssh.`service_type`, ' ', upssh.`service_name`) sort_service,
                CONCAT(upsop.`ap_country`, ' ', upsop.`ap_city`, ' ', upsop.`ap_address1`, ' ', opor.`shipping_country`,
                ' ', opor.`shipping_city`, ' ', opor.`shipping_address_1`, ' ', opor.`shipping_address_2`) sort_address
                FROM " . DB_PREFIX . $this->_archived_close . $this->_upsop . $this->_archived_dropdown
                . DB_PREFIX . $this->_archived_close . $this->_opor . $this->_archived_dropdown
                . DB_PREFIX . $this->_archived_close . $this->_orpr . $this->_archived_dropdown
                . DB_PREFIX . $this->_archived_close. $this->_upssh . $this->_archived_dropdown
                . DB_PREFIX . "order_status AS orst on orst.`order_status_id` = opor.`order_status_id`
                WHERE upsop.`status` = 3 AND orst.`order_status_id` = opor.`order_status_id`
                AND orst.`language_id` = opor.`language_id`
                GROUP BY orpr.`order_id`
                ORDER BY `" . $data['sort'] . "` " . $data['order'] . "
                    ";
                $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
            $query = $this->db->query($sql);
            return $query->rows;
        } catch (Exception $err) {
            http_response_code(403);
            $this->response->redirect($this->url->link('error/not_found&user_token=' . $this->session->data['user_token']));
        }
    }

    /**
     * ModelExtensionUpsmoduleArchivedorders getDetailOrderData
     * UPS <<EMAIL>>
     *
     * @param string $order_ids //The orderIds
     *
     * @return $query
     */
    public function getDetailOrderData($order_ids)
    {
        //get getDetailOrderData
        $sql = "
            SELECT upsop.*,
            opor.`order_id`, opor.`firstname`, opor.`lastname`, opor.`email`, opor.`telephone`,
            opor.`shipping_company`, opor.`shipping_address_1`, opor.`shipping_address_2`, opor.`shipping_city`,
            opor.`shipping_postcode`, opor.`shipping_country`, opor.`shipping_country_id`, opor.`shipping_country_id`,
            opor.`shipping_zone`, opor.`shipping_zone_id`, opor.`date_added`, opor.`shipping_method`,
            opor.`payment_code`, opor.`currency_code`, opor.`total`, opor.`currency_value`,
            SUM(orpr.`total`) as totalProduct, GROUP_CONCAT(orpr.`quantity`, ' x ', orpr.`name`) listProduct,
            upssh.`service_name`, upssh.`service_symbol`, upssh.`service_type`, upssh.`country_code`,
            upssh.`rate_code`, upssh.`id` as idservice, opor.`shipping_firstname`, opor.`shipping_lastname`,
            orst.`name`, (opor.`total` * opor.`currency_value`) as totalAll,
            cu.`code`, cu.`symbol_left`, cu.`symbol_right`, cu.`decimal_place`
            FROM " . DB_PREFIX . $this->_archived_close . $this->_upsop . $this->_archived_dropdown
            . DB_PREFIX . $this->_archived_close . $this->_opor . $this->_archived_dropdown
            . DB_PREFIX . $this->_archived_close . $this->_orpr . $this->_archived_dropdown
            . DB_PREFIX . $this->_archived_close . $this->_upssh . $this->_archived_dropdown
            . DB_PREFIX . "order_status AS orst on orst.`order_status_id` = opor.`order_status_id`
            LEFT JOIN " . DB_PREFIX . "currency AS cu on cu.`currency_id` = opor.`currency_id`
            WHERE upsop.`status` = 3 AND opor.`order_id` IN (" . $order_ids . ")
            AND orst.`order_status_id` = opor.`order_status_id` AND orst.`language_id` = opor.`language_id`
            GROUP BY orpr.`order_id`
            ORDER BY upsop.`id` ASC
        ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleArchivedorders getTotalNews
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getTotalNews()
    {
        $query = $this->db->query(
            "SELECT COUNT(1) AS total
            FROM " . DB_PREFIX . "upsmodule_open_orders WHERE status = 3"
        );
        return $query->row['total'];
    }

    /**
     * ModelExtensionUpsmoduleArchivedorders checkDateArchivedOrders
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function checkDateArchivedOrders()
    {
        $sql = "
            SELECT `id`, `archive_orders`, `date_created`
            FROM " . DB_PREFIX . "upsmodule_open_orders";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleArchivedorders checkDeleteDateArchivedOrders
     * UPS <<EMAIL>>
     *
     * @param string $order_id //The order_id
     *
     * @return null
     */
    public function checkDeleteDateArchivedOrders($order_id)
    {
        $sql = "
            UPDATE " . DB_PREFIX . "upsmodule_open_orders
            SET `status` = 0
            WHERE `id` IN(" . $order_id . ")";
        $query = $this->db->query($sql);
        $this->cache->delete('openorders');
    }

    /**
     * ModelExtensionUpsmoduleArchivedorders checkDeleteDateArchivedOrders
     * UPS <<EMAIL>>
     *
     * @param string $order_id //The order_id
     *
     * @return null
     */
    public function setUnArchivedOrders($list_order_id)
    {
        $sql = "
            UPDATE " . DB_PREFIX . "upsmodule_open_orders
            SET `status` = 1
            WHERE `id` IN({$list_order_id})";
        $query = $this->db->query($sql);
        $this->cache->delete('openorders');
    }
}
