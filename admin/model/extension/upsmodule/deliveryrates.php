<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleDeliveryrates file
 *
 * @category Deliveryrates_Model
 */

class ModelExtensionUpsmoduleDeliveryrates extends Model
{
    private $_services = 'extension/upsmodule/services';
    private $_base = 'extension/upsmodule/base';

    /**
     * ModelExtensionUpsmoduleDeliveryrates getListSetting
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListSetting()
    {
        $sql = "
                SELECT dr.*, ss.service_name, ss.service_symbol, ss.country_code, ss.service_type
                    FROM `" . DB_PREFIX . "upsmodule_delivery_rates` dr
                    LEFT JOIN `" . DB_PREFIX . "upsmodule_shipping_services` ss ON ss.id = dr.service_id
                    WHERE ss.service_selected = 1
                    ;
                ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleDeliveryrates getListService
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListService()
    {
        $sql = "
                SELECT dr.*, ss.service_name, ss.service_symbol, ss.country_code, ss.service_type, ss.service_key
                    FROM `" . DB_PREFIX . "upsmodule_delivery_rates` dr
                    LEFT JOIN `" . DB_PREFIX . "upsmodule_shipping_services` ss ON ss.id = dr.service_id
                    WHERE ss.service_selected = 1
                    GROUP BY dr.`service_id`
                    ORDER BY ss.`id` ASC
                    ;
                ";
        $query = $this->db->query($sql);

        $this->load->model($this->_base);
        $this->load->model($this->_services);
        $setting_data = $this->model_extension_upsmodule_base->getSetting();
        $country_code = "";
        if (isset($setting_data->ups_shipping_country_code)) {
            $country_code = $setting_data->ups_shipping_country_code;
        }
        if (!empty($country_code)) {
            $sorted_services = $this->model_extension_upsmodule_services->getListSortedServicesByCountryCode(
                $country_code
            );
            //sort shipping services
            $result = $this->model_extension_upsmodule_services->getListSortedServices($sorted_services, $query->rows);
            return $result;
        }
        //test again
        return [];
    }

    /**
     * ModelExtensionUpsmoduleDeliveryrates updateDeliveryRates
     * UPS <<EMAIL>>
     *
     * @param string $request //The request
     *
     * @return null
     */
    public function updateDeliveryRates($request)
    {
        $language_id = (int)$this->config->get('config_language_id');
        $this->db->query("DELETE FROM " . DB_PREFIX . "upsmodule_delivery_rates");
        $rate_type_array = $request['rate_type'];
        $sql = "";
        foreach ($rate_type_array as $service_id => $rate_type) {
            if ($rate_type == 'real_time' && isset($request['delivery_rate_percent'][$service_id])) {
                foreach ($request['delivery_rate_percent'][$service_id] as $key => $val) {
                    $sql .= ", ('" . $service_id . "', '" . $rate_type . "', '', '" . $val . "','','')";
                }
            } elseif ($rate_type == 'flat_rate' && isset($request['delivery_rate'][$service_id])) {
                foreach ($request['delivery_rate'][$service_id] as $key => $val) {
                    $min_order_value = '';
                    if (isset($request['min_order_value'][$service_id][$key])) {
                        $min_order_value = $request['min_order_value'][$service_id][$key];
                    }
                    $sql .= ", ('" . $service_id . "', '" . $rate_type . "', '" .
                        $min_order_value . "', '" . $val . "', '" . $request['rate_country'][$service_id][$key] . "','" . $request['rate_rule'][$service_id][$key] . "')";
                }
            } else {
                $sql;
            }
        }
        if (!empty($sql)) {
            $sqls = substr($sql, 1);
            $exist = $this->db->query("SHOW COLUMNS FROM `".DB_PREFIX."upsmodule_delivery_rates` LIKE 'rate_country'");
            
            if(!$exist->num_rows){
                
                $sql_add_col1 = "ALTER TABLE ".DB_PREFIX."upsmodule_delivery_rates ADD rate_country varchar(10) DEFAULT NULL";
                $sql_add_col2 = "ALTER TABLE ".DB_PREFIX."upsmodule_delivery_rates ADD rate_rule varchar(10) DEFAULT NULL";
                $this->db->query($sql_add_col1);
                $this->db->query($sql_add_col2);
            }

            //Insert
            $sql_insert = "INSERT INTO " . DB_PREFIX . "upsmodule_delivery_rates (`service_id`, `rate_type`,
                `min_order_value`, `delivery_rate`, `rate_country`, `rate_rule`) VALUES " . substr($sql, 1) . " ";
            $this->db->query($sql_insert);
        }
        $this->cache->delete('deliveryrates');
    }

    /**
     * ModelExtensionUpsmoduleDeliveryrates getCheckListAP
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getCheckListAp()
    {
        $sql = "
            SELECT `value`
                FROM `" . DB_PREFIX . "upsmodule_setting`
                WHERE `key` = 'ups_shipping_to_ap_delivery'
            ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleDeliveryrates getCheckListADD
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getCheckListAdd()
    {
        $sql = "
            SELECT `value`
                FROM `" . DB_PREFIX . "upsmodule_setting`
                WHERE `key` = 'ups_shipping_to_add_delivery'
            ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleDeliveryrates getListCheckPackageDimension
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckPackageDimension()
    {
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_menu_package_dimensions'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleDeliveryrates updateCheckDeliveryRates
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function updateCheckDeliveryRates()
    {
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_delivery_rates'"
        );
        $this->cache->delete('deliveryrates');
    }
}
