<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleApiModel file
 *
 * @category Api_Model
 */

class ModelExtensionUpsmoduleApiModel extends Model
{
    //get var
    private $_select_id = "SELECT id FROM ";
    private $_insert_into = "
    INSERT INTO ";
    private $_update = "
    UPDATE ";
    private $_table = 'upsmodule_shipping_license';
    private $_where = "'
    WHERE id = '";
    private $_Username = 'Username';
    private $_pass_username_data = 'Password';
    private $_access_license_number = 'AccessLicenseNumber';

    /**
     * ModelExtensionUpsmoduleAccount saveLicense
     * @author: UPS <<EMAIL>>
     *
     * @param string $access_license_text //The accessLicenseText
     * @param string $language_code     //The language_code
     *
     * @return null
     */
    public function saveLicense($access_license_text, $language_code)
    {
        //get query
        $query = $this->db->query($this->_select_id . DB_PREFIX . $this->_table);
        $find = $query->row;
        //check find
        if (empty($find['id'])) {
            //call select
            $this->db->query(
                $this->_insert_into . DB_PREFIX . $this->_table . "
                SET `AccessLicenseText` = '" . $this->db->escape($access_license_text) . "',
                `LanguageCode` = '" . $language_code . "',
                `Username` = 'nousername',
                `Password` = 'nopassword',
                `AccessLicenseNumber` = 'nokey'"
            );
        } else {
            //call select
            $this->db->query(
                $this->_update . DB_PREFIX . $this->_table . "
                SET `AccessLicenseText` = '" . $this->db->escape($access_license_text) .
                $this->_where . $find['id'] . "'"
            );
        }
    }

    /**
     * ModelExtensionUpsmoduleAccount updateLicense
     * @author: UPS <<EMAIL>>
     *
     * @param string $user_name //The username
     * @param string $password //The password
     *
     * @return null
     */
    public function updateLicense($user_name, $password)
    {
        //get query
        $query = $this->db->query($this->_select_id . DB_PREFIX . $this->_table);
        $find = $query->row;
        //check find
        if (empty($find['id'])) {
            $this->db->query(
                $this->_insert_into . DB_PREFIX . $this->_table . "
                SET `AccessLicenseText` = '',
                `LanguageCode` = '',
                `Username` = 'nousername',
                `Password` = 'nopassword',
                `AccessLicenseNumber` = 'nokey'"
            );
        } else {
            $this->db->query(
                $this->_update . DB_PREFIX . $this->_table . "
                SET `Username` = '" . $user_name . "',
                `Password` = '" . $password . $this->_where . $find['id'] . "'"
            );
        }
    }

    /**
     * ModelExtensionUpsmoduleAccount updateLicenseAccess2
     * @author: UPS <<EMAIL>>
     *
     * @param string $access_license_number //The accessLicenseNumber
     *
     * @return null
     */
    public function updateLicenseAccess2($access_license_number)
    {
        //get query
        $query = $this->db->query($this->_select_id . DB_PREFIX . $this->_table);
        $find = $query->row;
        //check find
        if (empty($find['id'])) {
            $this->db->query(
                $this->_insert_into . DB_PREFIX . $this->_table . "
                SET `AccessLicenseText` = '',
                `LanguageCode` = '',
                `Username` = 'nousername',
                `Password` = 'nopassword',
                `AccessLicenseNumber` = 'nokey'"
            );
        } else {
            $this->db->query(
                $this->_update . DB_PREFIX . $this->_table . "
                SET `AccessLicenseNumber` = '" . $access_license_number . $this->_where . $find['id'] . "'"
            );
        }
    }

    /**
     * ModelExtensionUpsmoduleAccount getLicense
     * @author: UPS <<EMAIL>>
     *
     * @return null
     */
    public function getLicense()
    {
        //get query
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . $this->_table);
        $querys = $query->row;
        //check querys
        if (!empty($querys['id'])) {
            return $querys;
        } else {
            $array = [];
            $array['id'] = '';
            $array['AccessLicenseText'] = '';
            $array[$this->_Username] = 'nousername';
            $array[$this->_pass_username_data] = 'nopassword';
            $array[$this->_access_license_number] = 'nokey';
            return $array;
        }
    }

    /**
     * ModelExtensionUpsmoduleAccount getLicenseDefault
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function getLicenseDefault()
    {
        $data = [];
        $data[$this->_Username] = "TuChu0103";
        $data[$this->_pass_username_data] = "T!@#052018";
        $data[$this->_access_license_number] = "0D46678E86A9D038";
        return $data;
    }

    /**
     * ModelExtensionUpsmoduleAccount getListLicensePromoCode
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function getListLicensePromoCode()
    {
        $sql = "
            SELECT *
            FROM " . DB_PREFIX . "upsmodule_shipping_license";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleAccount getLicensePromoCode
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function getLicensePromoCode()
    {
        $checkList = $this->getListLicensePromoCode();
        $data = [];
        $data[$this->_Username] = $checkList[$this->_Username];
        $data[$this->_pass_username_data] = $checkList[$this->_pass_username_data];
        $data[$this->_access_license_number] = $checkList[$this->_access_license_number];
        return $data;
    }

    /**
     * ModelExtensionUpsmoduleAccount updateLanguageCodeLicense
     * UPS <<EMAIL>>
     *
     * @param string $language_code //The languageCode
     *
     * @return null
     */
    public function updateLanguageCodeLicense($language_code)
    {
        //get query
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_shipping_license
            SET `LanguageCode` = '" . $language_code . "'
            WHERE 1"
        );
        $this->cache->delete('termcondition');
    }

    /**
     * ModelExtensionUpsmoduleAccount checkLanguageCodeLicense
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function checkLanguageCodeLicense()
    {
        $sql = "
            SELECT `LanguageCode`
            FROM " . DB_PREFIX . "upsmodule_shipping_license
                ";
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * ModelExtensionUpsmoduleAccount updateLanguageCodeLicense
     * UPS <<EMAIL>>
     *
     * @param string $license_number //The licenseNumber
     *
     * @return null
     */
    public function updateAccessLicenseNumber($license_number)
    {
        //get query
        $this->db->query(
            "UPDATE " . DB_PREFIX . "upsmodule_shipping_license
            SET `AccessLicenseNumber` = '" . $license_number . "'
            WHERE 1"
        );
        $this->cache->delete('account');
    }
}
