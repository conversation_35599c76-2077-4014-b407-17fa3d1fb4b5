<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionUpsmoduleShippingservice file
 *
 * @category Shippingservice_Model
 */

class ModelExtensionUpsmoduleShippingservice extends Model
{
    private $_select_from = '
        SELECT * FROM ';
    private $_update = "UPDATE ";
    private $_services = 'extension/upsmodule/services';

    /**
     * ModelExtensionUpsmoduleShippingservice listShippingServices
     * @author: UPS <<EMAIL>>
     *
     * @param string $country_code //The country_code
     * @param string $service_type //The service_type
     *
     * @return $query
     */
    public function listShippingServices($country_code, $service_type)
    {
        $sql = "" .
            $this->_select_from . "" .DB_PREFIX . "upsmodule_shipping_services oc_package_default
            WHERE country_code = '" . $country_code. "' AND service_type = '" . $service_type. "'";
        $query = $this->db->query($sql);
        $this->load->model($this->_services);
        $sorted_services = $this->model_extension_upsmodule_services->getListSortedServicesByCountryCode(
            $country_code,
            $service_type
        );
        //sort shipping services
        $result = $this->model_extension_upsmodule_services->getListSortedServices($sorted_services, $query->rows);
        return $result;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice getShippingServiceByCountry
     * @author: UPS <<EMAIL>>
     *
     * @param string $country_code //The country_code
     *
     * @return $query
     */
    public function getShippingServiceByCountry($country_code)
    {
        $sql = "" .
            $this->_select_from . "" .DB_PREFIX . "upsmodule_shipping_services oc_package_default
            WHERE country_code = '" . $country_code. "'";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice listAllShippingServices
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function listAllShippingServices()
    {
        $sql = "" .
            $this->_select_from . "" .DB_PREFIX . "upsmodule_shipping_services oc_package_default";
            $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice getListUncheckServices
     * @author: UPS <<EMAIL>>
     *
     * @param string $country_code //The country_code
     *
     * @return $query
     */
    public function getListUncheckServices($country_code)
    {
        $sql = "
            SELECT `id`, `service_key`, `service_selected`
            FROM " .DB_PREFIX . "upsmodule_shipping_services oc_package_default
            WHERE country_code = '" . $country_code. "' AND service_selected = '0'";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice getListAccount
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListAccount()
    {
        $sql = "
            SELECT `account_id`, `address_type`, `ups_account_number`, `account_default`
            FROM " .DB_PREFIX . "upsmodule_account oc_package_default";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice getSetting
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getSetting()
    {
        $query = $this->db->query(
            "SELECT  `setting_id`, `key`, `value`
            FROM " . DB_PREFIX . "upsmodule_setting ocs
            WHERE ocs.key in ('ups_shipping_to_ap_delivery', 'ups_shipping_default_shipping',
            'ups_shipping_chosen_number_of_access', 'ups_shipping_chosen_display_all', 'ups_shipping_to_add_delivery',
            'ups_shipping_cut_off_time', 'ups_shipping_choose_account_number_add', 'ups_shipping_require_signature',
            'ups_shipping_choose_account_number_ap', 'ups_shipping_apadr_toshipadr')"
        );
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice updateSetting
     * @author: UPS <<EMAIL>>
     *
     * @param string $key   //The key
     * @param string $value //The value
     *
     * @return null
     */
    public function updateSetting($key, $value)
    {
        $sql = $this->_update . DB_PREFIX . "upsmodule_setting
                        SET value = '" . $value. "'
                        WHERE " . DB_PREFIX . "upsmodule_setting.key = '" . $key. "'";
        $query = $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleShippingservice updateService
     * @author: UPS <<EMAIL>>
     *
     * @param string $id    //The id
     * @param string $value //The value
     *
     * @return null
     */
    public function updateService($id, $value)
    {
        $sql = $this->_update . DB_PREFIX . "upsmodule_shipping_services
                        SET service_selected = '" . $value. "'
                        WHERE " . DB_PREFIX . "upsmodule_shipping_services.id = '" . $id. "'";
        $query = $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleShippingservice saveDeliveryRates
     * @author: UPS <<EMAIL>>
     *
     * @param string $service_id //The service_id
     *
     * @return null
     */
    public function saveDeliveryRates($country_code, $service_id)
    {
        $rate_type = 'flat_rate';
        $delivery_rate = '0';
        if (strtolower($country_code) == 'us') {
            $rate_type = 'real_time';
            $delivery_rate = '100';
        }
        $sql = "INSERT INTO " . DB_PREFIX . "upsmodule_delivery_rates
                        SET service_id = '" . $service_id. "',
                            rate_type  = '" . $rate_type . "',
                            min_order_value  = '0',
                            delivery_rate  = '" . $delivery_rate . "'";
        $query = $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleShippingservice saveDeliveryRateForUS
     * @author: UPS <<EMAIL>>
     *
     * @param string $service_id //The service_id
     *
     * @return null
     */
    public function saveDeliveryRateForUS($service_id)
    {
        $sql = "
            INSERT INTO " . DB_PREFIX . "upsmodule_delivery_rates (
                `service_id`, `rate_type`, `min_order_value`, `delivery_rate`)
            VALUES ('" . $service_id. "', 'real_time', '0', '100')
            ON DUPLICATE KEY UPDATE `service_id` = values(`service_id`)";
        $this->db->query($sql);
    }

    /**
     * ModelExtensionUpsmoduleShippingservice getListDelivery
     * @author: UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListDelivery()
    {
        $sql = "" .
            $this->_select_from . "" .DB_PREFIX . "upsmodule_delivery_rates oc_delivery";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice deleteDelivery
     * @author: UPS <<EMAIL>>
     *
     * @param string $id //The id
     *
     * @return $query
     */
    public function deleteDelivery($id)
    {
        $sql = "DELETE FROM " . DB_PREFIX . "upsmodule_delivery_rates WHERE service_id = '" . $id . "' ";
        $query = $this->db->query($sql);
        return $query;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice getShippingServiceById
     * @author: UPS <<EMAIL>>
     *
     * @param string $serviceId //The serviceId
     *
     * @return $query
     */
    public function getShippingServiceById($serviceId)
    {
        $sql = "" .
            $this->_select_from . "" .DB_PREFIX . "upsmodule_shipping_services
            WHERE `id` = '". $serviceId ."'";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice getListCheckAccount
     * UPS <<EMAIL>>
     *
     * @return $query
     */
    public function getListCheckAccount()
    {
        $sql = "
            SELECT `key`, `value`
            FROM " .DB_PREFIX . "upsmodule_setting
            WHERE `key` = 'ups_shipping_menu_account'
            ";
        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * ModelExtensionUpsmoduleShippingservice updateCheckShippingService
     * UPS <<EMAIL>>
     *
     * @return null
     */
    public function updateCheckShippingService()
    {
        $this->db->query(
            $this->_update . DB_PREFIX . "upsmodule_setting
            SET `value` = '1'
            where `key` = 'ups_shipping_menu_shipping_service'"
        );
        $this->cache->delete('shippingservice');
    }
}
