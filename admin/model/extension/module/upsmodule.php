<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ModelExtensionModuleUpsmodule file
 *
 * @category Upsmodule_Model
 */

class ModelExtensionModuleUpsmodule extends Model
{
    private $_id = 'ups_shipping_module';
    private $_sub_versions = ['lite', 'light', 'free'];
    private $_config_file = '';
    private $_config_special = 'config/';
    private $_create_table_exist = "CREATE TABLE IF NOT EXISTS `";
    private $_create_table_exist_drop = "CREATE TABLE IF NOT EXISTS `";
    private $_insert_into = "INSERT INTO `";
    private $_drop_table = "DROP TABLE IF EXISTS `";

    /**
     * ModelExtensionModuleUpsmodule execute
     *
     * @param string $registry //The registry
     *
     * @return null
     */
    public function __construct($registry)
    {
        //__construct($registry)
        parent::__construct($registry);
        $this->_config_file = $this->getConfigFile($this->_id, $this->_sub_versions);
    }

    /**
     * ModelExtensionModuleUpsmodule getConfigFile
     * @author: UPS <<EMAIL>>
     *
     * @param int    $id           //The id
     * @param string $sub_versions //The sub_versions
     *
     * @return $id
     */
    public function getConfigFile($id, $sub_versions)
    {
        //check isset post['config']
        if (isset($this->request->post['config'])) {
            return $this->request->post['config'];
        }
        //set $full
        $full = DIR_SYSTEM . $this->_config_special . $id . '.php';
        //check $full
        if (file_exists($full)) {
            return $id;
        }
        //check $sub_versions
        foreach ($sub_versions as $lite) {
            if (file_exists(DIR_SYSTEM . $this->_config_special . $id . '_' . $lite . '.php')) {
                return $id . '_' . $lite;
            }
        }
        return false;
    }

    /**
     * ModelExtensionModuleUpsmodule getConfigFile
     * Return list of config files that contain the id of the module.
     * @author: UPS <<EMAIL>>
     *
     * @param int $id //The id
     *
     * @return $files
     */
    public function getConfigFiles($id)
    {
        //$files
        $files = [];
        //$results
        $results = glob(DIR_SYSTEM . $this->_config_special . $id . '*');
        //check $results
        if (!$results) {
            //
            return [];
        }
        //check $results
        foreach ($results as $result) {
            // $files[]
            $files[] = str_replace('.php', '', str_replace(DIR_SYSTEM . $this->_config_special, '', $result));
        }
        return $files;
    }

    /**
     * ModelExtensionModuleUpsmodule getConfigFile
     * Get config file values and merge with config database values
     * @author: UPS <<EMAIL>>
     *
     * @param int    $id          //The id
     * @param string $config_key  //The config_key
     * @param string $store_id    //The store_id
     * @param string $config_file //The config_file
     *
     * @return $result
     */
    public function getConfigData($id, $config_key, $store_id, $config_file = false)
    {
        //!config_file
        if (!$config_file) {
            //config_file
            $config_file = $this->_config_file;
        }
        //config_file
        if ($config_file) {
            //config_file
            $this->config->load($config_file);
        }
        //result
        $result = ($this->config->get($config_key)) ? $this->config->get($config_key) : [];
        return $result;
    }

    /**
     * ModelExtensionModuleUpsmodule getConfigFile
     * @author: UPS <<EMAIL>>
     *
     * @return $result
     */
    public function createTables()
    {
        //region example
        //sql1
        $sql1 = $this->_create_table_exist . DB_PREFIX . "news` (
        `news_id` int(11) NOT NULL AUTO_INCREMENT,
        `image` varchar(255) NOT NULL,
        `date_added` datetime NOT NULL,
        `status` tinyint(1) NOT NULL,
        PRIMARY KEY (`news_id`)
        )";
        $this->db->query($sql1);
        //sql2
        $sql2 = $this->_create_table_exist . DB_PREFIX . "news_description` (
        `news_description_id` int(11) NOT NULL AUTO_INCREMENT,
        `news_id` int(11) NOT NULL,
        `language_id` int(11) NOT NULL,
        `title` varchar(255) COLLATE utf8_bin NOT NULL,
        `description` text COLLATE utf8_bin NOT NULL,
        `short_description` text COLLATE utf8_bin NOT NULL,
        PRIMARY KEY (`news_description_id`)
        )";
        $this->db->query($sql2);
        //end
        //region UPS
        //sql3
        $sql3 = $this->_create_table_exist .DB_PREFIX . "upsmodule_account` (
            `account_id` int(10) NOT NULL AUTO_INCREMENT,
            `title` varchar(10) DEFAULT NULL,
            `fullname` varchar(100) DEFAULT NULL,
            `company` varchar(255) DEFAULT NULL,
            `email` varchar(100) DEFAULT NULL,
            `phone_number` varchar(100) DEFAULT NULL,
            `address_type` varchar(255) DEFAULT NULL,
            `address_1` varchar(255) DEFAULT NULL,
            `address_2` varchar(255) DEFAULT NULL,
            `address_3` varchar(255) DEFAULT NULL,
            `post_code` varchar(50) DEFAULT NULL,
            `city` varchar(255) DEFAULT NULL,
            `state_province_code` CHAR(50) NULL,
            `country` varchar(10) DEFAULT NULL,
            `account_type` int(11) DEFAULT NULL,
            `ups_account_name` varchar(255) DEFAULT NULL,
            `ups_account_number` varchar(255) DEFAULT NULL,
            `ups_invoice_number` varchar(255) DEFAULT NULL,
            `ups_invoice_amount` varchar(255) DEFAULT NULL,
            `ups_currency` varchar(255) DEFAULT NULL,
            `ups_invoice_date` datetime DEFAULT NULL,
            `ups_account_vatnumber` char(15) DEFAULT NULL,
            `ups_account_promocode` char(9) DEFAULT NULL,
            `device_identity` text,
            `account_default` int(11) DEFAULT NULL,
            `business_name` varchar(255) DEFAULT NULL,
            PRIMARY KEY (`account_id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
        ";
        $this->db->query($sql3);
        //sql4
        $sql4 = $this->_create_table_exist .DB_PREFIX . "upsmodule_account_description` (
                `account_id` INT(11) NOT NULL,
                `language_id` INT(11) NOT NULL,
                `name` VARCHAR(255) NOT NULL,
                `description` TEXT NOT NULL,
                `intro_text` TEXT NOT NULL,
                `meta_title` VARCHAR(255) NOT NULL,
                `meta_description` VARCHAR(255) NOT NULL,
                `meta_keyword` VARCHAR(255) NOT NULL,
                `author` VARCHAR(255) NOT NULL,
                `tag` TEXT,
            PRIMARY KEY (`account_id`, `language_id`),
            INDEX `name` (`name`)
        ) DEFAULT COLLATE=utf8_general_ci;";
        $this->db->query($sql4);
        //upsmodule_accessorial
        $this->db->query(
            $this->_create_table_exist_drop .DB_PREFIX . "upsmodule_accessorial` (
                `id` INT(10) NOT NULL,
                `accessorial_key` VARCHAR(100),
                `accessorial_name` VARCHAR(100),
                `accessorial_code` VARCHAR(100),
                `show_config` INT(11),
                `show_shipping` INT(11),
                PRIMARY KEY (`id`)
            ) DEFAULT COLLATE=utf8_general_ci;"
        );
        //sql6
        $sql6 = $this->_create_table_exist .DB_PREFIX . "upsmodule_package_default` (
            `package_id` INT(10) NOT NULL AUTO_INCREMENT,
            `package_name` VARCHAR(255),
            `weight` FLOAT,
            `unit_weight` VARCHAR(30),
            `length` FLOAT,
            `width` FLOAT,
            `height` FLOAT,
            `unit_dimension` VARCHAR(30),
            PRIMARY KEY (`package_id`)
        ) DEFAULT COLLATE=utf8_general_ci;";
        $this->db->query($sql6);
        /*Author: UPS <<EMAIL>>*/
        //sql7
        $sql7 = $this->_create_table_exist .DB_PREFIX . "upsmodule_delivery_rates` (
                `id` INT(10) NOT NULL AUTO_INCREMENT,
                `service_id` int(11) DEFAULT NULL COMMENT 'Service ID',
                `rate_type` varchar(20) DEFAULT NULL COMMENT 'Rate type',
                `min_order_value` float DEFAULT NULL COMMENT 'Min order value',
                `delivery_rate` float DEFAULT NULL COMMENT 'Delivery rate',
                PRIMARY KEY (`id`),
                UNIQUE KEY `idx_key` (`service_id`, `rate_type`, `min_order_value`)
            ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='upsmodule_delivery_rates';";
        $this->db->query($sql7);

        /*Author: UPS <<EMAIL>>*/
        //upsmodule_shipping_services
        $this->db->query(
            $this->_create_table_exist_drop .DB_PREFIX . "upsmodule_shipping_services` (
                `id` int(10) NOT NULL AUTO_INCREMENT,
                `country_code` char(2) DEFAULT NULL COMMENT 'Country',
                `service_type` char(20) DEFAULT NULL COMMENT 'Type(AP-ADD)',
                `service_key` char(100) DEFAULT NULL COMMENT 'Key',
                `service_key_delivery` char(100) DEFAULT NULL COMMENT 'Delivery key',
                `service_key_val` char(100) DEFAULT NULL COMMENT 'Value',
                `service_name` char(100) DEFAULT NULL COMMENT 'Name',
                `rate_code` char(20) DEFAULT NULL COMMENT 'Rate code',
                `tin_t_code` char(20) DEFAULT NULL COMMENT 'Tin T Code',
                `service_selected` int(11) DEFAULT NULL COMMENT 'Selected',
                `service_symbol` char(50) NOT NULL COMMENT 'Package Detail',
                `access_point_id` char(20) DEFAULT NULL COMMENT 'access point id',
                PRIMARY KEY (`id`),
                UNIQUE KEY `idx_key` (`service_key`)
                ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='upsmodule_shipping_services';"
        );
        /*Author: UPS <<EMAIL>>*/
        //sql8
        $sql8 = $this->_create_table_exist_drop . DB_PREFIX . "upsmodule_open_orders` (
                    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                    `order_id_opencart` int(11) DEFAULT NULL COMMENT 'Order Id OpenCart',
                    `shipping_service` int(11) DEFAULT NULL COMMENT 'Service',
                    `accessorial_service` text COMMENT 'Accessorial',
                    `shipment_id` int(11) DEFAULT NULL COMMENT 'Shipment Id',
                    `quote_id` int(11) DEFAULT NULL COMMENT 'Quote Id',
                    `status` int(11) DEFAULT NULL COMMENT 'Order status',
                    `ap_name` varchar(255) DEFAULT NULL COMMENT 'Access Point Name',
                    `ap_address1` char(128) DEFAULT NULL COMMENT 'Access Point Address 1',
                    `ap_address2` char(128) DEFAULT NULL COMMENT 'Access Point Address 2',
                    `ap_address3` char(128) DEFAULT NULL COMMENT 'Access Point Address 3',
                    `ap_state` char(12) DEFAULT NULL COMMENT 'Access Point state',
                    `ap_postcode` char(12) DEFAULT NULL COMMENT 'Access Point post code',
                    `ap_city` char(64) DEFAULT NULL COMMENT 'Access Point city',
                    `ap_country` char(20) DEFAULT NULL COMMENT 'Access Point country',
                    `location_id` int(11) DEFAULT NULL,
                    `access_point_id` char(20) DEFAULT NULL,
                    `cod` int(11) DEFAULT NULL COMMENT 'COD',
                    `archive_orders` datetime DEFAULT NULL,
                    `date_created` datetime DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `idx_orderid` (`order_id_opencart`)
                    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='upsmodule_open_orders';
            ";
        $this->db->query($sql8);
        //end
        //upsmodule_shipping_shipments
        $this->db->query(
            $this->_create_table_exist_drop . DB_PREFIX . "upsmodule_shipping_shipments` (
                `id` int(10) NOT NULL AUTO_INCREMENT,
                `shipment_id_opencart` int(11) DEFAULT NULL,
                `shipment_number` char(30) DEFAULT NULL,
                `create_date` datetime DEFAULT NULL,
                `status` char(50) DEFAULT NULL,
                `cod` int(11) DEFAULT NULL,
                `shipping_fee` double DEFAULT NULL,
                `order_value` double DEFAULT NULL,
                `accessorial_service` text COMMENT 'Accessorial',
                `shipping_service` int(11) DEFAULT NULL,
                `ap_email` char(100) DEFAULT NULL,
                `name` char(100) DEFAULT NULL,
                `address1` char(100) DEFAULT NULL,
                `address2` char(100) DEFAULT NULL,
                `address3` char(100) DEFAULT NULL,
                `state` char(50) DEFAULT NULL,
                `postcode` char(10) DEFAULT NULL,
                `city` char(50) DEFAULT NULL,
                `country` char(50) DEFAULT NULL,
                `phone` char(50) DEFAULT NULL,
                `email` char(100) DEFAULT NULL,
                `location_id` int(11) DEFAULT NULL,
                `access_point_id` char(20) DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `idx_shipment_number` (`shipment_number`)
                ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;"
        );
        //sql10
        $sql10 = $this->_create_table_exist . DB_PREFIX . "upsmodule_shipping_tracking` (
            `id` int(10) NOT NULL AUTO_INCREMENT,
            `tracking_number` char(30) DEFAULT NULL,
            `shipment_number` char(30) DEFAULT NULL,
            `status` tinyint(1) DEFAULT NULL,
            `package_detail` text,
            `order_id` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_shipment_number` (`shipment_number`),
            KEY `idx_tracking_number` (`tracking_number`)
            ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
            ";
        $this->db->query($sql10);
        //sql11
        $sql11 = $this->_create_table_exist . DB_PREFIX . "upsmodule_setting` (
                `setting_id` int(11) NOT NULL AUTO_INCREMENT,
                `store_id` int(11) NOT NULL DEFAULT '0',
                `code` varchar(128) NOT NULL,
                `key` varchar(128) NOT NULL,
                `value` text NOT NULL,
                `serialized` tinyint(1) NOT NULL,
                PRIMARY KEY (`setting_id`)
            ) ENGINE=InnoDB DEFAULT COLLATE=utf8_general_ci;";
        $this->db->query($sql11);
        //Region Update Database
            /*Author: UPS <<EMAIL>>*/
        //merchant_key
        $merchant_key = $this->generateRandomString();
        //sql12
        $sql12 = $this->_insert_into .DB_PREFIX . "upsmodule_setting`(`store_id`, `code`, `key`, `value`, `serialized`)
            VALUES (0, 'config', 'ups_shipping_show_term_condition', 1, 0),
                (0, 'config', 'ups_shipping_accept_term_condition', 0, 0),
                (0, 'config', 'ups_shipping_country_code', 'PL', 0),
                (0, 'config', 'ups_shipping_to_ap_delivery', '0', 0),
                (0, 'config', 'ups_shipping_default_shipping', '1', 0),
                (0, 'config', 'ups_shipping_chosen_number_of_access', '5', 0),
                (0, 'config', 'ups_shipping_chosen_display_all', '10', 0),
                (0, 'config', 'ups_shipping_to_add_delivery', '0', 0),
                (0, 'config', 'ups_shipping_cut_off_time', '17', 0),
                (0, 'config', 'ups_shipping_choose_account_number_ap', '', 0),
                (0, 'config', 'ups_shipping_choose_account_number_add', '', 0),
                (0, 'config', 'ups_shipping_menu_country', 0, 0),
                (0, 'config', 'ups_shipping_menu_term_condition', 0, 0),
                (0, 'config', 'ups_shipping_menu_account', 0, 0),
                (0, 'config', 'ups_shipping_menu_shipping_service', 0, 0),
                (0, 'config', 'ups_shipping_menu_COD', 0, 0),
                (0, 'config', 'ups_shipping_menu_accessorial', 0, 0),
                (0, 'config', 'ups_shipping_menu_package_dimensions', 0, 0),
                (0, 'config', 'ups_shipping_menu_delivery_rates', 0, 0),
                (0, 'config', 'ups_shipping_menu_billling_preference', 0, 0),
                (0, 'config', 'ups_shipping_merchant_key', '$merchant_key', 0),
                (0, 'config', 'ups_shipping_menu_shipment_manage', 0, 0),
                (0, 'config', 'ups_shipping_cod_option_active', 0, 0);";
        $this->db->query($sql12);

        /*Author: UPS <<EMAIL>>*/
        //sql13
        $sql13 = $this->_insert_into .DB_PREFIX . "upsmodule_accessorial`(`id`, `accessorial_key`, `accessorial_name`,
        `accessorial_code`, `show_config`, `show_shipping`)
            VALUES     (1, 'UPS_ACSRL_ADDITIONAL_HADING', 'Additional handling', 100, 1, 0),
                    (2, 'UPS_ACSRL_QV_SHIP_NOTIF', 'Quantum View Ship Notification', 6, 1, 0),
                    (3, 'UPS_ACSRL_QV_DLV_NOTIF', 'Quantum View Delivery Notification', 372, 1, 0),
                    (4, 'UPS_ACSRL_RESIDENTIAL_ADDRESS', 'Residential Address', 270, 0, 0),
                    (5, 'UPS_ACSRL_STATURDAY_DELIVERY', 'Saturday Delivery', 300, 0, 0),
                    (6, 'UPS_ACSRL_CARBON_NEUTRAL', 'Carbon Neutral', 441, 1, 0),
                    (7, 'UPS_ACSRL_DIRECT_DELIVERY_ONLY', 'Direct Delivery Only', '541', 0, 0),
                    (8, 'UPS_ACSRL_DECLARED_VALUE', 'Declared value', 5, 0, 0),
                    (9, 'UPS_ACSRL_SIGNATURE_REQUIRED', 'Signature Required', 2, 1, 0),
                    (10, 'UPS_ACSRL_ADULT_SIG_REQUIRED', 'Adult Signature Required', 3, 1, 0),
                    (11, 'UPS_ACSRL_ACCESS_POINT_COD', 'To Access Point COD', 4, 0, 0),
                    (12, 'UPS_ACSRL_TO_HOME_COD', 'To Home COD', 500, 0, 0)
            ON DUPLICATE KEY UPDATE `id` = values(`id`);";
        $this->db->query($sql13);
        //sql14
        $sql14 = $this->_insert_into .DB_PREFIX . "upsmodule_shipping_services`(`country_code`, `service_type`,
        `service_key`, `service_key_delivery`, `service_key_val`, `service_name`, `rate_code`, `tin_t_code`,
        `service_selected`, `service_symbol`) VALUES ('PL', 'AP', 'UPS_SP_SERV_PL_AP_AP_ECONOMY', 'UPS_DELI_PL_AP_AP_ECONOMY',
                'UPS_DELI_PL_AP_AP_ECONOMY_VAL', 'UPS Access Point Economy', '70', '39', '0', '&trade;'),
            ('PL', 'AP', 'UPS_SP_SERV_PL_AP_STANDARD', 'UPS_DELI_PL_AP_STANDARD', 'UPS_DELI_PL_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('PL', 'AP', 'UPS_SP_SERV_PL_AP_STANDARD_SATDELI', 'UPS_DELI_PL_AP_STANDARD_SATDELI',
                'UPS_DELI_PL_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('PL', 'AP', 'UPS_SP_SERV_PL_AP_EXPEDITED', 'UPS_DELI_PL_AP_EXPEDITED', 'UPS_DELI_PL_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('PL', 'AP', 'UPS_SP_SERV_PL_AP_EXPRESS_SAVER', 'UPS_DELI_PL_AP_EXPRESS_SAVER',
                'UPS_DELI_PL_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('PL', 'AP', 'UPS_SP_SERV_PL_AP_EXPRESS', 'UPS_DELI_PL_AP_EXPRESS', 'UPS_DELI_PL_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('PL', 'AP', 'UPS_SP_SERV_PL_AP_EXPRESS_SATDELI', 'UPS_DELI_PL_AP_EXPRESS_SATDELI',
                'UPS_DELI_PL_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('PL', 'AP', 'UPS_SP_SERV_PL_AP_EXPRESS_PLUS', 'UPS_DELI_PL_AP_EXPRESS_PLUS',
                'UPS_DELI_PL_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('PL', 'ADD', 'UPS_SP_SERV_PL_ADD_STANDARD', 'UPS_DELI_PL_ADD_STANDARD', 'UPS_DELI_PL_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('PL', 'ADD', 'UPS_SP_SERV_PL_ADD_STANDARD_SATDELI', 'UPS_DELI_PL_ADD_STANDARD_SATDELI',
                'UPS_DELI_PL_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('PL', 'ADD', 'UPS_SP_SERV_PL_ADD_EXPEDITED', 'UPS_DELI_PL_ADD_EXPEDITED',
                'UPS_DELI_PL_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('PL', 'ADD', 'UPS_SP_SERV_PL_ADD_EXPRESS_SAVER', 'UPS_DELI_PL_ADD_EXPRESS_SAVER',
                'UPS_DELI_PL_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('PL', 'ADD', 'UPS_SP_SERV_PL_ADD_EXPRESS', 'UPS_DELI_PL_ADD_EXPRESS', 'UPS_DELI_PL_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('PL', 'ADD', 'UPS_SP_SERV_PL_ADD_EXPRESS_SATDELI', 'UPS_DELI_PL_ADD_EXPRESS_SATDELI',
                'UPS_DELI_PL_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('PL', 'ADD', 'UPS_SP_SERV_PL_ADD_EXPRESS_PLUS', 'UPS_DELI_PL_ADD_EXPRESS_PLUS',
                'UPS_DELI_PL_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('GB', 'AP', 'UPS_SP_SERV_GB_AP_STANDARD', 'UPS_DELI_GB_AP_STANDARD', 'UPS_DELI_GB_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('GB', 'AP', 'UPS_SP_SERV_GB_AP_STANDARD_SATDELI', 'UPS_DELI_GB_AP_STANDARD_SATDELI',
                'UPS_DELI_GB_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('GB', 'AP', 'UPS_SP_SERV_GB_AP_EXPEDITED', 'UPS_DELI_GB_AP_EXPEDITED', 'UPS_DELI_GB_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('GB', 'AP', 'UPS_SP_SERV_GB_AP_WW_SAVER', 'UPS_DELI_GB_AP_WW_SAVER', 'UPS_DELI_GB_AP_WW_SAVER_VAL',
                'UPS Express Saver', '65', 'NULL', '0', '&reg;'),
            ('GB', 'AP', 'UPS_SP_SERV_GB_AP_EXPRESS', 'UPS_DELI_GB_AP_EXPRESS', 'UPS_DELI_GB_AP_EXPRESS_VAL',
                'UPS Express', '07', 'NULL', '0', '&reg;'),
            ('GB', 'AP', 'UPS_SP_SERV_GB_AP_EXPRESS_SATDELI', 'UPS_DELI_GB_AP_EXPRESS_SATDELI',
                'UPS_DELI_GB_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('GB', 'AP', 'UPS_SP_SERV_GB_AP_WW_EXPRESS_PLUS', 'UPS_DELI_GB_AP_WW_EXPRESS_PLUS',
                'UPS_DELI_GB_AP_WW_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '21', '0', '&reg;'),
            ('GB', 'ADD', 'UPS_SP_SERV_GB_ADD_STANDARD', 'UPS_DELI_GB_ADD_STANDARD', 'UPS_DELI_GB_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('GB', 'ADD', 'UPS_SP_SERV_GB_ADD_STANDARD_SATDELI', 'UPS_DELI_GB_ADD_STANDARD_SATDELI',
                'UPS_DELI_GB_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('GB', 'ADD', 'UPS_SP_SERV_GB_ADD_EXPEDITED', 'UPS_DELI_GB_ADD_EXPEDITED',
                'UPS_DELI_GB_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', 'NULL', '0', '&reg;'),
            ('GB', 'ADD', 'UPS_SP_SERV_GB_ADD_WW_SAVER', 'UPS_DELI_GB_ADD_WW_SAVER', 'UPS_DELI_GB_ADD_WW_SAVER_VAL',
                'UPS Express Saver', '65', 'NULL', '0', '&reg;'),
            ('GB', 'ADD', 'UPS_SP_SERV_GB_ADD_EXPRESS', 'UPS_DELI_GB_ADD_EXPRESS', 'UPS_DELI_GB_ADD_EXPRESS_VAL',
                'UPS Express', '07', 'NULL', '0', '&reg;'),
            ('GB', 'ADD', 'UPS_SP_SERV_GB_ADD_EXPRESS_SATDELI', 'UPS_DELI_GB_ADD_EXPRESS_SATDELI',
                'UPS_DELI_GB_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('GB', 'ADD', 'UPS_SP_SERV_GB_ADD_WW_EXPRESS_PLUS', 'UPS_DELI_GB_ADD_WW_EXPRESS_PLUS',
                'UPS_DELI_GB_ADD_WW_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '21', '0', '&reg;'),
            ('FR', 'AP', 'UPS_SP_SERV_FR_AP_AP_ECONOMY', 'UPS_DELI_FR_AP_AP_ECONOMY',
                'UPS_DELI_FR_AP_AP_ECONOMY_VAL', 'UPS Access Point Economy', '70', '39', '0', '&trade;'),
            ('FR', 'AP', 'UPS_SP_SERV_FR_AP_STANDARD', 'UPS_DELI_FR_AP_STANDARD', 'UPS_DELI_FR_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('FR', 'AP', 'UPS_SP_SERV_FR_AP_STANDARD_SATDELI', 'UPS_DELI_FR_AP_STANDARD_SATDELI',
                'UPS_DELI_FR_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPEDITED', 'UPS_DELI_FR_AP_EXPEDITED', 'UPS_DELI_FR_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPRESS_SAVER', 'UPS_DELI_FR_AP_EXPRESS_SAVER',
                'UPS_DELI_FR_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPRESS', 'UPS_DELI_FR_AP_EXPRESS', 'UPS_DELI_FR_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPRESS_SATDELI', 'UPS_DELI_FR_AP_EXPRESS_SATDELI',
                'UPS_DELI_FR_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('FR', 'AP', 'UPS_SP_SERV_FR_AP_EXPRESS_PLUS', 'UPS_DELI_FR_AP_EXPRESS_PLUS',
                'UPS_DELI_FR_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_STANDARD', 'UPS_DELI_FR_ADD_STANDARD', 'UPS_DELI_FR_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_STANDARD_SATDELI', 'UPS_DELI_FR_ADD_STANDARD_SATDELI',
                'UPS_DELI_FR_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPEDITED', 'UPS_DELI_FR_ADD_EXPEDITED',
                'UPS_DELI_FR_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPRESS_SAVER', 'UPS_DELI_FR_ADD_EXPRESS_SAVER',
                'UPS_DELI_FR_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPRESS', 'UPS_DELI_FR_ADD_EXPRESS', 'UPS_DELI_FR_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPRESS_SATDELI', 'UPS_DELI_FR_ADD_EXPRESS_SATDELI',
                'UPS_DELI_FR_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('FR', 'ADD', 'UPS_SP_SERV_FR_ADD_EXPRESS_PLUS', 'UPS_DELI_FR_ADD_EXPRESS_PLUS',
                'UPS_DELI_FR_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('DE', 'AP', 'UPS_SP_SERV_DE_AP_STANDARD', 'UPS_DELI_DE_AP_STANDARD', 'UPS_DELI_DE_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('DE', 'AP', 'UPS_SP_SERV_DE_AP_STANDARD_SATDELI', 'UPS_DELI_DE_AP_STANDARD_SATDELI',
                'UPS_DELI_DE_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPEDITED', 'UPS_DELI_DE_AP_EXPEDITED', 'UPS_DELI_DE_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS_SAVER', 'UPS_DELI_DE_AP_EXPRESS_SAVER',
                'UPS_DELI_DE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS_MIDDAY', 'UPS_DELI_DE_AP_EXPRESS_MIDDAY',
                'UPS_DELI_DE_AP_EXPRESS_MIDDAY_VAL', 'UPS Express 12:00', '74', '0', '0', ''),
            ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS', 'UPS_DELI_DE_AP_EXPRESS', 'UPS_DELI_DE_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS_SATDELI', 'UPS_DELI_DE_AP_EXPRESS_SATDELI',
                'UPS_DELI_DE_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('DE', 'AP', 'UPS_SP_SERV_DE_AP_EXPRESS_PLUS', 'UPS_DELI_DE_AP_EXPRESS_PLUS',
                'UPS_DELI_DE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_STANDARD', 'UPS_DELI_DE_ADD_STANDARD', 'UPS_DELI_DE_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_STANDARD_SATDELI', 'UPS_DELI_DE_ADD_STANDARD_SATDELI',
                'UPS_DELI_DE_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPEDITED', 'UPS_DELI_DE_ADD_EXPEDITED',
                'UPS_DELI_DE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS_SAVER', 'UPS_DELI_DE_ADD_EXPRESS_SAVER',
                'UPS_DELI_DE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS_MIDDAY', 'UPS_DELI_DE_ADD_EXPRESS_MIDDAY', 'UPS_DELI_DE_ADD_EXPRESS_MIDDAY_VAL',
                'UPS Express 12:00', '74', '25', '0', ''),
            ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS', 'UPS_DELI_DE_ADD_EXPRESS', 'UPS_DELI_DE_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS_SATDELI', 'UPS_DELI_DE_ADD_EXPRESS_SATDELI',
                'UPS_DELI_DE_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('DE', 'ADD', 'UPS_SP_SERV_DE_ADD_EXPRESS_PLUS', 'UPS_DELI_DE_ADD_EXPRESS_PLUS',
                'UPS_DELI_DE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('ES', 'AP', 'UPS_SP_SERV_ES_AP_STANDARD', 'UPS_DELI_ES_AP_STANDARD', 'UPS_DELI_ES_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('ES', 'AP', 'UPS_SP_SERV_ES_AP_STANDARD_SATDELI', 'UPS_DELI_ES_AP_STANDARD_SATDELI',
                'UPS_DELI_ES_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPEDITED', 'UPS_DELI_ES_AP_EXPEDITED', 'UPS_DELI_ES_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPRESS_SAVER', 'UPS_DELI_ES_AP_EXPRESS_SAVER',
                'UPS_DELI_ES_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPRESS', 'UPS_DELI_ES_AP_EXPRESS', 'UPS_DELI_ES_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPRESS_SATDELI', 'UPS_DELI_ES_AP_EXPRESS_SATDELI',
                'UPS_DELI_ES_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('ES', 'AP', 'UPS_SP_SERV_ES_AP_EXPRESS_PLUS', 'UPS_DELI_ES_AP_EXPRESS_PLUS',
                'UPS_DELI_ES_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_STANDARD', 'UPS_DELI_ES_ADD_STANDARD', 'UPS_DELI_ES_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_STANDARD_SATDELI', 'UPS_DELI_ES_ADD_STANDARD_SATDELI',
                'UPS_DELI_ES_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPEDITED', 'UPS_DELI_ES_ADD_EXPEDITED',
                'UPS_DELI_ES_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPRESS_SAVER', 'UPS_DELI_ES_ADD_EXPRESS_SAVER',
                'UPS_DELI_ES_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPRESS', 'UPS_DELI_ES_ADD_EXPRESS', 'UPS_DELI_ES_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPRESS_SATDELI', 'UPS_DELI_ES_ADD_EXPRESS_SATDELI',
                'UPS_DELI_ES_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('ES', 'ADD', 'UPS_SP_SERV_ES_ADD_EXPRESS_PLUS', 'UPS_DELI_ES_ADD_EXPRESS_PLUS',
                'UPS_DELI_ES_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('IT', 'AP', 'UPS_SP_SERV_IT_AP_STANDARD', 'UPS_DELI_IT_AP_STANDARD', 'UPS_DELI_IT_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('IT', 'AP', 'UPS_SP_SERV_IT_AP_STANDARD_SATDELI', 'UPS_DELI_IT_AP_STANDARD_SATDELI',
                'UPS_DELI_IT_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPEDITED', 'UPS_DELI_IT_AP_EXPEDITED', 'UPS_DELI_IT_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPRESS_SAVER', 'UPS_DELI_IT_AP_EXPRESS_SAVER',
                'UPS_DELI_IT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPRESS', 'UPS_DELI_IT_AP_EXPRESS', 'UPS_DELI_IT_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPRESS_SATDELI', 'UPS_DELI_IT_AP_EXPRESS_SATDELI',
                'UPS_DELI_IT_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('IT', 'AP', 'UPS_SP_SERV_IT_AP_EXPRESS_PLUS', 'UPS_DELI_IT_AP_EXPRESS_PLUS',
                'UPS_DELI_IT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_STANDARD', 'UPS_DELI_IT_ADD_STANDARD', 'UPS_DELI_IT_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_STANDARD_SATDELI', 'UPS_DELI_IT_ADD_STANDARD_SATDELI',
                'UPS_DELI_IT_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPEDITED', 'UPS_DELI_IT_ADD_EXPEDITED',
                'UPS_DELI_IT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPRESS_SAVER', 'UPS_DELI_IT_ADD_EXPRESS_SAVER',
                'UPS_DELI_IT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPRESS', 'UPS_DELI_IT_ADD_EXPRESS', 'UPS_DELI_IT_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPRESS_SATDELI', 'UPS_DELI_IT_ADD_EXPRESS_SATDELI',
                'UPS_DELI_IT_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('IT', 'ADD', 'UPS_SP_SERV_IT_ADD_EXPRESS_PLUS', 'UPS_DELI_IT_ADD_EXPRESS_PLUS',
                'UPS_DELI_IT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('NL', 'AP', 'UPS_SP_SERV_NL_AP_AP_ECONOMY', 'UPS_DELI_NL_AP_AP_ECONOMY',
                'UPS_DELI_NL_AP_AP_ECONOMY_VAL', 'UPS Access Point Economy', '70', '39', '0', '&trade;'),
            ('NL', 'AP', 'UPS_SP_SERV_NL_AP_STANDARD', 'UPS_DELI_NL_AP_STANDARD', 'UPS_DELI_NL_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('NL', 'AP', 'UPS_SP_SERV_NL_AP_STANDARD_SATDELI', 'UPS_DELI_NL_AP_STANDARD_SATDELI',
                'UPS_DELI_NL_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPEDITED', 'UPS_DELI_NL_AP_EXPEDITED', 'UPS_DELI_NL_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPRESS_SAVER', 'UPS_DELI_NL_AP_EXPRESS_SAVER',
                'UPS_DELI_NL_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPRESS', 'UPS_DELI_NL_AP_EXPRESS', 'UPS_DELI_NL_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPRESS_SATDELI', 'UPS_DELI_NL_AP_EXPRESS_SATDELI',
                'UPS_DELI_NL_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('NL', 'AP', 'UPS_SP_SERV_NL_AP_EXPRESS_PLUS', 'UPS_DELI_NL_AP_EXPRESS_PLUS',
                'UPS_DELI_NL_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_STANDARD', 'UPS_DELI_NL_ADD_STANDARD', 'UPS_DELI_NL_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_STANDARD_SATDELI', 'UPS_DELI_NL_ADD_STANDARD_SATDELI',
                'UPS_DELI_NL_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPEDITED', 'UPS_DELI_NL_ADD_EXPEDITED',
                'UPS_DELI_NL_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPRESS_SAVER', 'UPS_DELI_NL_ADD_EXPRESS_SAVER',
                'UPS_DELI_NL_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPRESS', 'UPS_DELI_NL_ADD_EXPRESS', 'UPS_DELI_NL_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPRESS_SATDELI', 'UPS_DELI_NL_ADD_EXPRESS_SATDELI',
                'UPS_DELI_NL_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('NL', 'ADD', 'UPS_SP_SERV_NL_ADD_EXPRESS_PLUS', 'UPS_DELI_NL_ADD_EXPRESS_PLUS',
                'UPS_DELI_NL_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('BE', 'AP', 'UPS_SP_SERV_BE_AP_AP_ECONOMY', 'UPS_DELI_BE_AP_AP_ECONOMY',
                'UPS_DELI_BE_AP_AP_ECONOMY_VAL', 'UPS Access Point Economy', '70', '39', '0', '&trade;'),
            ('BE', 'AP', 'UPS_SP_SERV_BE_AP_STANDARD', 'UPS_DELI_BE_AP_STANDARD', 'UPS_DELI_BE_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('BE', 'AP', 'UPS_SP_SERV_BE_AP_STANDARD_SATDELI', 'UPS_DELI_BE_AP_STANDARD_SATDELI',
                'UPS_DELI_BE_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPEDITED', 'UPS_DELI_BE_AP_EXPEDITED', 'UPS_DELI_BE_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPRESS_SAVER', 'UPS_DELI_BE_AP_EXPRESS_SAVER',
                'UPS_DELI_BE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPRESS', 'UPS_DELI_BE_AP_EXPRESS', 'UPS_DELI_BE_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPRESS_SATDELI', 'UPS_DELI_BE_AP_EXPRESS_SATDELI',
                'UPS_DELI_BE_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('BE', 'AP', 'UPS_SP_SERV_BE_AP_EXPRESS_PLUS', 'UPS_DELI_BE_AP_EXPRESS_PLUS',
                'UPS_DELI_BE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_STANDARD', 'UPS_DELI_BE_ADD_STANDARD', 'UPS_DELI_BE_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_STANDARD_SATDELI', 'UPS_DELI_BE_ADD_STANDARD_SATDELI',
                'UPS_DELI_BE_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPEDITED', 'UPS_DELI_BE_ADD_EXPEDITED',
                'UPS_DELI_BE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPRESS_SAVER', 'UPS_DELI_BE_ADD_EXPRESS_SAVER',
                'UPS_DELI_BE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPRESS', 'UPS_DELI_BE_ADD_EXPRESS', 'UPS_DELI_BE_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPRESS_SATDELI', 'UPS_DELI_BE_ADD_EXPRESS_SATDELI',
                'UPS_DELI_BE_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('BE', 'ADD', 'UPS_SP_SERV_BE_ADD_EXPRESS_PLUS', 'UPS_DELI_BE_ADD_EXPRESS_PLUS',
                'UPS_DELI_BE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('AT', 'AP', 'UPS_SP_SERV_AT_AP_STANDARD', 'UPS_DELI_AT_AP_STANDARD', 'UPS_DELI_AT_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('AT', 'AP', 'UPS_SP_SERV_AT_AP_STANDARD_SATDELI', 'UPS_DELI_AT_AP_STANDARD_SATDELI',
                'UPS_DELI_AT_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPEDITED', 'UPS_DELI_AT_AP_EXPEDITED', 'UPS_DELI_AT_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPRESS_SAVER', 'UPS_DELI_AT_AP_EXPRESS_SAVER',
                'UPS_DELI_AT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPRESS', 'UPS_DELI_AT_AP_EXPRESS', 'UPS_DELI_AT_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPRESS_SATDELI', 'UPS_DELI_AT_AP_EXPRESS_SATDELI',
                'UPS_DELI_AT_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('AT', 'AP', 'UPS_SP_SERV_AT_AP_EXPRESS_PLUS', 'UPS_DELI_AT_AP_EXPRESS_PLUS',
                'UPS_DELI_AT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_STANDARD', 'UPS_DELI_AT_ADD_STANDARD', 'UPS_DELI_AT_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_STANDARD_SATDELI', 'UPS_DELI_AT_ADD_STANDARD_SATDELI',
                'UPS_DELI_AT_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPEDITED', 'UPS_DELI_AT_ADD_EXPEDITED',
                'UPS_DELI_AT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPRESS_SAVER', 'UPS_DELI_AT_ADD_EXPRESS_SAVER',
                'UPS_DELI_AT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPRESS', 'UPS_DELI_AT_ADD_EXPRESS', 'UPS_DELI_AT_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPRESS_SATDELI', 'UPS_DELI_AT_ADD_EXPRESS_SATDELI',
                'UPS_DELI_AT_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('AT', 'ADD', 'UPS_SP_SERV_AT_ADD_EXPRESS_PLUS', 'UPS_DELI_AT_ADD_EXPRESS_PLUS',
                'UPS_DELI_AT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('BG', 'AP', 'UPS_SP_SERV_BG_AP_STANDARD', 'UPS_DELI_BG_AP_STANDARD', 'UPS_DELI_BG_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('BG', 'AP', 'UPS_SP_SERV_BG_AP_STANDARD_SATDELI', 'UPS_DELI_BG_AP_STANDARD_SATDELI',
                'UPS_DELI_BG_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPEDITED', 'UPS_DELI_BG_AP_EXPEDITED', 'UPS_DELI_BG_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPRESS_SAVER', 'UPS_DELI_BG_AP_EXPRESS_SAVER',
                'UPS_DELI_BG_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPRESS', 'UPS_DELI_BG_AP_EXPRESS', 'UPS_DELI_BG_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPRESS_SATDELI', 'UPS_DELI_BG_AP_EXPRESS_SATDELI',
                'UPS_DELI_BG_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('BG', 'AP', 'UPS_SP_SERV_BG_AP_EXPRESS_PLUS', 'UPS_DELI_BG_AP_EXPRESS_PLUS',
                'UPS_DELI_BG_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_STANDARD', 'UPS_DELI_BG_ADD_STANDARD', 'UPS_DELI_BG_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_STANDARD_SATDELI', 'UPS_DELI_BG_ADD_STANDARD_SATDELI',
                'UPS_DELI_BG_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPEDITED', 'UPS_DELI_BG_ADD_EXPEDITED',
                'UPS_DELI_BG_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPRESS_SAVER', 'UPS_DELI_BG_ADD_EXPRESS_SAVER',
                'UPS_DELI_BG_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPRESS', 'UPS_DELI_BG_ADD_EXPRESS', 'UPS_DELI_BG_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPRESS_SATDELI', 'UPS_DELI_BG_ADD_EXPRESS_SATDELI',
                'UPS_DELI_BG_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('BG', 'ADD', 'UPS_SP_SERV_BG_ADD_EXPRESS_PLUS', 'UPS_DELI_BG_ADD_EXPRESS_PLUS',
                'UPS_DELI_BG_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('HR', 'AP', 'UPS_SP_SERV_HR_AP_STANDARD', 'UPS_DELI_HR_AP_STANDARD', 'UPS_DELI_HR_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('HR', 'AP', 'UPS_SP_SERV_HR_AP_STANDARD_SATDELI', 'UPS_DELI_HR_AP_STANDARD_SATDELI',
                'UPS_DELI_HR_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPEDITED', 'UPS_DELI_HR_AP_EXPEDITED', 'UPS_DELI_HR_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPRESS_SAVER', 'UPS_DELI_HR_AP_EXPRESS_SAVER',
                'UPS_DELI_HR_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPRESS', 'UPS_DELI_HR_AP_EXPRESS', 'UPS_DELI_HR_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPRESS_SATDELI', 'UPS_DELI_HR_AP_EXPRESS_SATDELI',
                'UPS_DELI_HR_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('HR', 'AP', 'UPS_SP_SERV_HR_AP_EXPRESS_PLUS', 'UPS_DELI_HR_AP_EXPRESS_PLUS',
                'UPS_DELI_HR_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_STANDARD', 'UPS_DELI_HR_ADD_STANDARD', 'UPS_DELI_HR_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_STANDARD_SATDELI', 'UPS_DELI_HR_ADD_STANDARD_SATDELI',
                'UPS_DELI_HR_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPEDITED', 'UPS_DELI_HR_ADD_EXPEDITED',
                'UPS_DELI_HR_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPRESS_SAVER', 'UPS_DELI_HR_ADD_EXPRESS_SAVER',
                'UPS_DELI_HR_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPRESS', 'UPS_DELI_HR_ADD_EXPRESS', 'UPS_DELI_HR_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPRESS_SATDELI', 'UPS_DELI_HR_ADD_EXPRESS_SATDELI',
                'UPS_DELI_HR_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('HR', 'ADD', 'UPS_SP_SERV_HR_ADD_EXPRESS_PLUS', 'UPS_DELI_HR_ADD_EXPRESS_PLUS',
                'UPS_DELI_HR_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('CY', 'AP', 'UPS_SP_SERV_CY_AP_STANDARD', 'UPS_DELI_CY_AP_STANDARD', 'UPS_DELI_CY_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('CY', 'AP', 'UPS_SP_SERV_CY_AP_STANDARD_SATDELI', 'UPS_DELI_CY_AP_STANDARD_SATDELI',
                'UPS_DELI_CY_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPEDITED', 'UPS_DELI_CY_AP_EXPEDITED', 'UPS_DELI_CY_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPRESS_SAVER', 'UPS_DELI_CY_AP_EXPRESS_SAVER',
                'UPS_DELI_CY_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPRESS', 'UPS_DELI_CY_AP_EXPRESS', 'UPS_DELI_CY_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPRESS_SATDELI', 'UPS_DELI_CY_AP_EXPRESS_SATDELI',
                'UPS_DELI_CY_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('CY', 'AP', 'UPS_SP_SERV_CY_AP_EXPRESS_PLUS', 'UPS_DELI_CY_AP_EXPRESS_PLUS',
                'UPS_DELI_CY_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_STANDARD', 'UPS_DELI_CY_ADD_STANDARD', 'UPS_DELI_CY_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_STANDARD_SATDELI', 'UPS_DELI_CY_ADD_STANDARD_SATDELI',
                'UPS_DELI_CY_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPEDITED', 'UPS_DELI_CY_ADD_EXPEDITED',
                'UPS_DELI_CY_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPRESS_SAVER', 'UPS_DELI_CY_ADD_EXPRESS_SAVER',
                'UPS_DELI_CY_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPRESS', 'UPS_DELI_CY_ADD_EXPRESS', 'UPS_DELI_CY_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPRESS_SATDELI', 'UPS_DELI_CY_ADD_EXPRESS_SATDELI',
                'UPS_DELI_CY_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('CY', 'ADD', 'UPS_SP_SERV_CY_ADD_EXPRESS_PLUS', 'UPS_DELI_CY_ADD_EXPRESS_PLUS',
                'UPS_DELI_CY_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_STANDARD', 'UPS_DELI_CZ_AP_STANDARD', 'UPS_DELI_CZ_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_STANDARD_SATDELI', 'UPS_DELI_CZ_AP_STANDARD_SATDELI',
                'UPS_DELI_CZ_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPEDITED', 'UPS_DELI_CZ_AP_EXPEDITED', 'UPS_DELI_CZ_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPRESS_SAVER', 'UPS_DELI_CZ_AP_EXPRESS_SAVER',
                'UPS_DELI_CZ_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPRESS', 'UPS_DELI_CZ_AP_EXPRESS', 'UPS_DELI_CZ_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPRESS_SATDELI', 'UPS_DELI_CZ_AP_EXPRESS_SATDELI',
                'UPS_DELI_CZ_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('CZ', 'AP', 'UPS_SP_SERV_CZ_AP_EXPRESS_PLUS', 'UPS_DELI_CZ_AP_EXPRESS_PLUS',
                'UPS_DELI_CZ_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_STANDARD', 'UPS_DELI_CZ_ADD_STANDARD', 'UPS_DELI_CZ_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_STANDARD_SATDELI', 'UPS_DELI_CZ_ADD_STANDARD_SATDELI',
                'UPS_DELI_CZ_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPEDITED', 'UPS_DELI_CZ_ADD_EXPEDITED',
                'UPS_DELI_CZ_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPRESS_SAVER', 'UPS_DELI_CZ_ADD_EXPRESS_SAVER',
                'UPS_DELI_CZ_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPRESS', 'UPS_DELI_CZ_ADD_EXPRESS', 'UPS_DELI_CZ_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPRESS_SATDELI', 'UPS_DELI_CZ_ADD_EXPRESS_SATDELI',
                'UPS_DELI_CZ_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('CZ', 'ADD', 'UPS_SP_SERV_CZ_ADD_EXPRESS_PLUS', 'UPS_DELI_CZ_ADD_EXPRESS_PLUS',
                'UPS_DELI_CZ_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('DK', 'AP', 'UPS_SP_SERV_DK_AP_STANDARD', 'UPS_DELI_DK_AP_STANDARD', 'UPS_DELI_DK_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('DK', 'AP', 'UPS_SP_SERV_DK_AP_STANDARD_SATDELI', 'UPS_DELI_DK_AP_STANDARD_SATDELI',
                'UPS_DELI_DK_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPEDITED', 'UPS_DELI_DK_AP_EXPEDITED', 'UPS_DELI_DK_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPRESS_SAVER', 'UPS_DELI_DK_AP_EXPRESS_SAVER',
                'UPS_DELI_DK_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPRESS', 'UPS_DELI_DK_AP_EXPRESS', 'UPS_DELI_DK_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPRESS_SATDELI', 'UPS_DELI_DK_AP_EXPRESS_SATDELI',
                'UPS_DELI_DK_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('DK', 'AP', 'UPS_SP_SERV_DK_AP_EXPRESS_PLUS', 'UPS_DELI_DK_AP_EXPRESS_PLUS',
                'UPS_DELI_DK_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_STANDARD', 'UPS_DELI_DK_ADD_STANDARD', 'UPS_DELI_DK_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_STANDARD_SATDELI', 'UPS_DELI_DK_ADD_STANDARD_SATDELI',
                'UPS_DELI_DK_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPEDITED', 'UPS_DELI_DK_ADD_EXPEDITED',
                'UPS_DELI_DK_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPRESS_SAVER', 'UPS_DELI_DK_ADD_EXPRESS_SAVER',
                'UPS_DELI_DK_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPRESS', 'UPS_DELI_DK_ADD_EXPRESS', 'UPS_DELI_DK_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPRESS_SATDELI', 'UPS_DELI_DK_ADD_EXPRESS_SATDELI',
                'UPS_DELI_DK_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('DK', 'ADD', 'UPS_SP_SERV_DK_ADD_EXPRESS_PLUS', 'UPS_DELI_DK_ADD_EXPRESS_PLUS',
                'UPS_DELI_DK_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('EE', 'AP', 'UPS_SP_SERV_EE_AP_STANDARD', 'UPS_DELI_EE_AP_STANDARD', 'UPS_DELI_EE_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('EE', 'AP', 'UPS_SP_SERV_EE_AP_STANDARD_SATDELI', 'UPS_DELI_EE_AP_STANDARD_SATDELI',
                'UPS_DELI_EE_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPEDITED', 'UPS_DELI_EE_AP_EXPEDITED', 'UPS_DELI_EE_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPRESS_SAVER', 'UPS_DELI_EE_AP_EXPRESS_SAVER',
                'UPS_DELI_EE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPRESS', 'UPS_DELI_EE_AP_EXPRESS', 'UPS_DELI_EE_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPRESS_SATDELI', 'UPS_DELI_EE_AP_EXPRESS_SATDELI',
                'UPS_DELI_EE_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('EE', 'AP', 'UPS_SP_SERV_EE_AP_EXPRESS_PLUS', 'UPS_DELI_EE_AP_EXPRESS_PLUS',
                'UPS_DELI_EE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_STANDARD', 'UPS_DELI_EE_ADD_STANDARD', 'UPS_DELI_EE_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_STANDARD_SATDELI', 'UPS_DELI_EE_ADD_STANDARD_SATDELI',
                'UPS_DELI_EE_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPEDITED', 'UPS_DELI_EE_ADD_EXPEDITED',
                'UPS_DELI_EE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPRESS_SAVER', 'UPS_DELI_EE_ADD_EXPRESS_SAVER',
                'UPS_DELI_EE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPRESS', 'UPS_DELI_EE_ADD_EXPRESS', 'UPS_DELI_EE_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPRESS_SATDELI', 'UPS_DELI_EE_ADD_EXPRESS_SATDELI',
                'UPS_DELI_EE_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('EE', 'ADD', 'UPS_SP_SERV_EE_ADD_EXPRESS_PLUS', 'UPS_DELI_EE_ADD_EXPRESS_PLUS',
                'UPS_DELI_EE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('FI', 'AP', 'UPS_SP_SERV_FI_AP_STANDARD', 'UPS_DELI_FI_AP_STANDARD', 'UPS_DELI_FI_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('FI', 'AP', 'UPS_SP_SERV_FI_AP_STANDARD_SATDELI', 'UPS_DELI_FI_AP_STANDARD_SATDELI',
                'UPS_DELI_FI_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPEDITED', 'UPS_DELI_FI_AP_EXPEDITED', 'UPS_DELI_FI_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPRESS_SAVER', 'UPS_DELI_FI_AP_EXPRESS_SAVER',
                'UPS_DELI_FI_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPRESS', 'UPS_DELI_FI_AP_EXPRESS', 'UPS_DELI_FI_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPRESS_SATDELI', 'UPS_DELI_FI_AP_EXPRESS_SATDELI',
                'UPS_DELI_FI_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('FI', 'AP', 'UPS_SP_SERV_FI_AP_EXPRESS_PLUS', 'UPS_DELI_FI_AP_EXPRESS_PLUS',
                'UPS_DELI_FI_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_STANDARD', 'UPS_DELI_FI_ADD_STANDARD', 'UPS_DELI_FI_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_STANDARD_SATDELI', 'UPS_DELI_FI_ADD_STANDARD_SATDELI',
                'UPS_DELI_FI_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPEDITED', 'UPS_DELI_FI_ADD_EXPEDITED',
                'UPS_DELI_FI_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPRESS_SAVER', 'UPS_DELI_FI_ADD_EXPRESS_SAVER',
                'UPS_DELI_FI_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPRESS', 'UPS_DELI_FI_ADD_EXPRESS', 'UPS_DELI_FI_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPRESS_SATDELI', 'UPS_DELI_FI_ADD_EXPRESS_SATDELI',
                'UPS_DELI_FI_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('FI', 'ADD', 'UPS_SP_SERV_FI_ADD_EXPRESS_PLUS', 'UPS_DELI_FI_ADD_EXPRESS_PLUS',
                'UPS_DELI_FI_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('GR', 'AP', 'UPS_SP_SERV_GR_AP_STANDARD', 'UPS_DELI_GR_AP_STANDARD', 'UPS_DELI_GR_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('GR', 'AP', 'UPS_SP_SERV_GR_AP_STANDARD_SATDELI', 'UPS_DELI_GR_AP_STANDARD_SATDELI',
                'UPS_DELI_GR_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPEDITED', 'UPS_DELI_GR_AP_EXPEDITED', 'UPS_DELI_GR_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPRESS_SAVER', 'UPS_DELI_GR_AP_EXPRESS_SAVER',
                'UPS_DELI_GR_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPRESS', 'UPS_DELI_GR_AP_EXPRESS', 'UPS_DELI_GR_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPRESS_SATDELI', 'UPS_DELI_GR_AP_EXPRESS_SATDELI',
                'UPS_DELI_GR_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('GR', 'AP', 'UPS_SP_SERV_GR_AP_EXPRESS_PLUS', 'UPS_DELI_GR_AP_EXPRESS_PLUS',
                'UPS_DELI_GR_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_STANDARD', 'UPS_DELI_GR_ADD_STANDARD', 'UPS_DELI_GR_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_STANDARD_SATDELI', 'UPS_DELI_GR_ADD_STANDARD_SATDELI',
                'UPS_DELI_GR_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPEDITED', 'UPS_DELI_GR_ADD_EXPEDITED',
                'UPS_DELI_GR_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPRESS_SAVER', 'UPS_DELI_GR_ADD_EXPRESS_SAVER',
                'UPS_DELI_GR_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPRESS', 'UPS_DELI_GR_ADD_EXPRESS', 'UPS_DELI_GR_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPRESS_SATDELI', 'UPS_DELI_GR_ADD_EXPRESS_SATDELI',
                'UPS_DELI_GR_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('GR', 'ADD', 'UPS_SP_SERV_GR_ADD_EXPRESS_PLUS', 'UPS_DELI_GR_ADD_EXPRESS_PLUS',
                'UPS_DELI_GR_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('HU', 'AP', 'UPS_SP_SERV_HU_AP_STANDARD', 'UPS_DELI_HU_AP_STANDARD', 'UPS_DELI_HU_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('HU', 'AP', 'UPS_SP_SERV_HU_AP_STANDARD_SATDELI', 'UPS_DELI_HU_AP_STANDARD_SATDELI',
                'UPS_DELI_HU_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPEDITED', 'UPS_DELI_HU_AP_EXPEDITED', 'UPS_DELI_HU_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPRESS_SAVER', 'UPS_DELI_HU_AP_EXPRESS_SAVER',
                'UPS_DELI_HU_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPRESS', 'UPS_DELI_HU_AP_EXPRESS', 'UPS_DELI_HU_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPRESS_SATDELI', 'UPS_DELI_HU_AP_EXPRESS_SATDELI',
                'UPS_DELI_HU_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('HU', 'AP', 'UPS_SP_SERV_HU_AP_EXPRESS_PLUS', 'UPS_DELI_HU_AP_EXPRESS_PLUS',
                'UPS_DELI_HU_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_STANDARD', 'UPS_DELI_HU_ADD_STANDARD', 'UPS_DELI_HU_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_STANDARD_SATDELI', 'UPS_DELI_HU_ADD_STANDARD_SATDELI',
                'UPS_DELI_HU_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPEDITED', 'UPS_DELI_HU_ADD_EXPEDITED',
                'UPS_DELI_HU_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPRESS_SAVER', 'UPS_DELI_HU_ADD_EXPRESS_SAVER',
                'UPS_DELI_HU_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPRESS', 'UPS_DELI_HU_ADD_EXPRESS', 'UPS_DELI_HU_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPRESS_SATDELI', 'UPS_DELI_HU_ADD_EXPRESS_SATDELI',
                'UPS_DELI_HU_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('HU', 'ADD', 'UPS_SP_SERV_HU_ADD_EXPRESS_PLUS', 'UPS_DELI_HU_ADD_EXPRESS_PLUS',
                'UPS_DELI_HU_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('IE', 'AP', 'UPS_SP_SERV_IE_AP_STANDARD', 'UPS_DELI_IE_AP_STANDARD', 'UPS_DELI_IE_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('IE', 'AP', 'UPS_SP_SERV_IE_AP_STANDARD_SATDELI', 'UPS_DELI_IE_AP_STANDARD_SATDELI',
                'UPS_DELI_IE_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPEDITED', 'UPS_DELI_IE_AP_EXPEDITED', 'UPS_DELI_IE_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPRESS_SAVER', 'UPS_DELI_IE_AP_EXPRESS_SAVER',
                'UPS_DELI_IE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPRESS', 'UPS_DELI_IE_AP_EXPRESS', 'UPS_DELI_IE_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPRESS_SATDELI', 'UPS_DELI_IE_AP_EXPRESS_SATDELI',
                'UPS_DELI_IE_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('IE', 'AP', 'UPS_SP_SERV_IE_AP_EXPRESS_PLUS', 'UPS_DELI_IE_AP_EXPRESS_PLUS',
                'UPS_DELI_IE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_STANDARD', 'UPS_DELI_IE_ADD_STANDARD', 'UPS_DELI_IE_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_STANDARD_SATDELI', 'UPS_DELI_IE_ADD_STANDARD_SATDELI',
                'UPS_DELI_IE_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPEDITED', 'UPS_DELI_IE_ADD_EXPEDITED',
                'UPS_DELI_IE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPRESS_SAVER', 'UPS_DELI_IE_ADD_EXPRESS_SAVER',
                'UPS_DELI_IE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPRESS', 'UPS_DELI_IE_ADD_EXPRESS', 'UPS_DELI_IE_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPRESS_SATDELI', 'UPS_DELI_IE_ADD_EXPRESS_SATDELI',
                'UPS_DELI_IE_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('IE', 'ADD', 'UPS_SP_SERV_IE_ADD_EXPRESS_PLUS', 'UPS_DELI_IE_ADD_EXPRESS_PLUS',
                'UPS_DELI_IE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('IS', 'AP', 'UPS_SP_SERV_IS_AP_STANDARD', 'UPS_DELI_IS_AP_STANDARD', 'UPS_DELI_IS_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('IS', 'AP', 'UPS_SP_SERV_IS_AP_STANDARD_SATDELI', 'UPS_DELI_IS_AP_STANDARD_SATDELI',
                'UPS_DELI_IS_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPEDITED', 'UPS_DELI_IS_AP_EXPEDITED', 'UPS_DELI_IS_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPRESS_SAVER', 'UPS_DELI_IS_AP_EXPRESS_SAVER',
                'UPS_DELI_IS_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPRESS', 'UPS_DELI_IS_AP_EXPRESS', 'UPS_DELI_IS_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPRESS_SATDELI', 'UPS_DELI_IS_AP_EXPRESS_SATDELI',
                'UPS_DELI_IS_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('IS', 'AP', 'UPS_SP_SERV_IS_AP_EXPRESS_PLUS', 'UPS_DELI_IS_AP_EXPRESS_PLUS',
                'UPS_DELI_IS_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_STANDARD', 'UPS_DELI_IS_ADD_STANDARD', 'UPS_DELI_IS_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_STANDARD_SATDELI', 'UPS_DELI_IS_ADD_STANDARD_SATDELI',
                'UPS_DELI_IS_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPEDITED', 'UPS_DELI_IS_ADD_EXPEDITED',
                'UPS_DELI_IS_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPRESS_SAVER', 'UPS_DELI_IS_ADD_EXPRESS_SAVER',
                'UPS_DELI_IS_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPRESS', 'UPS_DELI_IS_ADD_EXPRESS', 'UPS_DELI_IS_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPRESS_SATDELI', 'UPS_DELI_IS_ADD_EXPRESS_SATDELI',
                'UPS_DELI_IS_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('IS', 'ADD', 'UPS_SP_SERV_IS_ADD_EXPRESS_PLUS', 'UPS_DELI_IS_ADD_EXPRESS_PLUS',
                'UPS_DELI_IS_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('LV', 'AP', 'UPS_SP_SERV_LV_AP_STANDARD', 'UPS_DELI_LV_AP_STANDARD', 'UPS_DELI_LV_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('LV', 'AP', 'UPS_SP_SERV_LV_AP_STANDARD_SATDELI', 'UPS_DELI_LV_AP_STANDARD_SATDELI',
                'UPS_DELI_LV_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPEDITED', 'UPS_DELI_LV_AP_EXPEDITED', 'UPS_DELI_LV_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPRESS_SAVER', 'UPS_DELI_LV_AP_EXPRESS_SAVER',
                'UPS_DELI_LV_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPRESS', 'UPS_DELI_LV_AP_EXPRESS', 'UPS_DELI_LV_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPRESS_SATDELI', 'UPS_DELI_LV_AP_EXPRESS_SATDELI',
                'UPS_DELI_LV_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('LV', 'AP', 'UPS_SP_SERV_LV_AP_EXPRESS_PLUS', 'UPS_DELI_LV_AP_EXPRESS_PLUS',
                'UPS_DELI_LV_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_STANDARD', 'UPS_DELI_LV_ADD_STANDARD', 'UPS_DELI_LV_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_STANDARD_SATDELI', 'UPS_DELI_LV_ADD_STANDARD_SATDELI',
                'UPS_DELI_LV_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPEDITED', 'UPS_DELI_LV_ADD_EXPEDITED',
                'UPS_DELI_LV_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPRESS_SAVER', 'UPS_DELI_LV_ADD_EXPRESS_SAVER',
                'UPS_DELI_LV_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPRESS', 'UPS_DELI_LV_ADD_EXPRESS', 'UPS_DELI_LV_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPRESS_SATDELI', 'UPS_DELI_LV_ADD_EXPRESS_SATDELI',
                'UPS_DELI_LV_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('LV', 'ADD', 'UPS_SP_SERV_LV_ADD_EXPRESS_PLUS', 'UPS_DELI_LV_ADD_EXPRESS_PLUS',
                'UPS_DELI_LV_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('LT', 'AP', 'UPS_SP_SERV_LT_AP_STANDARD', 'UPS_DELI_LT_AP_STANDARD', 'UPS_DELI_LT_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('LT', 'AP', 'UPS_SP_SERV_LT_AP_STANDARD_SATDELI', 'UPS_DELI_LT_AP_STANDARD_SATDELI',
                'UPS_DELI_LT_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPEDITED', 'UPS_DELI_LT_AP_EXPEDITED', 'UPS_DELI_LT_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPRESS_SAVER', 'UPS_DELI_LT_AP_EXPRESS_SAVER',
                'UPS_DELI_LT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPRESS', 'UPS_DELI_LT_AP_EXPRESS', 'UPS_DELI_LT_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPRESS_SATDELI', 'UPS_DELI_LT_AP_EXPRESS_SATDELI',
                'UPS_DELI_LT_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('LT', 'AP', 'UPS_SP_SERV_LT_AP_EXPRESS_PLUS', 'UPS_DELI_LT_AP_EXPRESS_PLUS',
                'UPS_DELI_LT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_STANDARD', 'UPS_DELI_LT_ADD_STANDARD', 'UPS_DELI_LT_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_STANDARD_SATDELI', 'UPS_DELI_LT_ADD_STANDARD_SATDELI',
                'UPS_DELI_LT_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPEDITED', 'UPS_DELI_LT_ADD_EXPEDITED',
                'UPS_DELI_LT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPRESS_SAVER', 'UPS_DELI_LT_ADD_EXPRESS_SAVER',
                'UPS_DELI_LT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPRESS', 'UPS_DELI_LT_ADD_EXPRESS', 'UPS_DELI_LT_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPRESS_SATDELI', 'UPS_DELI_LT_ADD_EXPRESS_SATDELI',
                'UPS_DELI_LT_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('LT', 'ADD', 'UPS_SP_SERV_LT_ADD_EXPRESS_PLUS', 'UPS_DELI_LT_ADD_EXPRESS_PLUS',
                'UPS_DELI_LT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('LU', 'AP', 'UPS_SP_SERV_LU_AP_STANDARD', 'UPS_DELI_LU_AP_STANDARD', 'UPS_DELI_LU_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('LU', 'AP', 'UPS_SP_SERV_LU_AP_STANDARD_SATDELI', 'UPS_DELI_LU_AP_STANDARD_SATDELI',
                'UPS_DELI_LU_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPEDITED', 'UPS_DELI_LU_AP_EXPEDITED', 'UPS_DELI_LU_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPRESS_SAVER', 'UPS_DELI_LU_AP_EXPRESS_SAVER',
                'UPS_DELI_LU_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPRESS', 'UPS_DELI_LU_AP_EXPRESS', 'UPS_DELI_LU_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPRESS_SATDELI', 'UPS_DELI_LU_AP_EXPRESS_SATDELI',
                'UPS_DELI_LU_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('LU', 'AP', 'UPS_SP_SERV_LU_AP_EXPRESS_PLUS', 'UPS_DELI_LU_AP_EXPRESS_PLUS',
                'UPS_DELI_LU_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_STANDARD', 'UPS_DELI_LU_ADD_STANDARD', 'UPS_DELI_LU_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_STANDARD_SATDELI', 'UPS_DELI_LU_ADD_STANDARD_SATDELI',
                'UPS_DELI_LU_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPEDITED', 'UPS_DELI_LU_ADD_EXPEDITED',
                'UPS_DELI_LU_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPRESS_SAVER', 'UPS_DELI_LU_ADD_EXPRESS_SAVER',
                'UPS_DELI_LU_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPRESS', 'UPS_DELI_LU_ADD_EXPRESS', 'UPS_DELI_LU_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPRESS_SATDELI', 'UPS_DELI_LU_ADD_EXPRESS_SATDELI',
                'UPS_DELI_LU_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('LU', 'ADD', 'UPS_SP_SERV_LU_ADD_EXPRESS_PLUS', 'UPS_DELI_LU_ADD_EXPRESS_PLUS',
                'UPS_DELI_LU_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('MT', 'AP', 'UPS_SP_SERV_MT_AP_STANDARD', 'UPS_DELI_MT_AP_STANDARD', 'UPS_DELI_MT_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('MT', 'AP', 'UPS_SP_SERV_MT_AP_STANDARD_SATDELI', 'UPS_DELI_MT_AP_STANDARD_SATDELI',
                'UPS_DELI_MT_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPEDITED', 'UPS_DELI_MT_AP_EXPEDITED', 'UPS_DELI_MT_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPRESS_SAVER', 'UPS_DELI_MT_AP_EXPRESS_SAVER',
                'UPS_DELI_MT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPRESS', 'UPS_DELI_MT_AP_EXPRESS', 'UPS_DELI_MT_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPRESS_SATDELI', 'UPS_DELI_MT_AP_EXPRESS_SATDELI',
                'UPS_DELI_MT_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('MT', 'AP', 'UPS_SP_SERV_MT_AP_EXPRESS_PLUS', 'UPS_DELI_MT_AP_EXPRESS_PLUS',
                'UPS_DELI_MT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_STANDARD', 'UPS_DELI_MT_ADD_STANDARD', 'UPS_DELI_MT_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_STANDARD_SATDELI', 'UPS_DELI_MT_ADD_STANDARD_SATDELI',
                'UPS_DELI_MT_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPEDITED', 'UPS_DELI_MT_ADD_EXPEDITED',
                'UPS_DELI_MT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPRESS_SAVER', 'UPS_DELI_MT_ADD_EXPRESS_SAVER',
                'UPS_DELI_MT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPRESS', 'UPS_DELI_MT_ADD_EXPRESS', 'UPS_DELI_MT_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPRESS_SATDELI', 'UPS_DELI_MT_ADD_EXPRESS_SATDELI',
                'UPS_DELI_MT_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('MT', 'ADD', 'UPS_SP_SERV_MT_ADD_EXPRESS_PLUS', 'UPS_DELI_MT_ADD_EXPRESS_PLUS',
                'UPS_DELI_MT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('PT', 'AP', 'UPS_SP_SERV_PT_AP_STANDARD', 'UPS_DELI_PT_AP_STANDARD', 'UPS_DELI_PT_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('PT', 'AP', 'UPS_SP_SERV_PT_AP_STANDARD_SATDELI', 'UPS_DELI_PT_AP_STANDARD_SATDELI',
                'UPS_DELI_PT_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPEDITED', 'UPS_DELI_PT_AP_EXPEDITED', 'UPS_DELI_PT_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPRESS_SAVER', 'UPS_DELI_PT_AP_EXPRESS_SAVER',
                'UPS_DELI_PT_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPRESS', 'UPS_DELI_PT_AP_EXPRESS', 'UPS_DELI_PT_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPRESS_SATDELI', 'UPS_DELI_PT_AP_EXPRESS_SATDELI',
                'UPS_DELI_PT_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('PT', 'AP', 'UPS_SP_SERV_PT_AP_EXPRESS_PLUS', 'UPS_DELI_PT_AP_EXPRESS_PLUS',
                'UPS_DELI_PT_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_STANDARD', 'UPS_DELI_PT_ADD_STANDARD', 'UPS_DELI_PT_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_STANDARD_SATDELI', 'UPS_DELI_PT_ADD_STANDARD_SATDELI',
                'UPS_DELI_PT_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPEDITED', 'UPS_DELI_PT_ADD_EXPEDITED',
                'UPS_DELI_PT_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPRESS_SAVER', 'UPS_DELI_PT_ADD_EXPRESS_SAVER',
                'UPS_DELI_PT_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPRESS', 'UPS_DELI_PT_ADD_EXPRESS', 'UPS_DELI_PT_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPRESS_SATDELI', 'UPS_DELI_PT_ADD_EXPRESS_SATDELI',
                'UPS_DELI_PT_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('PT', 'ADD', 'UPS_SP_SERV_PT_ADD_EXPRESS_PLUS', 'UPS_DELI_PT_ADD_EXPRESS_PLUS',
                'UPS_DELI_PT_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('RO', 'AP', 'UPS_SP_SERV_RO_AP_STANDARD', 'UPS_DELI_RO_AP_STANDARD', 'UPS_DELI_RO_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('RO', 'AP', 'UPS_SP_SERV_RO_AP_STANDARD_SATDELI', 'UPS_DELI_RO_AP_STANDARD_SATDELI',
                'UPS_DELI_RO_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPEDITED', 'UPS_DELI_RO_AP_EXPEDITED', 'UPS_DELI_RO_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPRESS_SAVER', 'UPS_DELI_RO_AP_EXPRESS_SAVER',
                'UPS_DELI_RO_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPRESS', 'UPS_DELI_RO_AP_EXPRESS', 'UPS_DELI_RO_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPRESS_SATDELI', 'UPS_DELI_RO_AP_EXPRESS_SATDELI',
                'UPS_DELI_RO_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('RO', 'AP', 'UPS_SP_SERV_RO_AP_EXPRESS_PLUS', 'UPS_DELI_RO_AP_EXPRESS_PLUS',
                'UPS_DELI_RO_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_STANDARD', 'UPS_DELI_RO_ADD_STANDARD', 'UPS_DELI_RO_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_STANDARD_SATDELI', 'UPS_DELI_RO_ADD_STANDARD_SATDELI',
                'UPS_DELI_RO_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPEDITED', 'UPS_DELI_RO_ADD_EXPEDITED',
                'UPS_DELI_RO_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPRESS_SAVER', 'UPS_DELI_RO_ADD_EXPRESS_SAVER',
                'UPS_DELI_RO_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPRESS', 'UPS_DELI_RO_ADD_EXPRESS', 'UPS_DELI_RO_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPRESS_SATDELI', 'UPS_DELI_RO_ADD_EXPRESS_SATDELI',
                'UPS_DELI_RO_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('RO', 'ADD', 'UPS_SP_SERV_RO_ADD_EXPRESS_PLUS', 'UPS_DELI_RO_ADD_EXPRESS_PLUS',
                'UPS_DELI_RO_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('SK', 'AP', 'UPS_SP_SERV_SK_AP_STANDARD', 'UPS_DELI_SK_AP_STANDARD', 'UPS_DELI_SK_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('SK', 'AP', 'UPS_SP_SERV_SK_AP_STANDARD_SATDELI', 'UPS_DELI_SK_AP_STANDARD_SATDELI',
                'UPS_DELI_SK_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPEDITED', 'UPS_DELI_SK_AP_EXPEDITED', 'UPS_DELI_SK_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPRESS_SAVER', 'UPS_DELI_SK_AP_EXPRESS_SAVER',
                'UPS_DELI_SK_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPRESS', 'UPS_DELI_SK_AP_EXPRESS', 'UPS_DELI_SK_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPRESS_SATDELI', 'UPS_DELI_SK_AP_EXPRESS_SATDELI',
                'UPS_DELI_SK_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('SK', 'AP', 'UPS_SP_SERV_SK_AP_EXPRESS_PLUS', 'UPS_DELI_SK_AP_EXPRESS_PLUS',
                'UPS_DELI_SK_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_STANDARD', 'UPS_DELI_SK_ADD_STANDARD', 'UPS_DELI_SK_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_STANDARD_SATDELI', 'UPS_DELI_SK_ADD_STANDARD_SATDELI',
                'UPS_DELI_SK_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPEDITED', 'UPS_DELI_SK_ADD_EXPEDITED',
                'UPS_DELI_SK_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPRESS_SAVER', 'UPS_DELI_SK_ADD_EXPRESS_SAVER',
                'UPS_DELI_SK_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPRESS', 'UPS_DELI_SK_ADD_EXPRESS', 'UPS_DELI_SK_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPRESS_SATDELI', 'UPS_DELI_SK_ADD_EXPRESS_SATDELI',
                'UPS_DELI_SK_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('SK', 'ADD', 'UPS_SP_SERV_SK_ADD_EXPRESS_PLUS', 'UPS_DELI_SK_ADD_EXPRESS_PLUS',
                'UPS_DELI_SK_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('SI', 'AP', 'UPS_SP_SERV_SI_AP_STANDARD', 'UPS_DELI_SI_AP_STANDARD', 'UPS_DELI_SI_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('SI', 'AP', 'UPS_SP_SERV_SI_AP_STANDARD_SATDELI', 'UPS_DELI_SI_AP_STANDARD_SATDELI',
                'UPS_DELI_SI_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPEDITED', 'UPS_DELI_SI_AP_EXPEDITED', 'UPS_DELI_SI_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPRESS_SAVER', 'UPS_DELI_SI_AP_EXPRESS_SAVER',
                'UPS_DELI_SI_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPRESS', 'UPS_DELI_SI_AP_EXPRESS', 'UPS_DELI_SI_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPRESS_SATDELI', 'UPS_DELI_SI_AP_EXPRESS_SATDELI',
                'UPS_DELI_SI_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('SI', 'AP', 'UPS_SP_SERV_SI_AP_EXPRESS_PLUS', 'UPS_DELI_SI_AP_EXPRESS_PLUS',
                'UPS_DELI_SI_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_STANDARD', 'UPS_DELI_SI_ADD_STANDARD', 'UPS_DELI_SI_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_STANDARD_SATDELI', 'UPS_DELI_SI_ADD_STANDARD_SATDELI',
                'UPS_DELI_SI_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPEDITED', 'UPS_DELI_SI_ADD_EXPEDITED',
                'UPS_DELI_SI_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPRESS_SAVER', 'UPS_DELI_SI_ADD_EXPRESS_SAVER',
                'UPS_DELI_SI_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPRESS', 'UPS_DELI_SI_ADD_EXPRESS', 'UPS_DELI_SI_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPRESS_SATDELI', 'UPS_DELI_SI_ADD_EXPRESS_SATDELI',
                'UPS_DELI_SI_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('SI', 'ADD', 'UPS_SP_SERV_SI_ADD_EXPRESS_PLUS', 'UPS_DELI_SI_ADD_EXPRESS_PLUS',
                'UPS_DELI_SI_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('SE', 'AP', 'UPS_SP_SERV_SE_AP_STANDARD', 'UPS_DELI_SE_AP_STANDARD', 'UPS_DELI_SE_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('SE', 'AP', 'UPS_SP_SERV_SE_AP_STANDARD_SATDELI', 'UPS_DELI_SE_AP_STANDARD_SATDELI',
                'UPS_DELI_SE_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPEDITED', 'UPS_DELI_SE_AP_EXPEDITED', 'UPS_DELI_SE_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPRESS_SAVER', 'UPS_DELI_SE_AP_EXPRESS_SAVER',
                'UPS_DELI_SE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPRESS', 'UPS_DELI_SE_AP_EXPRESS', 'UPS_DELI_SE_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPRESS_SATDELI', 'UPS_DELI_SE_AP_EXPRESS_SATDELI',
                'UPS_DELI_SE_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('SE', 'AP', 'UPS_SP_SERV_SE_AP_EXPRESS_PLUS', 'UPS_DELI_SE_AP_EXPRESS_PLUS',
                'UPS_DELI_SE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_STANDARD', 'UPS_DELI_SE_ADD_STANDARD', 'UPS_DELI_SE_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_STANDARD_SATDELI', 'UPS_DELI_SE_ADD_STANDARD_SATDELI',
                'UPS_DELI_SE_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPEDITED', 'UPS_DELI_SE_ADD_EXPEDITED',
                'UPS_DELI_SE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPRESS_SAVER', 'UPS_DELI_SE_ADD_EXPRESS_SAVER',
                'UPS_DELI_SE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPRESS', 'UPS_DELI_SE_ADD_EXPRESS', 'UPS_DELI_SE_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPRESS_SATDELI', 'UPS_DELI_SE_ADD_EXPRESS_SATDELI',
                'UPS_DELI_SE_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('SE', 'ADD', 'UPS_SP_SERV_SE_ADD_EXPRESS_PLUS', 'UPS_DELI_SE_ADD_EXPRESS_PLUS',
                'UPS_DELI_SE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('CH', 'AP', 'UPS_SP_SERV_CH_AP_STANDARD', 'UPS_DELI_CH_AP_STANDARD', 'UPS_DELI_CH_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('CH', 'AP', 'UPS_SP_SERV_CH_AP_STANDARD_SATDELI', 'UPS_DELI_CH_AP_STANDARD_SATDELI',
                'UPS_DELI_CH_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPEDITED', 'UPS_DELI_CH_AP_EXPEDITED', 'UPS_DELI_CH_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPRESS_SAVER', 'UPS_DELI_CH_AP_EXPRESS_SAVER',
                'UPS_DELI_CH_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPRESS', 'UPS_DELI_CH_AP_EXPRESS', 'UPS_DELI_CH_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPRESS_SATDELI', 'UPS_DELI_CH_AP_EXPRESS_SATDELI',
                'UPS_DELI_CH_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('CH', 'AP', 'UPS_SP_SERV_CH_AP_EXPRESS_PLUS', 'UPS_DELI_CH_AP_EXPRESS_PLUS',
                'UPS_DELI_CH_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_STANDARD', 'UPS_DELI_CH_ADD_STANDARD', 'UPS_DELI_CH_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_STANDARD_SATDELI', 'UPS_DELI_CH_ADD_STANDARD_SATDELI',
                'UPS_DELI_CH_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPEDITED', 'UPS_DELI_CH_ADD_EXPEDITED',
                'UPS_DELI_CH_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPRESS_SAVER', 'UPS_DELI_CH_ADD_EXPRESS_SAVER',
                'UPS_DELI_CH_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPRESS', 'UPS_DELI_CH_ADD_EXPRESS', 'UPS_DELI_CH_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPRESS_SATDELI', 'UPS_DELI_CH_ADD_EXPRESS_SATDELI',
                'UPS_DELI_CH_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('CH', 'ADD', 'UPS_SP_SERV_CH_ADD_EXPRESS_PLUS', 'UPS_DELI_CH_ADD_EXPRESS_PLUS',
                'UPS_DELI_CH_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('RS', 'AP', 'UPS_SP_SERV_RS_AP_STANDARD', 'UPS_DELI_RS_AP_STANDARD', 'UPS_DELI_RS_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('RS', 'AP', 'UPS_SP_SERV_RS_AP_STANDARD_SATDELI', 'UPS_DELI_RS_AP_STANDARD_SATDELI',
                'UPS_DELI_RS_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPEDITED', 'UPS_DELI_RS_AP_EXPEDITED', 'UPS_DELI_RS_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPRESS_SAVER', 'UPS_DELI_RS_AP_EXPRESS_SAVER',
                'UPS_DELI_RS_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPRESS', 'UPS_DELI_RS_AP_EXPRESS', 'UPS_DELI_RS_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPRESS_SATDELI', 'UPS_DELI_RS_AP_EXPRESS_SATDELI',
                'UPS_DELI_RS_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('RS', 'AP', 'UPS_SP_SERV_RS_AP_EXPRESS_PLUS', 'UPS_DELI_RS_AP_EXPRESS_PLUS',
                'UPS_DELI_RS_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_STANDARD', 'UPS_DELI_RS_ADD_STANDARD', 'UPS_DELI_RS_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_STANDARD_SATDELI', 'UPS_DELI_RS_ADD_STANDARD_SATDELI',
                'UPS_DELI_RS_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPEDITED', 'UPS_DELI_RS_ADD_EXPEDITED',
                'UPS_DELI_RS_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPRESS_SAVER', 'UPS_DELI_RS_ADD_EXPRESS_SAVER',
                'UPS_DELI_RS_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPRESS', 'UPS_DELI_RS_ADD_EXPRESS', 'UPS_DELI_RS_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPRESS_SATDELI', 'UPS_DELI_RS_ADD_EXPRESS_SATDELI',
                'UPS_DELI_RS_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('RS', 'ADD', 'UPS_SP_SERV_RS_ADD_EXPRESS_PLUS', 'UPS_DELI_RS_ADD_EXPRESS_PLUS',
                'UPS_DELI_RS_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('NO', 'AP', 'UPS_SP_SERV_NO_AP_STANDARD', 'UPS_DELI_NO_AP_STANDARD', 'UPS_DELI_NO_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('NO', 'AP', 'UPS_SP_SERV_NO_AP_STANDARD_SATDELI', 'UPS_DELI_NO_AP_STANDARD_SATDELI',
                'UPS_DELI_NO_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPEDITED', 'UPS_DELI_NO_AP_EXPEDITED', 'UPS_DELI_NO_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPRESS_SAVER', 'UPS_DELI_NO_AP_EXPRESS_SAVER',
                'UPS_DELI_NO_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPRESS', 'UPS_DELI_NO_AP_EXPRESS', 'UPS_DELI_NO_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPRESS_SATDELI', 'UPS_DELI_NO_AP_EXPRESS_SATDELI',
                'UPS_DELI_NO_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('NO', 'AP', 'UPS_SP_SERV_NO_AP_EXPRESS_PLUS', 'UPS_DELI_NO_AP_EXPRESS_PLUS',
                'UPS_DELI_NO_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_STANDARD', 'UPS_DELI_NO_ADD_STANDARD', 'UPS_DELI_NO_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_STANDARD_SATDELI', 'UPS_DELI_NO_ADD_STANDARD_SATDELI',
                'UPS_DELI_NO_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPEDITED', 'UPS_DELI_NO_ADD_EXPEDITED',
                'UPS_DELI_NO_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPRESS_SAVER', 'UPS_DELI_NO_ADD_EXPRESS_SAVER',
                'UPS_DELI_NO_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPRESS', 'UPS_DELI_NO_ADD_EXPRESS', 'UPS_DELI_NO_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPRESS_SATDELI', 'UPS_DELI_NO_ADD_EXPRESS_SATDELI',
                'UPS_DELI_NO_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('NO', 'ADD', 'UPS_SP_SERV_NO_ADD_EXPRESS_PLUS', 'UPS_DELI_NO_ADD_EXPRESS_PLUS',
                'UPS_DELI_NO_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('TR', 'AP', 'UPS_SP_SERV_TR_AP_STANDARD', 'UPS_DELI_TR_AP_STANDARD', 'UPS_DELI_TR_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('TR', 'AP', 'UPS_SP_SERV_TR_AP_STANDARD_SATDELI', 'UPS_DELI_TR_AP_STANDARD_SATDELI',
                'UPS_DELI_TR_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPEDITED', 'UPS_DELI_TR_AP_EXPEDITED', 'UPS_DELI_TR_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPRESS_SAVER', 'UPS_DELI_TR_AP_EXPRESS_SAVER',
                'UPS_DELI_TR_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPRESS', 'UPS_DELI_TR_AP_EXPRESS', 'UPS_DELI_TR_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPRESS_SATDELI', 'UPS_DELI_TR_AP_EXPRESS_SATDELI',
                'UPS_DELI_TR_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('TR', 'AP', 'UPS_SP_SERV_TR_AP_EXPRESS_PLUS', 'UPS_DELI_TR_AP_EXPRESS_PLUS',
                'UPS_DELI_TR_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_STANDARD', 'UPS_DELI_TR_ADD_STANDARD', 'UPS_DELI_TR_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_STANDARD_SATDELI', 'UPS_DELI_TR_ADD_STANDARD_SATDELI',
                'UPS_DELI_TR_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPEDITED', 'UPS_DELI_TR_ADD_EXPEDITED',
                'UPS_DELI_TR_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPRESS_SAVER', 'UPS_DELI_TR_ADD_EXPRESS_SAVER',
                'UPS_DELI_TR_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPRESS', 'UPS_DELI_TR_ADD_EXPRESS', 'UPS_DELI_TR_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPRESS_SATDELI', 'UPS_DELI_TR_ADD_EXPRESS_SATDELI',
                'UPS_DELI_TR_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('TR', 'ADD', 'UPS_SP_SERV_TR_ADD_EXPRESS_PLUS', 'UPS_DELI_TR_ADD_EXPRESS_PLUS',
                'UPS_DELI_TR_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('JE', 'AP', 'UPS_SP_SERV_JE_AP_STANDARD', 'UPS_DELI_JE_AP_STANDARD', 'UPS_DELI_JE_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', '68', '0', ''),
            ('JE', 'AP', 'UPS_SP_SERV_JE_AP_STANDARD_SATDELI', 'UPS_DELI_JE_AP_STANDARD_SATDELI',
                'UPS_DELI_JE_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPEDITED', 'UPS_DELI_JE_AP_EXPEDITED', 'UPS_DELI_JE_AP_EXPEDITED_VAL',
                'UPS Expedited', '08', '05', '0', '&reg;'),
            ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPRESS_SAVER', 'UPS_DELI_JE_AP_EXPRESS_SAVER',
                'UPS_DELI_JE_AP_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPRESS', 'UPS_DELI_JE_AP_EXPRESS', 'UPS_DELI_JE_AP_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPRESS_SATDELI', 'UPS_DELI_JE_AP_EXPRESS_SATDELI',
                'UPS_DELI_JE_AP_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('JE', 'AP', 'UPS_SP_SERV_JE_AP_EXPRESS_PLUS', 'UPS_DELI_JE_AP_EXPRESS_PLUS',
                'UPS_DELI_JE_AP_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_STANDARD', 'UPS_DELI_JE_ADD_STANDARD', 'UPS_DELI_JE_ADD_STANDARD_VAL',
                'UPS&reg; Standard', '11', '25', '0', ''),
            ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_STANDARD_SATDELI', 'UPS_DELI_JE_ADD_STANDARD_SATDELI',
                'UPS_DELI_JE_ADD_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPEDITED', 'UPS_DELI_JE_ADD_EXPEDITED',
                'UPS_DELI_JE_ADD_EXPEDITED_VAL', 'UPS Expedited', '08', '05', '0', '&reg;'),
            ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPRESS_SAVER', 'UPS_DELI_JE_ADD_EXPRESS_SAVER',
                'UPS_DELI_JE_ADD_EXPRESS_SAVER_VAL', 'UPS Express Saver', '65', '26', '0', '&reg;'),
            ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPRESS', 'UPS_DELI_JE_ADD_EXPRESS', 'UPS_DELI_JE_ADD_EXPRESS_VAL',
                'UPS Express', '07', '24', '0', '&reg;'),
            ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPRESS_SATDELI', 'UPS_DELI_JE_ADD_EXPRESS_SATDELI',
                'UPS_DELI_JE_ADD_EXPRESS_SATDELI_VAL', 'UPS Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('JE', 'ADD', 'UPS_SP_SERV_JE_ADD_EXPRESS_PLUS', 'UPS_DELI_JE_ADD_EXPRESS_PLUS',
                'UPS_DELI_JE_ADD_EXPRESS_PLUS_VAL', 'UPS Express Plus', '54', '23', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_GROUND', 'UPS_DELI_US_AP_GROUND', 'UPS_DELI_US_AP_GROUND_VAL',
                'UPS&reg; Ground', '03', 'NULL', '0', ''),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_3DAY_SELECT', 'UPS_DELI_US_AP_3DAY_SELECT',
                'UPS_DELI_US_AP_3DAY_SELECT_VAL', 'UPS 3 Day Select', '12', 'NULL', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_2ND_DAY_AIR', 'UPS_DELI_US_AP_2ND_DAY_AIR',
                'UPS_DELI_US_AP_2ND_DAY_AIR_VAL', 'UPS 2nd Day Air', '02', 'NULL', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_2ND_DAY_AIR_AM', 'UPS_DELI_US_AP_2ND_DAY_AIR_AM',
                'UPS_DELI_US_AP_2ND_DAY_AIR_AM_VAL', 'UPS 2nd Day Air A.M', '59', 'NULL', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_NEXT_DAY_AIR_SAVER', 'UPS_DELI_US_AP_NEXT_DAY_AIR_SAVER',
                'UPS_DELI_US_AP_NEXT_DAY_AIR_SAVER_VAL', 'UPS Next Day Air Saver', '13', '', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_NEXT_DAY_AIR', 'UPS_DELI_US_AP_NEXT_DAY_AIR',
                'UPS_DELI_US_AP_NEXT_DAY_AIR_VAL', 'UPS Next Day Air', '01', 'NULL', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_NEXT_DAY_AIR_EARLY', 'UPS_DELI_US_AP_NEXT_DAY_AIR_EARLY',
                'UPS_DELI_US_AP_NEXT_DAY_AIR_EARLY_VAL', 'UPS Next Day Air&reg; Early', '14', '', '0', ''),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_STANDARD', 'UPS_DELI_US_AP_STANDARD', 'UPS_DELI_US_AP_STANDARD_VAL',
                'UPS&reg; Standard', '11', 'NULL', '0', ''),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_STANDARD_SATDELI', 'UPS_DELI_US_AP_STANDARD_SATDELI',
                'UPS_DELI_US_AP_STANDARD_SATDELI_VAL', 'UPS&reg; Standard - Saturday Delivery', '11', 'NULL', '0', ''),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_EXPEDITED', 'UPS_DELI_US_AP_WW_EXPEDITED',
                'UPS_DELI_US_AP_WW_EXPEDITED_VAL', 'UPS Worldwide Expedited', '08', 'NULL', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_SAVER', 'UPS_DELI_US_AP_WW_SAVER', 'UPS_DELI_US_AP_WW_SAVER_VAL',
                'UPS Worldwide Saver', '65', 'NULL', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_EXPRESS', 'UPS_DELI_US_AP_WW_EXPRESS',
                'UPS_DELI_US_AP_WW_EXPRESS_VAL', 'UPS Worldwide Express', '07', 'NULL', '0', '&reg;'),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_EXPRESS_SATDELI', 'UPS_DELI_US_AP_WW_EXPRESS_SATDELI',
                'UPS_DELI_US_AP_WW_EXPRESS_SATDELI_VAL', 'UPS Worldwide Express&reg; - Saturday Delivery', '07', 'NULL', '0', ''),
            ('US', 'AP', 'UPS_SP_SERV_US_AP_WW_EXPRESS_PLUS', 'UPS_DELI_US_AP_WW_EXPRESS_PLUS',
                'UPS_DELI_US_AP_WW_EXPRESS_PLUS_VAL', 'UPS Worldwide Express Plus', '54', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_GROUND', 'UPS_DELI_US_ADD_GROUND', 'UPS_DELI_US_ADD_GROUND_VAL',
                'UPS&reg; Ground', '03', 'NULL', '0', ''),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_3DAY_SELECT', 'UPS_DELI_US_ADD_3DAY_SELECT',
                'UPS_DELI_US_ADD_3DAY_SELECT_VAL', 'UPS 3 Day Select', '12', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_2ND_DAY_AIR', 'UPS_DELI_US_ADD_2ND_DAY_AIR',
                'UPS_DELI_US_ADD_2ND_DAY_AIR_VAL', 'UPS 2nd Day Air', '02', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_2ND_DAY_AIR_AM', 'UPS_DELI_US_ADD_2ND_DAY_AIR_AM',
                'UPS_DELI_US_ADD_2ND_DAY_AIR_AM_VAL', 'UPS 2nd Day Air A.M', '59', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_NEXT_DAY_AIR_SAVER', 'UPS_DELI_US_ADD_NEXT_DAY_AIR_SAVER',
                'UPS_DELI_US_ADD_NEXT_DAY_AIR_SAVER_VAL', 'UPS Next Day Air Saver', '13', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_NEXT_DAY_AIR', 'UPS_DELI_US_ADD_NEXT_DAY_AIR',
                'UPS_DELI_US_ADD_NEXT_DAY_AIR_VAL', 'UPS Next Day Air', '01', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_NEXT_DAY_AIR_EARLY', 'UPS_DELI_US_ADD_NEXT_DAY_AIR_EARLY',
                'UPS_DELI_US_ADD_NEXT_DAY_AIR_EARLY_VAL', 'UPS Next Day Air&reg; Early', '14', 'NULL', '0', ''),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_STANDARD', 'UPS_DELI_US_ADD_STANDARD',
                'UPS_DELI_US_ADD_STANDARD_VAL', 'UPS&reg; Standard', '11', 'NULL', '0', ''),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_WW_EXPEDITED', 'UPS_DELI_US_ADD_WW_EXPEDITED',
                'UPS_DELI_US_ADD_WW_EXPEDITED_VAL', 'UPS Worldwide Expedited', '08', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_WW_SAVER', 'UPS_DELI_US_ADD_WW_SAVER',
                'UPS_DELI_US_ADD_WW_SAVER_VAL', 'UPS Worldwide Saver', '65', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_WW_EXPRESS', 'UPS_DELI_US_ADD_WW_EXPRESS',
                'UPS_DELI_US_ADD_WW_EXPRESS_VAL', 'UPS Worldwide Express', '07', 'NULL', '0', '&reg;'),
            ('US', 'ADD', 'UPS_SP_SERV_US_ADD_WW_EXPRESS_PLUS', 'UPS_DELI_US_ADD_WW_EXPRESS_PLUS',
                'UPS_DELI_US_ADD_WW_EXPRESS_PLUS_VAL', 'UPS Worldwide Express Plus', '54', 'NULL', '0', '&reg;')
            ON DUPLICATE KEY UPDATE `service_key_val` = values(`service_key_val`);";
        $this->db->query($sql14);
        //end
        //region license
        //sql15
        $sql15 = $this->_create_table_exist . DB_PREFIX . "upsmodule_shipping_license` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `AccessLicenseText` longtext COMMENT 'Api Access 1',
            `LanguageCode` varchar(5) DEFAULT NULL COMMENT 'LanguageCode',
            `Username` varchar(127) DEFAULT NULL COMMENT 'Api Account 1',
            `Password` varchar(127) DEFAULT NULL COMMENT 'Api Account 1',
            `AccessLicenseNumber` varchar(127) DEFAULT NULL COMMENT 'Api Access 2',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='upsmodule_shipping_license'; ";
        $this->db->query($sql15);
        //end
        //region log api
        //sql16
        $sql16 = $this->_create_table_exist . DB_PREFIX . "upsmodule_shipping_logs_api` (
                `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `method` varchar(255) DEFAULT NULL COMMENT 'Method',
                `full_uri` varchar(255) DEFAULT NULL COMMENT 'URI',
                `request` text COMMENT 'Request',
                `response` text COMMENT 'Response',
                `time_request` datetime DEFAULT NULL COMMENT 'Time request',
                `time_response` datetime DEFAULT NULL COMMENT 'Time response',
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='upsmodule_shipping_logs_api'; ";
        $this->db->query($sql16);
        //end
        //region add setting
        //sql17
        $sql17 = $this->_insert_into . DB_PREFIX . "setting` (`code`, `key`, `value`, `serialized`) VALUES ('upsmodule',
            'module_upsmodule_status', '1', '0');";
        $this->db->query($sql17);
        //end
        //region log send data API
        //end
        //region add order_status
        //check_status
        $check_status = "SELECT os.order_status_id FROM `" . DB_PREFIX . "order_status` os where os.`name` =
            'Delivered' AND `language_id` = 1;";
        $find = $this->db->query($check_status)->row;
        //check order_status_id
        if (empty($find['order_status_id'])) {
            $sql18
                = $this->_insert_into . DB_PREFIX . "order_status` (`language_id`, `name`) VALUES ('1', 'Delivered');";
            $this->db->query($sql18);
        }
        //end
        //region add log
        //sql19
        $sql19 = $this->_create_table_exist . DB_PREFIX . "upsmodule_shipping_logs_api_mamage` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `key_api` varchar(255) DEFAULT NULL,
                `method_name` varchar(255) DEFAULT NULL,
                `data_api` text DEFAULT NULL,
                `count_retry` int(11) DEFAULT NULL,
                `date_created` datetime DEFAULT NULL,
                `date_update` datetime DEFAULT NULL,
                PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
            ";
        $this->db->query($sql19);
        //end
        $this->upgradeTablesForPackageDimension();
    }

    private function upgradeTablesForPackageDimension()
    {
        // Add column package_item to table upsmodule_package_default
        $has_package_item_column = $this->db->query("SHOW COLUMNS FROM `" . DB_PREFIX . "upsmodule_package_default` WHERE Field='package_item'")->num_rows > 0;
        if (!$has_package_item_column) {
            $this->db->query("ALTER TABLE " . DB_PREFIX . "upsmodule_package_default 
                            ADD COLUMN package_item int(11) DEFAULT NULL COMMENT 'Package Item'");
        }
        // Add column package to table upsmodule_open_orders
        $has_package_item_column = $this->db->query("SHOW COLUMNS FROM `" . DB_PREFIX . "upsmodule_open_orders` WHERE Field='package'")->num_rows > 0;
        if (!$has_package_item_column) {
            $this->db->query("ALTER TABLE " . DB_PREFIX . "upsmodule_open_orders 
                            ADD COLUMN package text DEFAULT NULL COMMENT 'Shipping Package'
                            AFTER accessorial_service");
        }
        // Create new table for product dimension
        $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "upsmodule_product_dimension` (
            `package_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'Package ID',
            `package_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Package Name',
            `weight` float(10,2) DEFAULT NULL COMMENT 'Weight',
            `unit_weight` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Unit weight',
            `length` float(10,2) DEFAULT NULL COMMENT 'Length',
            `width` float(10,2) DEFAULT NULL COMMENT 'Width',
            `height` float(10,2) DEFAULT NULL COMMENT 'Height',
            `unit_dimension` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Unit dimension',
            PRIMARY KEY (`package_id`)
        ) DEFAULT COLLATE=utf8_general_ci;");
        // Create new table for backup rate
        $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "upsmodule_fallback_rates` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `service_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Service Type',
            `service_id` int(11) DEFAULT NULL COMMENT 'Service ID',
            `fallback_rate` float DEFAULT NULL COMMENT 'Fallback Rate',
            PRIMARY KEY (`id`)
        ) DEFAULT COLLATE=utf8_general_ci;");
    }
    /**
     * ModelExtensionModuleUpsmodule generateRandomString
     * Create key Merchant
     * @author: UPS <<EMAIL>>
     *
     * @return $random_string
     */
    function generateRandomString()
    {
        //characters
        $characters = '0123456789abcdef';
        //strlen($characters)
        $characters_length = strlen($characters);
        $sub = '-';
        //1
        $random_string = '';
        //check $i
        for ($i = 0; $i < 8; $i++) {
            //randomString
            $random_string .= $characters[rand(0, $characters_length - 1)];
        }
        //2
        $random_string .= $sub;
        //check $i
        for ($i = 0; $i < 4; $i++) {
            //randomString
            $random_string .= $characters[rand(0, $characters_length - 1)];
        }
        //3
        $random_string .= $sub;
        //check $i
        for ($i = 0; $i < 4; $i++) {
            //randomString
            $random_string .= $characters[rand(0, $characters_length - 1)];
        }
        //4
        $random_string .= $sub;
        //check $i
        for ($i = 0; $i < 4; $i++) {
            //randomString
            $random_string .= $characters[rand(0, $characters_length - 1)];
        }
        //5
        $random_string .= $sub;
        //check $i
        for ($i = 0; $i < 12; $i++) {
            //randomString
            $random_string .= $characters[rand(0, $characters_length - 1)];
        }
        return $random_string;
    }

    /**
     * ModelExtensionModuleUpsmodule getGroupId
     * @author: UPS <<EMAIL>>
     *
     * @return $user_group_id
     */
    public function getGroupId()
    {
        //user_group_id
        $user_group_id = $this->user->getId();
        return $user_group_id;
    }

    /**
     * ModelExtensionModuleUpsmodule deleteEvent
     * @author: UPS <<EMAIL>>
     *
     * @param string $code //The code
     *
     * @return $user_group_id
     */
    public function deleteEvent($code)
    {
        //check VERSION
        if (VERSION >= '3.0.0.0') {
            //load->model('setting/event')
            $this->load->model('setting/event');
            //model_setting_event->deleteEventByCode($code)
            return $this->model_setting_event->deleteEventByCode($code);
        } elseif (VERSION > '2.0.0.0') {
            //model('extension/event')
            $this->load->model('extension/event');
            //model_extension_event->deleteEvent($code)
            return $this->model_extension_event->deleteEvent($code);
        } else {
            //sql
            $this->db->query("DELETE FROM " . DB_PREFIX . "event WHERE `code` = '" . $this->db->escape($code) . "'");
        }
    }

    /**
     * ModelExtensionModuleUpsmodule deleteDatabase
     * @author: UPS <<EMAIL>>
     *
     * @return $user_group_id
     */
    public function deleteDatabase()
    {
        //region Example
        //setting
        $this->db->query(
            "DELETE FROM `" . DB_PREFIX . "setting` WHERE `code` = 'upsmodule' and `key` = 'module_upsmodule_status';"
        );
        //news
        $this->db->query($this->_drop_table . DB_PREFIX . "news`");
        //news_description
        $this->db->query($this->_drop_table . DB_PREFIX . "news_description`");
        //end
        //region UPS
        //upsmodule_setting
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_setting`");
        //upsmodule_account
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_account`");
        //upsmodule_account_description
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_account_description`");
        //upsmodule_accessorial
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_accessorial`");
        //upsmodule_package_default
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_package_default`");
        //upsmodule_product_dimension
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_product_dimension`");
        //upsmodule_delivery_rates
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_delivery_rates`");
        //upsmodule_fallback_rates
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_fallback_rates`");
        //upsmodule_shipping_services
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_shipping_services`");
        //upsmodule_open_orders
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_open_orders`");
        //upsmodule_shipping_license
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_shipping_license`");
        //upsmodule_shipping_shipments
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_shipping_shipments`");
        //upsmodule_shipping_tracking
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_shipping_tracking`");
        //upsmodule_shipping_logs_api
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_shipping_logs_api`");
        //upsmodule_shipping_logs_api_mamage
        $this->db->query($this->_drop_table . DB_PREFIX . "upsmodule_shipping_logs_api_mamage`");
        //end
    }
}
