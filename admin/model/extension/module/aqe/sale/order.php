<?php
include_once(modification(DIR_APPLICATION . 'model/extension/module/aqe/general.php'));

class ModelExtensionModuleAqeSaleOrder extends ModelExtensionModuleAqeGeneral {
	protected static $count = 0;

	public function getOrders($data = array()) {
		if (isset($data['columns'])) {
			$columns = $data['columns'];
		} else {
			$columns = array('order_id', 'customer', 'status', 'total', 'date_added', 'date_modified');
		}

		$sql = "SELECT SQL_CALC_FOUND_ROWS o.*";

		if (in_array("customer", $columns)) {
			$sql .= ", CONCAT(o.firstname, ' ', o.lastname) AS customer";
		}

		if (in_array("status", $columns)) {
			$sql .= ", os.name AS status";
		}

		$sql .= "  FROM `" . DB_PREFIX . "order` o";

		if (in_array("status", $columns)) {
			$sql .= "  LEFT JOIN " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id AND os.language_id = '" . (int)$this->config->get('config_language_id') . "')";
		}

		$where = array();

		$int_filters = array(
			'order_id'              => 'o.order_id',
			'status'                => 'o.order_status_id',
			'payment_country_id'    => 'o.payment_country_id',
			'shipping_country_id'   => 'o.shipping_country_id',
		);

		foreach ($int_filters as $key => $value) {
			if (isset($data["filter_$key"]) && !is_null($data["filter_$key"])) {
				$where[] = "$value = '" . (int)$data["filter_$key"] . "'";
			}
		}

		if (isset($data['filter_status']) && !is_null($data['filter_status'])) {
			$where[] = "o.order_status_id = '" . (int)$data["filter_status"] . "'";
		} elseif (!empty($data['filter_order_statuses'])) {
			$implode = array();

			foreach ($data['filter_order_statuses'] as $order_status_id) {
				$implode[] = "o.order_status_id = '" . (int)$order_status_id . "'";
			}

			if ($implode) {
				$where[] = "(" . implode(" OR ", $implode) . ")";
			}
		} else {
			$where[] = "o.order_status_id > '0'";
		}

		$float_interval_filters = array(
			'total'             => 'o.total',
		);

		foreach ($float_interval_filters as $key => $value) {
			if (isset($data["filter_$key"]) && !is_null($data["filter_$key"])) {
				if ($this->config->get('module_admin_quick_edit_interval_filter')) {
					$where[] = $this->filterInterval($data["filter_$key"], $value);
				} else {
					$where[] = "$value = '" . (int)$data["filter_$key"] . "'";
				}
			}
		}

		$date_filters = array(
			'date_added'        => 'o.date_added',
			'date_modified'     => 'o.date_modified',
		);

		foreach ($date_filters as $key => $value) {
			if (isset($data["filter_$key"]) && !is_null($data["filter_$key"])) {
				if ($this->config->get('module_admin_quick_edit_interval_filter')) {
					$where[] = $this->filterInterval($this->db->escape($data["filter_$key"]), $value, true);
				} else {
					$where[] = "DATE($value) = DATE('" . $this->db->escape($data["filter_$key"]) . "')";
				}
			}
		}

		$anywhere_filters = array(
			'customer'          => "CONCAT(o.firstname, ' ', o.lastname)",
			'shipping_method'   => 'o.shipping_method',
			'payment_method'    => 'o.payment_method',
		);

		foreach ($anywhere_filters as $key => $value) {
			if (!empty($data["filter_$key"])) {
				if ($this->config->get('module_admin_quick_edit_match_anywhere')) {
					$tokens = preg_split("/\s+/", trim($data["filter_$key"]));

					foreach ($tokens as $token) {
						$where[] = "$value LIKE '%" . $this->db->escape($token) . "%'";
					}
				} else {
					$where[] = "$value LIKE '" . $this->db->escape($data["filter_$key"]) . "%'";
				}
			}
		}

		if ($where) {
			$sql .= " WHERE " . implode(" AND ", $where);
		}

		$sql .= " GROUP BY o.order_id";

		$sort_data = array(
			'o.order_id',
			'customer',
			'o.payment_country',
			'o.payment_method',
			'o.shipping_country',
			'o.shipping_method',
			'o.total',
			'status',
			'o.date_added',
			'o.date_modified',
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY o.order_id";
		}

		if (isset($data['order']) && ($data['order'] == 'ASC')) {
			$sql .= " ASC";
		} else {
			$sql .= " DESC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		$count = $this->db->query("SELECT FOUND_ROWS() AS count");
		$this->count = ($count->num_rows) ? (int)$count->row['count'] : 0;

		return $query->rows;
	}

	public function getTotalOrders() {
		return $this->count;
	}

	public function quickEditOrder($order_id, $column, $value, $lang_id=null, $data=null) {
		$editable = array('order_id', 'customer_id', 'customer', 'email', 'telephone', 'product_id', 'product', 'model', 'quantity', 'return_reason', 'opened', 'comment', 'return_action', 'return_status', 'date_ordered');
		$result = false;
		// if (in_array($column, $editable)) {
		// 	if (in_array($column, array('email', 'telephone', 'product', 'model', 'comment', 'date_ordered')))
		// 		$result = $this->db->query("UPDATE `" . DB_PREFIX . "return` SET " . $column . " = '" . $this->db->escape($value) . "', date_modified = NOW() WHERE return_id = '" . (int)$return_id . "'");
		// 	else if (in_array($column, array('order_id', 'product_id', 'customer_id', 'quantity', 'opened'))) {
		// 		if ($column == 'quantity' && strpos(trim($value), "#") === 0 && preg_match('/^#\s*(?P<operator>[+-\/\*])\s*(?P<operand>-?\d+\.?\d*)(?P<percent>%)?$/', trim($value), $matches) === 1) {
		// 			list($operator, $operand) = $this->parseExpression($matches);
		// 			$result = $this->db->query("UPDATE `" . DB_PREFIX . "return` SET `$column` = `$column` $operator '" . (float)$operand . "', date_modified = NOW() WHERE return_id = '" . (int)$return_id . "'");
		// 			$query = $this->db->query("SELECT `$column` FROM `" . DB_PREFIX . "return` WHERE return_id = '" . (int)$return_id . "'");
		// 			$result = $query->row[$column];
		// 		} else {
		// 			$result = $this->db->query("UPDATE `" . DB_PREFIX . "return` SET `" . $column . "` = '" . (int)$value . "', date_modified = NOW() WHERE return_id = '" . (int)$return_id . "'");
		// 		}
		// 	} else if (in_array($column, array('return_reason', 'return_action', 'return_status')))
		// 		$result = $this->db->query("UPDATE `" . DB_PREFIX . "return` SET " . $column . "_id = '" . (int)$value . "', date_modified = NOW() WHERE return_id = '" . (int)$return_id . "'");
		// 	else if ($column == "customer") {
		// 		$first_name = $data['first_name'];
		// 		$last_name = $data['last_name'];
		// 		$result = $this->db->query("UPDATE `" . DB_PREFIX . "return` SET firstname = '" . $this->db->escape($first_name) . "', lastname = '" . $this->db->escape($last_name) . "', date_modified = NOW() WHERE return_id = '" . (int)$return_id . "'");
		// 	}
		// }

		return $result;
	}
}
