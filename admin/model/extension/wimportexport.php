<?php
class ModelExtensionWimportexport extends Model {
	
	public function CreateTable(){
		$this->db->query("CREATE TABLE IF NOT EXISTS `".DB_PREFIX."excel_import_export` (`excel_import_export_id` int(11) NOT NULL AUTO_INCREMENT,`type` varchar(28) NOT NULL,`name` varchar(250) NOT NULL,`tablename` varchar(250) NOT NULL,`status` tinyint(4) NOT NULL,  `user_id` int(11) NOT NULL,`formdata` text NOT NULL,`cron_job_link` varchar(250) NOT NULL,`date_added` datetime NOT NULL,`date_modified` datetime NOT NULL, PRIMARY KEY (`excel_import_export_id`))");
	}
	
    public function getColumns($tablevalue, $columns = false) {
		$fields = array();
		$customfields = array();
		$defaultcolumns = $this->model_extension_wimportexport->defaulttablefields($tablevalue);
		if($this->config->get($tablevalue.'_table')){
			$query = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX .$tablevalue);
			foreach($query->rows as $row){
				if(in_array($row['Field'],$defaultcolumns)){
					$fields[] = array(
						'name' => ucfirst($tablevalue).' - '.ucfirst(str_replace('_',' ',$row['Field'])),
						'value' => $tablevalue.'s'.'.'.$row['Field'],
						'column' => $row['Field'],
						'fname' => ucfirst(str_replace('_',' ',$row['Field'])),
					);
				}elseif(!in_array($row['Field'],$defaultcolumns)){
					$customfields[] = array(
						'name' => ucfirst($tablevalue).' - '.ucfirst(str_replace('_',' ',$row['Field'])),
						'value' => $tablevalue.'s'.'.'.$row['Field'],
						'column' => $row['Field'],
						'fname' => ucfirst(str_replace('_',' ',$row['Field'])),
					);
				}
			}
			$totaltable = count($this->config->get($tablevalue.'_table')) - 1;
			$tablecolumn = $this->config->get($tablevalue.'_table');
			foreach($tablecolumn as $key => $value){
				if($key != 0){
					$querys = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX .$value);
					foreach($querys->rows as $rows){
						if(in_array($rows['Field'],$defaultcolumns)){
							$fields[] = array(
								'name' => ucfirst(str_replace('_',' ',$value)).' - '.ucfirst(str_replace('_',' ',$rows['Field'])),
								'value' => $value.'s'.'.'.$rows['Field'],
								'column' => $rows['Field'],
								'fname' => ucfirst(str_replace('_',' ',$rows['Field'])),
							);
						}elseif(!in_array($rows['Field'],$defaultcolumns)){
							$customfields[] = array(
								'name' => ucfirst(str_replace('_',' ',$value)).' - '.ucfirst(str_replace('_',' ',$rows['Field'])),
								'value' => $value.'s'.'.'.$rows['Field'],
								'column' => $rows['Field'],
								'fname' => ucfirst(str_replace('_',' ',$rows['Field'])),
							);
						}
					}
				}
				
			}
		}else{
			$query = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX .$tablevalue);
			
			foreach($query->rows as $row){
				if(in_array($row['Field'],$defaultcolumns)){
					$fields[] = array(
						'name' => ucfirst(str_replace('_',' ',$tablevalue)).' - '.$row['Field'],
						'value' => $tablevalue.'s'.'.'.$row['Field'],
						'column' => $row['Field'],
						'fname' => ucfirst(str_replace('_',' ',$row['Field'])),
					);
				}elseif(!in_array($row['Field'],$defaultcolumns)){
					$customfields[] = array(
						'name' => ucfirst($tablevalue).' - '.ucfirst(str_replace('_',' ',$row['Field'])),
						'value' => $tablevalue.'s'.'.'.$row['Field'],
						'column' => $row['Field'],
						'fname' => ucfirst(str_replace('_',' ',$row['Field'])),
					);
				}
			}
		}
		
		$fielddata = array(
			'fields' => $fields,
			'customfields' => $customfields,
		);
		
		return $fielddata;
    }
	
	public function addcase($data,$type){
		$this->db->query("INSERT INTO " . DB_PREFIX . "excel_import_export SET type = '" . $type . "',name = '" . $this->db->escape($data['case_name']) . "',tablename = '" . $data['export_table'] . "',status = '" . $data['status'] . "',user_id = '" . $data['user'] . "', formdata = '" . $this->db->escape(json_encode($data)) . "',date_added = NOW(),date_modified = NOW()");
	}
	
	public function editcase($data,$excel_id,$type){
		$this->db->query("UPDATE " . DB_PREFIX . "excel_import_export SET type = '" . $type . "',name = '" . $this->db->escape($data['case_name']) . "',tablename = '" . $data['export_table'] . "',status = '" . $data['status'] . "',user_id = '" . $data['user'] . "', formdata = '" . $this->db->escape(json_encode($data)) . "',date_modified = NOW() WHERE excel_import_export_id = '" . (int)$excel_id . "'");
	}
	
	public function updatelink($link,$excel_id){
		$this->db->query("UPDATE " . DB_PREFIX . "excel_import_export SET cron_job_link = '" . $this->db->escape($link) . "',date_modified = NOW() WHERE excel_import_export_id = '" . (int)$excel_id . "'");
	}
	
	public function deletecase($excel_id){
		$this->db->query("DELETE FROM " . DB_PREFIX . "excel_import_export WHERE excel_import_export_id = '" . (int)$excel_id . "'");
	}
	
	public function defaulttablefields($table){
		$data['product'] = array('product_id','model','sku','upc','ean','jan','isbn','mpn','location','quantity','stock_status_id','image','manufacturer_id','shipping','price','points','tax_class_id','date_available','weight','weight_class_id','length','width','height','length_class_id','subtract','minimum','sort_order','status','viewed','date_added','date_modified','language_id','name','description','tag','meta_title','meta_description','meta_keyword','store_id');
		
		$data['category'] = array('category_id','image','parent_id','top','column','sort_order','status','date_added','date_modified','language_id','name','description','meta_title','meta_description','meta_keyword','store_id');
		
		$data['manufacturer'] = array('manufacturer_id','image','sort_order','name','store_id');
		
		$data['product_special'] = array('product_special_id','product_id','customer_group_id','priority','price','date_start','date_end');
		
		$data['product_discount'] = array('product_discount_id','product_id','customer_group_id','quantity','priority','price','date_start','date_end');
		
		$data['product_attribute'] = array('attribute_id','product_id','language_id','text');
		
		$data['attribute'] = array('attribute_id','attribute_group_id','sort_order','language_id','name');
		
		$data['attribute_group'] = array('attribute_group_id','sort_order','language_id','name');
		
		$data['option'] = array('option_id','type','sort_order','language_id','name');
		
		$data['option_value'] = array('option_value_id','option_id','image','sort_order','language_id','name');
		
		$data['filter'] = array('filter_id','filter_group_id','sort_order','language_id','name');
		
		$data['filter_group'] = array('filter_group_id','sort_order','language_id','name');
		
		$data['product_option'] = array('product_option_id','product_id','option_id','value','required','product_option_value_id','option_value_id','quantity','subtract','price','price_prefix','points','points_prefix','weight','weight_prefix');
		
		$data['customer'] = array('customer_id','customer_group_id','store_id','language_id','firstname','lastname','email','telephone','fax','password','salt','cart','wishlist','newsletter','image','address_id','custom_field','ip','status','safe','token','code','date_added','file');
		
		$data['customer_group'] = array('customer_group_id','approval','sort_order','language_id','name','description');
		
		$data['order'] = array('order_id','invoice_no','invoice_prefix','store_id','store_name','store_url','customer_id','customer_group_id','firstname','lastname','email','telephone','fax','custom_field','payment_firstname','payment_lastname','payment_company','payment_address_1','payment_address_2','payment_city','payment_postcode','payment_country','payment_country_id','payment_zone','payment_zone_id','payment_address_format','payment_custom_field','payment_method','payment_code','shipping_firstname','shipping_lastname','shipping_company','shipping_address_1','shipping_address_2','shipping_city','shipping_postcode','shipping_country','shipping_country_id','shipping_zone','shipping_zone_id','shipping_address_format','shipping_custom_field','shipping_method','shipping_code','comment','total','order_status_id','affiliate_id','commission','marketing_id','tracking','language_id','currency_id','currency_code','currency_value','ip','forwarded_ip','user_agent','accept_language','date_added','date_modified');
		
		$data['order_product'] = array('order_product_id','order_id','product_id','name','model','quantity','price','total','tax','reward');
		
		$data['order_option'] = array('order_option_id','order_id','order_product_id','product_option_id','product_option_value_id','name','value','type');
		
		$data['order_total'] = array('order_total_id','order_id','code','title','value','sort_order');
		
		return isset($data[$table]) ? $data[$table] : array();
	}
	
	public function getCases($data = array()){
		$sql = "SELECT * FROM " . DB_PREFIX . "excel_import_export WHERE excel_import_export_id > 0";
		
		if (!empty($data['filter_name'])) {
			$sql .= " AND name LIKE '" . $this->db->escape($data['filter_name']) . "%'";
		}
		
		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND status = '" . (int)$data['filter_status'] . "'";
		}
		
		if (isset($data['filter_user']) && $data['filter_user'] !== '') {
			$sql .= " AND user_id = '" . (int)$data['filter_user'] . "'";
		}
		
		if (isset($data['filter_table']) && $data['filter_table'] !== '') {
			$sql .= " AND tablename = '" . $data['filter_table'] . "'";
		}
		
		$sql .= " ORDER BY excel_import_export_id DESC";

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}
		
		$query = $this->db->query($sql);

		return $query->rows;
	}
	
	public function getCase($id){
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "excel_import_export WHERE excel_import_export_id = '". $id ."'");
		
		return $query->row;
	}

	public function getTotalCases($data= array()){
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "excel_import_export WHERE excel_import_export_id > 0";
		
		if (!empty($data['filter_name'])) {
			$sql .= " AND name LIKE '" . $this->db->escape($data['filter_name']) . "%'";
		}
		
		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND status = '" . (int)$data['filter_status'] . "'";
		}
		
		if (isset($data['filter_user']) && $data['filter_user'] !== '') {
			$sql .= " AND user_id = '" . (int)$data['filter_user'] . "'";
		}
		
		if (isset($data['filter_table']) && $data['filter_table'] !== '') {
			$sql .= " AND tablename = '" . $data['filter_table'] . "'";
		}
		
		$query = $this->db->query($sql);
		
		return $query->row['total'];
	}
	
	public function getData($data) {
		$tablename = $data['export_table'];
		$joins = $this->config->get($tablename.'_table');
		$defaultcolumns = $this->model_extension_wimportexport->defaulttablefields($tablename);
		if($defaultcolumns){
			foreach($defaultcolumns as $key => $column){
				if($key == 0){
					$unique_id = $column;
				}
			}
		}else{
			$columns = $this->model_extension_wimportexport->getMigratecolumns($tablename);
			foreach($columns as $key => $column){
				if($key == 0){
					$unique_id = $column['Field'];
				}
			}
		}
		
		
		$sql = "SELECT * ,".$tablename.'s'.'.'.$unique_id." FROM " . DB_PREFIX .$tablename.' '.$tablename.'s';
		
		if($joins){
			foreach($joins as $key => $join){
				if($key !=0){
					$sql .= " LEFT JOIN ".DB_PREFIX.$join.' '.$join.'s'. " ON (".$tablename.'s'.".".$unique_id."=".$join.'s'.".".$unique_id.")";
				}
			}
		}
		
		if(!empty($data['filter_categories']) && $tablename == 'product'){
			$sql .= " LEFT JOIN ".DB_PREFIX."product_to_category p2c ON (".$tablename."s.".$unique_id."= p2c.".$unique_id.")";
		}
		
		if(!empty($data['filter'])){
			$sql .= " WHERE";
			$fi = 0;
			foreach($data['filter'] as $filter){
				if($fi){
					$sql .= ' '.(isset($filter['condition']) ? $filter['condition'] : '').' '.$filter['column'].' '.html_entity_decode($filter['operation']).' "'. $filter['value'] . '"';
				}else{
					$sql .= ' '.$filter['column'].' '.html_entity_decode($filter['operation']).' "'. $filter['value'] . '"';
				}
				$fi++;
			}
		}
		
		if(!empty($data['filter'])){
			if(isset($data['filter_language_id']) && $tablename == 'product' || $tablename == 'option' || $tablename == 'option_value'
			|| $tablename == 'product_attribute' || $tablename == 'attribute' || $tablename == 'attribute_group' || $tablename == 'filter' || $tablename == 'filter_group' || $tablename == 'customer_group'){
				$sql .= " AND ".$tablename."_descriptions.language_id = '".$data['filter_language_id']."'";
			}elseif(isset($data['filter_store']) && $tablename == 'product'){
				$sql .= " AND product_to_stores.store_id = '".$data['filter_store']."'";
			}elseif((isset($data['filter_store']) || isset($data['filter_language_id'])) && $tablename == 'category'){
				$sql .= " AND category_to_stores.store_id = '".$data['filter_store']."' AND category_descriptions.language_id = '".$data['filter_language_id']."'";
			}
		}else{
			if(isset($data['filter_language_id']) && $tablename == 'product' || $tablename == 'option' || $tablename == 'option_value' || $tablename == 'attribute' || $tablename == 'attribute_group' || $tablename == 'filter' || $tablename == 'filter_group' || $tablename == 'customer_group'){
				$sql .= " WHERE ".$tablename."_descriptions.language_id = '".$data['filter_language_id']."'";
			}elseif(isset($data['filter_language_id']) && $tablename == 'product_attribute'){
				$sql .= " WHERE ".$tablename."s.language_id = '".$data['filter_language_id']."'";
			}elseif(isset($data['filter_store']) && $tablename == 'product'){
				$sql .= " AND product_to_stores.store_id = '".$data['filter_store']."'";
			}elseif((isset($data['filter_store']) || isset($data['filter_language_id'])) && $tablename == 'category'){
				$sql .= " WHERE category_to_stores.store_id = '".$data['filter_store']."' AND category_descriptions.language_id = '".$data['filter_language_id']."'";
			}
		}
		
		if(!empty($data['filter_categories']) && $tablename == 'product'){
			$sql .= " AND (";
			foreach($data['filter_categories'] as $k => $category_id){
				if($k!=0){
					$sql .= " OR";
				}
				 $sql .="  p2c.category_id = '".$category_id."'";
			}
			$sql .=" )";
		}
		
		if(!empty($data['filter_manufacturer']) && $tablename == 'product'){
			$sql .= " AND (";
			foreach($data['filter_manufacturer'] as $k => $manufacturer_id){
				if($k!=0){
					$sql .= " OR";
				}
				 $sql .= $tablename."s.manufacturer_id = '".$manufacturer_id."'";
			}
			$sql .=" )";
		}
		
		$sql .= " GROUP BY ".$tablename.'s'.".".$unique_id;
		
		$sql .= " ORDER BY ".$data['sort_order_field']." ".strtoupper($data['sort_order_mode']);

		$query = $this->db->query($sql);
		
		return $query->rows;
		
	}

	public function toNum($num, $code = '') {
		$alphabets = array('', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z');

		$division = floor($num / 26);
	
		$remainder = $num % 26; 

		if($remainder == 0)
		{
			$division = $division - 1;
			$code .= 'z';
		}else{
			$code .= $alphabets[$remainder];
		}
		
		if($division > 26)
			return $this->toNum($division, $code);   
		else
			$code .= $alphabets[$division];     
		
		return strrev($code);
	}

	 public function setData($sheetarray,$formdata){
		$new = '';
		$update = '';
		$delete = '';
		$tablename = $formdata['export_table'];
		$tables = $this->config->get($tablename.'_table');
		if(!$tables){
			$tables[] = $tablename;
		}
	
		if($tablename == 'product'){
			$unique_id = $formdata['import_type'];
		}else{
			$defaultcolumns = $this->model_extension_wimportexport->defaulttablefields($tablename);
			if($defaultcolumns){
				foreach($defaultcolumns as $key => $column){
					if($key == 0){
						$unique_id = $column;
					}
				}
			}else{
				$columns = $this->model_extension_wimportexport->getMigratecolumns($tablename);
				foreach($columns as $key => $column){
					if($key == 0){
						$unique_id = $column['Field'];
					}
				}
			}
		}
		
		if(isset($formdata['filter']) && !empty($formdata['filter'])){
			foreach($formdata['filter'] as $filter){
				$gettable = explode(".",$filter['column']);
				$operator = html_entity_decode($filter['operator']);
				if(!empty($filter['value'])){
					switch ($operator) {
						case '>=':
						$condition = $sheetarray[$gettable[1]] >= $filter['value'];
						break;
						
						case '=':
						$condition = $sheetarray[$gettable[1]] == $filter['value'];
						break;
						
						case '>':
						$condition = $sheetarray[$gettable[1]] > $filter['value'];
						break;
						
						case '<':
						$condition = $sheetarray[$gettable[1]] < $filter['value'];
						break;
						
						case '<=':
						$condition = $sheetarray[$gettable[1]] <= $filter['value'];
						break;
					}
					
					$last_id = 0;

					foreach($tables as $key => $table){
						$query = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX .$table);
						$columns = array();
						foreach($query->rows as $row){
							$columns[] = $row['Field'];
						}
						$imagen = '';
						if(isset($sheetarray['name']) && !empty($sheetarray['name'])){
							$imagen = str_replace(' ','_',$sheetarray['name']);
						}
						if($filter['action'] == '1'){
							if($condition){
								$sql = "UPDATE " . DB_PREFIX .$table." SET ";
								$j = 0;
								foreach($sheetarray as $column => $value){
									if(in_array($column,$columns)){
										$j++;
										if($j != 1){
											$sql .= ", ";
										}
										
										if($column == 'language_id'){
											$value = $formdata['filter_language_id'];
										}
										if($column == 'store_id'){
											$value = $formdata['filter_store_id'];
										}
										if($column == 'image'){
											if(!empty($value)){
											  $image = str_replace('?dl=0','?raw=1',$value);
											  $mainimage = $this->fetchingimage($image,$imagen);	
											  $sql .= "`".$column . "` = '". $this->db->escape($mainimage)."'";
											}else{
												$sql .= "`".$column . "` = '". $this->db->escape('')."'";
											}
										}else{
											$sql .= "`".$column . "` = '". $this->db->escape($value)."'";
										}
									}
								}
								if(in_array('date_modified',$columns)){
									$sql .= ",date_modified = NOW()";
								}
								$sql .= " WHERE ".$gettable[1]." = ".$sheetarray[$gettable[1]];
								$this->db->query($sql);
								$update = 'Update '.$tablename.' '.$sheetarray[$gettable[1]];
							}else{
								if($sheetarray[$gettable[1]] == ''){
									$sql = "INSERT INTO " . DB_PREFIX .$table." SET ";
									$j = 0;
									foreach($sheetarray as $column => $value){
										if(in_array($column,$columns)){
											$j++;
											if($j != 1){
												$sql .= ", ";
											}
											
											if($column == 'language_id'){
												$value = $formdata['filter_language_id'];
											}
											if($column == 'store_id'){
												$value = $formdata['filter_store_id'];
											}
											if($unique_id == $column && $last_id != 0){
												$sql .= "`".$column . "` = '". $last_id."'";
											}else{
												if($column == 'image'){
													if(!empty($value)){
													  $image = str_replace('?dl=0','?raw=1',$value);
													  $mainimage = $this->fetchingimage($image,$imagen);	
													  $sql .= "`".$column . "` = '". $this->db->escape($mainimage)."'";
													}else{
														$sql .= "`".$column . "` = '". $this->db->escape('')."'";
													}
												}else{
													$sql .= "`".$column . "` = '". $this->db->escape($value)."'";
												}
											}
										}
									}
									if(!array_key_exists('date_added', $sheetarray) && in_array('date_added',$columns)){
										$sql .= ",date_added = NOW()";
									}
									if(!array_key_exists('date_modified', $sheetarray) && in_array('date_modified',$columns)){
										$sql .= ",date_modified = NOW()";
									}
									
									$this->db->query($sql);
									if($key < 1){
										$last_id += $this->db->getLastId();
										$new = 'New '.$tablename.' '.$last_id;
									}
								}
							}
						}elseif($filter['action'] == '2'){
							$delete = 'Delete '.$tablename.' '.$sheetarray[$gettable[1]];
							if(in_array($gettable[1],$columns)){
								$this->db->query("DELETE FROM " . DB_PREFIX . $table." WHERE ".$gettable[1].$operator."'" . $sheetarray[$gettable[1]] . "'");
							}
						}
					}
				}
			}
		}else{
			$last_id = 0;
			
			foreach($tables as $key => $table){
				$query = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX .$table);
				$columns = array();
				foreach($query->rows as $row){
					$columns[] = $row['Field'];
				}
				
				$checksql = "SELECT * FROM " . DB_PREFIX . $table." WHERE ".$unique_id." = '" . $sheetarray[$unique_id] . "'";
				if(in_array('language_id',$columns) && $table != 'customer'){
					$checksql .= " AND language_id = ".$formdata['filter_language_id'];
				}
				
				$checkquery = $this->db->query($checksql)->row;
				
				if($checkquery){
					$sql = "UPDATE " . DB_PREFIX .$table." SET ";
				}else{
					$sql = "INSERT INTO " . DB_PREFIX .$table." SET ";
				}
				$j = 0;
				
				$imagen = '';
				if(isset($sheetarray['name']) && !empty($sheetarray['name'])){
					$imagen = str_replace(' ','_',$sheetarray['name']);
				}
				foreach($sheetarray as $column => $value){
					if(in_array($column,$columns)){
						$j++;
						
						if($j != 1){
							$sql .= ", ";
						}
						if($column == 'language_id'){
							$value = $formdata['filter_language_id'];
						}
						if($column == 'store_id'){
							$value = $formdata['filter_store'];
						}
						if($unique_id == $column && $last_id != 0){
							$sql .= "`".$column . "` = '". $last_id."'";
						}else{
							if($column == 'date_added' && empty($sheetarray['date_added'])){
								$sql .= "date_added = NOW()";
							}elseif($column == 'date_modified' && empty($sheetarray['date_modified'])){
								$sql .= "date_modified = NOW()";
							}else{
								if($column == 'image'){
									if(!empty($value)){
									  $image = str_replace('?dl=0','?raw=1',$value);
									  $mainimage = $this->fetchingimage($image,$imagen);	
									  $sql .= "`".$column . "` = '". $this->db->escape($mainimage)."'";
									}else{
										$sql .= "`".$column . "` = '". $this->db->escape('')."'";
									}
								}else{
									$sql .= "`".$column . "` = '". $this->db->escape($value)."'";
								}
							}
						}
					}
				}
				
				if(!array_key_exists('date_added', $sheetarray) && in_array('date_modified',$columns)){
					$sql .= ", date_added = NOW()";
				}
				
				if(!array_key_exists('date_modified', $sheetarray) && in_array('date_modified',$columns)){
					$sql .= ",date_modified = NOW()";
				}
				
				if($checkquery){
					$sql .= " WHERE ".$unique_id." = ".$sheetarray[$unique_id];
					if(in_array('language_id',$columns) && $table != 'customer'){
						$sql .= " AND language_id = '". $formdata['filter_language_id']."'";
					}
					$update = 'Update '.$tablename.' '.$sheetarray[$unique_id];
				}
				
				$this->db->query($sql);
				if($key < 1){
					$last_id += $this->db->getLastId();
					if(!$checkquery){
						$new = 'New '.$tablename.' '.$last_id;
					}
				}
			}
		}
		
		if(!empty($new)){
			$result = array('new' => $new);
		}
		if(!empty($update)){
			$result = array('update' => $update);
		}
		if(!empty($delete)){
			$result = array('delete' => $delete);
		}
		
		return isset($result) ? $result : '';
	}
	
	public function getTables($tablename){
		$tables = array();
		$query = $this->db->query("SELECT TABLE_NAME FROM information_schema.TABLES WHERE table_schema='".DB_DATABASE."' AND TABLE_NAME LIKE '" . DB_PREFIX . $tablename. "%'");
		foreach($query->rows as $row){
			$table = str_replace(DB_PREFIX,'',$row['TABLE_NAME']);
			if(!in_array($table,$this->getCustomTables())){
				$tables[] = $table;
			}
		}
		return $tables;
	}
	
	public function getCustomdbTables($tablename){
		$customtables = array();
		$query = $this->db->query("SELECT TABLE_NAME FROM information_schema.TABLES WHERE table_schema='".DB_DATABASE."' AND TABLE_NAME LIKE '" . DB_PREFIX . $tablename. "%'");
		foreach($query->rows as $row){
			$table = str_replace(DB_PREFIX,'',$row['TABLE_NAME']);
			if(!in_array($table,$this->defaultDbtables())){
				$customtables[] = $table;
			}
		}
		return $customtables;
	}
	
	public function getCustomTables(){
		$customtables = array();
		$query = $this->db->query("SELECT TABLE_NAME FROM information_schema.TABLES WHERE table_schema='".DB_DATABASE."'");
		foreach($query->rows as $row){
			$table = str_replace(DB_PREFIX,'',$row['TABLE_NAME']);
			$customtables[] = $table;
		}
		return $customtables;
	}
	
	public function getMigratedata($tablename){
		$query = $this->db->query("SELECT * FROM ".DB_PREFIX .$tablename);
		
		return $query->rows;
	}
	
	public function getMigratecolumns($tablename){
		$query = $this->db->query("SHOW COLUMNS FROM ".DB_PREFIX .$tablename);
		
		return $query->rows;
	}
	
	public function importdb($sheetarray,$table){
		$temp_table = $table;
		if($table == 'url_alias'){
			$table = 'seo_url';
		}
		$dbtable = $this->db->query("SELECT TABLE_NAME FROM information_schema.TABLES WHERE table_schema='".DB_DATABASE."' AND TABLE_NAME ='" . DB_PREFIX . $table. "'");
		if($dbtable->row){
			$this->db->query("DELETE FROM  " . DB_PREFIX .$table);
			$query = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX .$table);
			$columns = array();
			foreach($query->rows as $row){
				$columns[] = $row['Field'];
			} 
			
			foreach($sheetarray as $sheetdata){
				if($temp_table == 'url_alias'){
					$sheetdata['language_id'] = $this->config->get('config_language_id');
					$sheetdata['store_id'] = 0;
				}
				
				$j = 0;
				$sql = "INSERT INTO " . DB_PREFIX .$table." SET ";
				foreach($sheetdata as $column => $value){
					if(in_array($column,$columns)){
						$j++;
						if($j != 1){
							$sql .= ", ";
						}
						if($temp_table == 'language' && $column == 'code' && $value == 'en'){
							$sql .= "`".$column . "` = '". $this->db->escape('en-gb')."'";
						}else{
							$sql .= "`".$column . "` = '". $this->db->escape($value)."'";
						}	
					}
				}
				
				$this->db->query($sql);
			}
		}
	}
	
	public function fetchingimage($image_url, $image_file){
		if(strpos($image_url,'https://') !== false){
			$image_url = str_replace ( ' ', '%20', $image_url);
			$imagename = preg_replace ('/[^\p{L}\p{N}]/u', '', $image_file);
			$pathinfo = pathinfo($image_url);
			if(!empty($pathinfo['extension'])){
			  $extension = $pathinfo['extension'];
			}else{
			  $extension = 'jpeg';
			}
			
			$path = DIR_IMAGE.'catalog/products/';
			if(!file_exists($path)){
			  mkdir($path);
			  chmod($path,0777);
			}
			
			$fp = fopen ($path.$imagename.'.'.$extension, 'wb');
			$ch = curl_init($image_url);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_FILE, $fp);
			curl_setopt($ch, CURLOPT_HEADER, 0);
			curl_exec($ch);
			curl_close($ch);                            
			fclose($fp); 
			return 'catalog/products/'.$imagename.'.'.$extension;
		}elseif(strpos($image_url,'http://') !== false){
			$image_url = str_replace ( ' ', '%20', $image_url);
			$pathinfo = pathinfo($image_url);
			if(!empty($pathinfo['extension'])){
			  $extension = $pathinfo['extension'];  			
			}else{
			  $extension = 'jpeg';
			}
			
			$path = DIR_IMAGE.'catalog/products/';
			if(!file_exists($path)){
			  mkdir($path);
			  chmod($path,0777);
			}
			$imagename = preg_replace ('/[^\p{L}\p{N}]/u', '', $image_file);
			$fp = fopen ($path.$imagename.'.'.$extension, 'wb');
			$ch = curl_init($image_url);
			curl_setopt($ch, CURLOPT_FILE, $fp);
			curl_setopt($ch, CURLOPT_HEADER, 0);
			curl_exec($ch);
			curl_close($ch); 
			fclose($fp); 
			return 'catalog/products/'.$imagename.'.'.$extension;
		}else{
			return $image_url;
		}
	}
	
	public function defaultDbtables(){
		$tables = array('address','advertise_google_target','api','api_ip','api_session','attribute','attribute_description','attribute_group','attribute_group_description','banner','banner_image','cart','category','category_description','category_filter','category_path','category_to_google_product_category','category_to_layout','category_to_store','country','coupon','coupon_category','coupon_history','coupon_product','currency','custom_field','custom_field_customer_group','custom_field_description','custom_field_value','custom_field_value_description','customer','customer_activity','customer_affiliate','customer_approval','customer_group','customer_group_description','customer_history','customer_ip','customer_login','customer_online','customer_reward','customer_search','customer_transaction','customer_wishlist','download','download_description','event','extension','extension_install','extension_path','filter','filter_description','filter_group','filter_group_description','geo_zone','information','information_description','information_to_layout','information_to_store','language','layout','layout_module','layout_route','length_class','length_class_description','location','manufacturer','manufacturer_to_store','marketing','modification','module','option','option_description','option_value','option_value_description','order','order_history','order_option','order_product','order_recurring','order_recurring_transaction','order_shipment','order_status','order_total','order_voucher','product','product_advertise_google','product_advertise_google_status','product_advertise_google_target','product_attribute','product_description','product_discount','product_filter','product_image','product_option','product_option_value','product_recurring','product_related','product_reward','product_special','product_to_category','product_to_download','product_to_layout','product_to_store','recurring','recurring_description','return','return_action','return_history','return_reason','return_status','review','seo_url','session','setting','shipping_courier','statistics','stock_status','store','tax_class','tax_rate','tax_rate_to_customer_group','tax_rule','theme','translation','upload','user','user_group','voucher','voucher_history','voucher_theme','voucher_theme_description','weight_class','weight_class_description','zone','zone_to_geo_zone','excel_import_export');
		
		return $tables;
	}
}
