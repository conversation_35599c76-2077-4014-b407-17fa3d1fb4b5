<?php
class ModelQbOpcQb extends Model {
	public function createTable() {
    $this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "qb_customer (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `oc_customer_id` int(11) NOT NULL,
      `qb_customer_id` int(11) NOT NULL,
      `qb_sync_token` int(11) NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");

    $this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "qb_product (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `oc_product_id` int(11) NOT NULL,
      `qb_product_id` int(11) NOT NULL,
      `qb_sync_token` int(11) NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");

    $this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "qb_order (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `oc_order_id` int(11) NOT NULL,
      `qb_order_id` int(11) NOT NULL,
      `qb_sync_token` int(11) NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");
	}

  public function dropTable() {
    $this->db->query("DROP TABLE IF EXISTS " . DB_PREFIX . "qb_customer");
    $this->db->query("DROP TABLE IF EXISTS " . DB_PREFIX . "qb_product");
    $this->db->query("DROP TABLE IF EXISTS " . DB_PREFIX . "qb_order");
	}
}
