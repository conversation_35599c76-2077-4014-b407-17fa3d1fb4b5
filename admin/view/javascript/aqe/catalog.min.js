/*Bootstrap overlapping modals*/
!function(e){e.fn.modal.Constructor.prototype.show=function(t){var s=this,o=e.Event("show.bs.modal",{relatedTarget:t});this.$element.trigger(o),this.isShown||o.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.$body.addClass("modal-open"),this.setScrollbar(),this.escape(),this.$element.on("click.dismiss.bs.modal",'.close[data-dismiss="modal"]:first',e.proxy(this.hide,this)),this.$element.on("click.dismiss.bs.modal",'.cancel[data-dismiss="modal"]:first',e.proxy(this.hide,this)),this.backdrop(function(){var o=e.support.transition&&s.$element.hasClass("fade");s.$element.parent().length||s.$element.appendTo(s.$body),s.$element.show().scrollTop(0),o&&s.$element[0].offsetWidth,s.$backdrop.css("z-index",1030+1*e(".modal.fade.in").length),s.$element.css("z-index",1040+1*e(".modal.fade.in").length).addClass("in").attr("aria-hidden",!1),s.enforceFocus();var n=e.Event("shown.bs.modal",{relatedTarget:t});o?s.$element.find(".modal-dialog").one("bsTransitionEnd",function(){s.$element.trigger("focus").trigger(n)}).emulateTransitionEnd(300):s.$element.trigger("focus").trigger(n)}))},e(document).on("hidden.bs.modal",".modal",function(){e(".modal.fade.in").length?e(document.body).addClass("modal-open"):e(document.body).removeClass("modal-open")})}(jQuery);
/*Form serialization*/
!function(e){e.fn.serializeObject=function(){var i=this,t={},a={},n={validate:/^[a-zA-Z][a-zA-Z0-9_]*(?:\[(?:\d*|[a-zA-Z0-9_]+)\])*$/,key:/[a-zA-Z0-9_]+|(?=\[\])/g,push:/^$/,fixed:/^\d+$/,named:/^[a-zA-Z0-9_]+$/};return this.build=function(e,i,t){return e[i]=t,e},this.push_counter=function(e){return void 0===a[e]&&(a[e]=0),a[e]++},e.each(e(this).serializeArray(),function(){if(n.validate.test(this.name)){for(var a,u=this.name.match(n.key),h=this.value,r=this.name;void 0!==(a=u.pop());)r=r.replace(new RegExp("\\["+a+"\\]$"),""),a.match(n.push)?h=i.build([],i.push_counter(r),h):a.match(n.named)?h=i.build({},a,h):a.match(n.fixed)&&(h=i.build([],a,h));t=e.extend(!0,t,h)}}),t}}(jQuery);
/*jQuery.jeditable*/
!function(t){"use strict";t.fn.editableAriaShim=function(){return this.attr({role:"button",tabindex:0}),this},t.fn.editable=function(e,a){if("disable"!==e)if("enable"!==e){if("destroy"!==e){var i=t.extend({},t.fn.editable.defaults,{target:e},a),n=t.editable.types[i.type].plugin||function(){},l=t.editable.types[i.type].submit||function(){},o=t.editable.types[i.type].buttons||t.editable.types.defaults.buttons,s=t.editable.types[i.type].content||t.editable.types.defaults.content,r=t.editable.types[i.type].element||t.editable.types.defaults.element,d=t.editable.types[i.type].reset||t.editable.types.defaults.reset,p=t.editable.types[i.type].destroy||t.editable.types.defaults.destroy,c=i.callback||function(){},u=i.intercept||function(t){return t},h=i.onedit||function(){},f=i.onsubmit||function(){},m=i.onreset||function(){},y=i.onerror||d;i.before;return i.tooltip&&t(this).attr("title",i.tooltip),this.each(function(){var a=this;t(this).data("event.editable",i.event),t(this).html().trim()||t(this).html(i.placeholder),"destroy"!==e?(t(this).on(i.event,function(e){if(!0!==t(this).data("disabled.editable")&&9!==e.which&&!a.editing&&!1!==h.apply(this,[i,a,e])){if(i.before&&"function"==typeof i.before)i.before(e);else if(i.before&&"function"!=typeof i.before)throw"The 'before' option needs to be provided as a function!";e.preventDefault(),e.stopPropagation(),i.tooltip&&t(a).removeAttr("title"),t(this).html().toLowerCase().replace(/(;|"|\/)/g,"")===i.placeholder.toLowerCase().replace(/(;|"|\/)/g,"")&&t(this).html(""),a.editing=!0,a.revert=i.html?t(a).html():t(a).text(),t(a).html("");var p=t("<form />");i.cssclass&&("inherit"===i.cssclass?p.attr("class",t(a).attr("class")):p.attr("class",i.cssclass)),i.style&&("inherit"===i.style?(p.attr("style",t(a).attr("style")),p.css("display",t(a).css("display"))):p.attr("style",i.style)),i.label&&p.append("<label>"+i.label+"</label>"),i.formid&&p.attr("id",i.formid);var m,b,v=r.apply(p,[i,a]);i.inputcssclass&&("inherit"===i.inputcssclass?v.attr("class",t(a).attr("class")):v.attr("class",i.inputcssclass));var g=!1;if(i.loadurl){b=a.setTimeout(function(){v.disabled=!0},100),t(a).html(i.loadtext);var x={};x[i.id]=a.id,"function"==typeof i.loaddata?t.extend(x,i.loaddata.apply(a,[a.revert,i])):t.extend(x,i.loaddata),t.ajax({type:i.loadtype,url:i.loadurl,data:x,async:!1,cache:!1,success:function(t){a.clearTimeout(b),m=t,v.disabled=!1}})}else i.data?(m=i.data,"function"==typeof i.data&&(m=i.data.apply(a,[a.revert,i]))):m=a.revert;if(s.apply(p,[m,i,a]),v.attr("name",i.name),"none"!==i.width){var w=i.width-(v.outerWidth(!0)-i.width);v.width(w)}o.apply(p,[i,a]),i.showfn&&"function"==typeof i.showfn&&p.hide(),t(a).html(""),t(a).append(p),i.showfn&&"function"==typeof i.showfn&&i.showfn(p),n.apply(p,[i,a]),p.find(":input:visible:enabled:first").trigger("focus"),i.select&&v.trigger("select"),t(this).on("keydown",function(t){27===t.which?(t.preventDefault(),d.apply(p,[i,a])):13==t.which&&t.shiftKey&&(t.preventDefault(),p.trigger("submit"))}),"cancel"===i.onblur?v.on("blur",function(t){b=a.setTimeout(function(){d.apply(p,[i,a])},500)}):"submit"===i.onblur?v.on("blur",function(t){b=a.setTimeout(function(){p.trigger("submit")},200)}):"function"==typeof i.onblur&&v.on("blur",function(t){!1===i.onblur.apply(a,[v.val(),i,p])&&d.apply(p,[i,a])}),p.on("submit",function(e){if(e.preventDefault(),e.stopPropagation(),g)return!1;if(g=!0,b&&a.clearTimeout(b),(g=!1!==f.apply(p,[i,a]))&&(g=!1!==l.apply(p,[i,a])))if("function"==typeof i.target){var n=function(e,n){g=!1,!1!==n&&(t(a).html(e),a.editing=!1,c.apply(a,[a.innerText,i]),t(a).html().trim()||t(a).html(i.placeholder))},o=i.target.apply(a,[v.val(),i,n]);!1!==o&&void 0!==o&&n(o,o)}else{var s={};s[i.name]=v.val(),s[i.id]=a.id,"function"==typeof i.submitdata?t.extend(s,i.submitdata.apply(a,[a.revert,i,s])):t.extend(s,i.submitdata),"PUT"===i.method&&(s._method="put"),t(a).html(i.indicator);var r={type:"POST",complete:function(t,e){g=!1},data:s,dataType:"html",url:i.target,success:function(e,n){e=u.apply(a,[e,n]),"html"===r.dataType&&t(a).html(e),a.editing=!1,c.apply(a,[e,i,s]),t(a).html().trim()||t(a).html(i.placeholder)},error:function(t,e,n){y.apply(p,[i,a,t])}};t.extend(r,i.ajaxoptions),t.ajax(r)}return t(a).attr("title",i.tooltip),!1})}}),a.reset=function(e){a.editing&&!1!==m.apply(e,[i,a])&&(i.html?t(a).html(a.revert):t(a).text(a.revert),a.editing=!1,t(a).html().trim()||t(a).html(i.placeholder),i.tooltip&&t(a).attr("title",i.tooltip))},a.destroy=function(e){t(a).off(t(a).data("event.editable")).removeData("disabled.editable").removeData("event.editable"),a.clearTimeouts(),a.editing&&d.apply(e,[i,a])},a.clearTimeout=function(e){var i=t(a).data("timeouts");if(clearTimeout(e),i){var n=i.indexOf(e);n>-1?(i.splice(n,1),i.length<=0&&t(a).removeData("timeouts")):console.warn("jeditable clearTimeout could not find timeout "+e)}},a.clearTimeouts=function(){var e=t(a).data("timeouts");if(e){for(var i=0,n=e.length;i<n;++i)clearTimeout(e[i]);e.length=0,t(a).removeData("timeouts")}},a.setTimeout=function(e,i){var n=t(a).data("timeouts"),l=setTimeout(function(){e(),a.clearTimeout(l)},i);return n||(n=[],t(a).data("timeouts",n)),n.push(l),l}):p.apply(t(this).find("form"),[i,a])})}t(this).off(t(this).data("event.editable")).removeData("disabled.editable").removeData("event.editable")}else t(this).data("disabled.editable",!1);else t(this).data("disabled.editable",!0)};var e=function(t){var e=document.createElement("input");return e.setAttribute("type",t),"text"!==e.type?t:"text"};t.editable={types:{defaults:{element:function(e,a){var i=t('<input type="hidden"></input>');return t(this).append(i),i},content:function(e,a,i){t(this).find(":input:first").val(e)},reset:function(t,e){e.reset(this)},destroy:function(t,e){e.destroy(this)},buttons:function(e,a){var i,n,l=this;(e.submit&&(e.submit.match(/>$/)?i=t(e.submit).on("click",function(){"submit"!==i.attr("type")&&l.trigger("submit")}):((i=t('<button type="submit" />')).html(e.submit),e.submitcssclass&&i.addClass(e.submitcssclass)),t(this).append(i)),e.cancel)&&(e.cancel.match(/>$/)?n=t(e.cancel):((n=t('<button type="cancel" />')).html(e.cancel),e.cancelcssclass&&n.addClass(e.cancelcssclass)),t(this).append(n),t(n).on("click",function(i){return("function"==typeof t.editable.types[e.type].reset?t.editable.types[e.type].reset:t.editable.types.defaults.reset).apply(l,[e,a]),!1}))}},text:{element:function(e,a){var i=t("<input />").attr({autocomplete:"off",list:e.list,maxlength:e.maxlength,pattern:e.pattern,placeholder:e.placeholder,tooltip:e.tooltip,type:"text"});return"none"!==e.width&&i.css("width",e.width),"none"!==e.height&&i.css("height",e.height),e.size&&i.attr("size",e.size),e.maxlength&&i.attr("maxlength",e.maxlength),t(this).append(i),i}},textarea:{element:function(e,a){var i=t("<textarea></textarea>");return e.rows?i.attr("rows",e.rows):"none"!==e.height&&i.height(e.height),e.cols?i.attr("cols",e.cols):"none"!==e.width&&i.width(e.width),e.maxlength&&i.attr("maxlength",e.maxlength),t(this).append(i),i}},select:{element:function(e,a){var i=t("<select />");return e.multiple&&i.attr("multiple","multiple"),t(this).append(i),i},content:function(e,a,i){var n;n=String===e.constructor?JSON.parse(e):e;var l,o,s=[];if(Array.isArray(n)&&n.every(Array.isArray))s=n,n={},s.forEach(function(t){n[t[0]]=t[1]});else for(l in n)s.push([l,n[l]]);a.sortselectoptions&&s.sort(function(t,e){return(t=t[1])<(e=e[1])?-1:t>e?1:0});for(var r=0;r<s.length;r++){l=s[r][0];var d=s[r][1];n.hasOwnProperty(l)&&("selected"!==l&&(o=t("<option />").val(l).append(d),n.selected!==l&&l!==String.prototype.trim.call(null==i.revert?"":i.revert)||t(o).prop("selected","selected"),t(this).find("select").append(o)))}if(!a.submit){var p=this;t(this).find("select").change(function(){p.trigger("submit")})}}},number:{element:function(a,i){var n=t("<input />").attr({maxlength:a.maxlength,placeholder:a.placeholder,min:a.min,max:a.max,step:a.step,tooltip:a.tooltip,type:e("number")});return"none"!==a.width&&n.css("width",a.width),t(this).append(n),n}},email:{element:function(a,i){var n=t("<input />").attr({maxlength:a.maxlength,placeholder:a.placeholder,tooltip:a.tooltip,type:e("email")});return"none"!==a.width&&n.css("width",a.width),t(this).append(n),n}},url:{element:function(a,i){var n=t("<input />").attr({maxlength:a.maxlength,pattern:a.pattern,placeholder:a.placeholder,tooltip:a.tooltip,type:e("url")});return"none"!==a.width&&n.css("width",a.width),t(this).append(n),n}}},addInputType:function(e,a){t.editable.types[e]=a}},t.fn.editable.defaults={name:"value",id:"id",type:"text",width:"auto",height:"auto",event:"click.editable keydown.editable",onblur:"cancel",tooltip:"Click to edit",loadtype:"GET",loadtext:"Loading...",placeholder:"Click to edit",sortselectoptions:!1,html:!1,loaddata:{},submitdata:{},ajaxoptions:{}}}(jQuery);
/*custom*/
!function(e,t,a){var n={error_ajax_request:"An AJAX error occured!"},i={success:"alert-success",error:"alert-danger",warning:"alert-warning",info:"alert-info"},l={success:"fa-check-circle",error:"fa-times-circle",warning:"fa-exclamation-triangle",info:"fa-info-circle"},r=t.unique;e.debugging=!1,e.user_token="",e.success_timeout=5e3,e.texts=void 0!==e.texts?t.extend(n,e.texts):n,e.alert_classes=void 0!==e.alert_classes?t.extend(i,e.alert_classes):i,e.alert_icons=void 0!==e.alert_icons?t.extend(l,e.alert_icons):l,Number.prototype.round=function(e){return+(Math.round(this+"e+"+e)+"e-"+e)},null==e.clear_alerts&&(e.clear_alerts=function(e){var a,n=e||t("#alerts");n.length&&(a=t.map(t(".alert",n),function(e){var a=t.Deferred();return t(e).alert("close").on("closed.bs.alert",function(){a.resolve()}),a.promise()}),t.when.apply(null,a))}),null==e.display_alert&&(e.display_alert=function(a,n,i,l,r){l=null!=l?l:1e4,r=null==r||r;var s,o=a||t("#alerts"),c=[];n&&o.length&&(r&&(c=t.map(t(".alert",o),function(e){var a=t.Deferred();return t(e).alert("close").on("closed.bs.alert",function(){a.resolve()}),a.promise()})),t.when.apply(null,c).done(function(){i in e.alert_classes||(i="error"),s=(s=t("<div/>",{class:"alert "+e.alert_classes[i]+" fade"}).html(n).prepend(t("<i/>",{class:"fa "+e.alert_icons[i]})).prepend(t("<button/>",{type:"button",class:"close","data-dismiss":"alert","aria-hidden":"true"}).html("&times;"))).appendTo(o),setTimeout(function(){s.addClass("in")},0),l&&setTimeout(function(){s.alert("close")},l)}))}),null==e.display_alerts&&(e.display_alerts=function(a,n,i){n=null==n||n,i=null!=i?i:t("#alerts");a&&t.each(a,function(a,l){"object"==typeof l?t.each(l,function(t,l){l&&(e.display_alert(i,l,a,"success"==a?e.success_timeout:0,n),n=!1)}):l&&(e.display_alert(i,l,a,"success"==a?e.success_timeout:0,n),n=!1)})}),null==e.update_nav_controls&&(e.update_nav_controls=function(e){(e=null!=e&&e)?t("#bull5i-navbar :input").prop("disabled",!0):(t("#bull5i-navbar :input").prop("disabled",!1),t("input[name*='selected']:checked").length?(t("#btn-send").prop("disabled",!1),t("#btn-delete").prop("disabled",!1),t("#btn-copy").prop("disabled",!1),t("input[name*='selected']:checked").length>1?t("#batch-edit-container").removeClass("hidden"):t("#batch-edit-container").addClass("hidden")):(t("#btn-send").prop("disabled",!0),t("#btn-delete").prop("disabled",!0),t("#btn-copy").prop("disabled",!0),t("#batch-edit-container").addClass("hidden")))}),null==e.update_special_price_menu&&(e.update_special_price_menu=function(e){var a=e.data("value"),n=t("#filter_special_price"),i=t("#filter_price");null!=a&&n.length&&i.length&&(n.val(a),i.val(a),e.closest("ul").find("li.active").removeClass("active"),e.closest("ul").find("i.fa-check").removeClass("fa-check"),e.closest("li").addClass("active"),e.find("i.fa").addClass("fa-check"),""==a?i.prop("disabled",!1):i.prop("disabled",!0))}),t.unique=function(e){return e instanceof Array&&e.length&&!e[0].nodeType?t.grep(e,function(a,n){return t.inArray(a,e)===n}):r.apply(this,arguments)},t.fn.serializeHash=function(){var e={};return t.each(t(this).serializeArray(),function(t,a){null==e[a.name]?e[a.name]=a.value:"object"==typeof e[a.name]?e[a.name].push(a.value):e[a.name]=[e[a.name],a.value]}),e},t.editable.addInputType("bs_text",{element:function(e,a){var n=t("<input/>",{type:"text",class:"form-control input-sm input-qe"});return t(this).append(n),n}}),t.editable.addInputType("bs_autocomplete",{element:function(e,a){var n=t("<input/>",{type:"hidden"}),i=t("<div/>",{class:"input-group"}).append(t("<input/>",{type:"text",class:"form-control input-sm input-qe",autocomplete:"off"}),t("<span/>",{class:"input-group-btn"}).append(t("<button/>",{type:"submit",class:"btn btn-sm btn-primary"}).html(e.buttontext)));return t(this).append(i),t(this).append(n),t("input[type=text]",i).autocomplete({source:function(a,n){t.ajax({url:e.autocomplete+encodeURIComponent(a),dataType:"json",success:function(a){n(t.unique(t.map(a,function(t){return{label:t[e.label],value:t[e.value]}})))}})},select:function(e){return n.val(e.value),t("input[type=text]",i).val(e.label),!1}}),n}}),t.editable.addInputType("bs_textarea",{element:function(e,a){var n=t("<textarea />",{class:"form-control input-sm input-qe",style:"width:auto;margin-bottom:5px"});return e.rows?n.attr("rows",e.rows):"none"!=e.height&&n.css("height",e.height+"px"),e.cols?n.attr("cols",e.cols):"none"!=e.width&&n.css("width",e.width-e.padding+"px"),t(this).append(n),n},buttons:function(e,a){var n,i=this;if(e.submit||e.cancel){var l,r=t("<div />",{class:"btn-group"});if(e.submit&&(e.submit.match(/>$/)?n=t(e.submit).on("click",function(){"submit"!==n.attr("type")&&i.trigger("submit")}):((n=t('<button type="submit" />')).html(e.submit),e.submitcssclass&&n.addClass(e.submitcssclass)),t(r).append(n)),e.cancel)e.cancel.match(/>$/)?l=t(e.cancel):((l=t('<button type="cancel" />')).html(e.cancel),e.cancelcssclass&&l.addClass(e.cancelcssclass)),t(r).append(l),t(l).on("click",function(n){return("function"==typeof t.editable.types[e.type].reset?t.editable.types[e.type].reset:t.editable.types.defaults.reset).apply(i,[e,a]),!1});t(this).append(r)}}}),t.editable.addInputType("bs_select",{element:function(e,a){var n=t("<select />",{class:"form-control input-sm input-qe"});return e.multiple&&n.attr("multiple","multiple"),t(this).append(n),n},content:function(e,a,n){var i,l=this;if(i=String===e.constructor?JSON.parse(e):e,t.each(i,function(e,a){if(a.hasOwnProperty("type")&&a.hasOwnProperty("values")){var n=t("<optgroup/>",{label:a.hasOwnProperty("label")?a.label:"Group"});t.each(a.values,function(e,a){if(a.hasOwnProperty("id")&&a.hasOwnProperty("value")&&"selected"!=a.id){var i=t("<option />").val(a.id).append(a.value);n.append(i)}}),t("select",l).append(n)}else{if(!a.hasOwnProperty("id")||!a.hasOwnProperty("value"))return;if("selected"==a.id)return;var i=t("<option />").val(a.id).append(a.value);t("select",l).append(i)}}),t("select option",this).each(function(){t(this).val()!=i.selected&&t(this).text()!=t("<div/>").html(String.prototype.trim.call(null==n.revert?"":n.revert)).text()||t(this).attr("selected","selected")}),!a.submit){var r=this;t("select",this).change(function(){r.trigger("submit")})}}}),t.editable.addInputType("bs_advanced_select",{element:function(e,a){var n=t("<select />",{class:"form-control input-sm input-qe"});return t(this).append(n),n},content:function(a,n,i){var l;if((l=String===a.constructor?JSON.parse(a):a).error)return e.display_alert(t("#alerts"),l.error,"error",0,!0),t("select",this).remove(),void setTimeout(function(){i.reset(o)},0);for(var r in l=l.select)if(l.hasOwnProperty(r)&&"selected"!=r){var s=t("<option />").val(r).append(l[r]);t("select",this).append(s)}if(t("select",this).children().each(function(){t(this).val()!=l.selected&&t(this).text()!=t("<div/>").html(String.prototype.trim.call(null==i.revert?"":i.revert)).text()||t(this).attr("selected","selected")}),!n.submit){var o=this;t("select",this).change(function(){o.trigger("submit")})}}}),t.editable.addInputType("status_edit",{element:function(e,a){var n=t("<select />",{class:"form-control input-sm input-qe"});return t(this).append(n),n},content:function(e,a,n){var i;for(var l in i=String===e.constructor?JSON.parse(e):e)if(i.hasOwnProperty(l)&&"selected"!=l){var r=t("<option />").val(l).append(i[l]);t("select",this).append(r)}var s=t("<label />");s.attr("for","notify"),s.html(a.notify_customer_text);var o=t("<input />"),c=a.notify_customer;o.attr({type:"checkbox",name:"notify",id:"notify",value:"1"}),c&&o.attr("checked","checked"),t(this).append(t("<br />")).append(o).append(s),o.click(function(){t(this).focus()}),t("select",this).children().each(function(){t(this).val()!=i.selected&&t(this).text()!=String.prototype.trim.call(null==n.revert?"":n.revert)||t(this).attr("selected","selected")});var u=this;a.submit||t("select",this).change(function(){u.trigger("submit")})}}),t.editable.addInputType("multiselect_edit",{element:function(e,a){var n=t("<input />");return n.attr({type:"hidden"}),t(this).append(n),n},content:function(a,n,i){var l;l=String===a.constructor?JSON.parse(a):a;var r=this;if(l.alerts&&!l.error&&e.display_alerts(l.alerts,!0,t("#alerts")),l.error)return e.display_alert(t("#alerts"),l.error,"error",0,!0),t("input",this).remove(),void setTimeout(function(){i.reset(r)},0);e.aqe_popup(l.title,l.popup,function(e){r.trigger("submit"),"function"==typeof e&&e.call(null,!0)},"modal-md").fail(function(){i.reset(r)})}}),t.editable.addInputType("popup_edit",{element:function(e,a){var n=t("<input />");return n.attr({type:"hidden"}),t(this).append(n),n},content:function(a,n,i){var l;l=String===a.constructor?JSON.parse(a):a;var r=this;if(l.alerts&&!l.error&&e.display_alerts(l.alerts,!0,t("#alerts")),l.error)return e.display_alert(t("#alerts"),l.error,"error",0,!0),t("input",this).remove(),void setTimeout(function(){i.reset(r)},0);var s=t(i).attr("id"),o=t(i).attr("id").split("-")[1],c=t(i).attr("id").split("-")[0],u={alerts:t.merge(t("#aqe-modal .notice"),t("#alerts"))};e.aqe_popup(l.title,l.popup,function(l){a={id:s,old:"",new:""},e.batch_edit&&t("input[name*='selected']:checked").length&&(a.ids=t("input[name*='selected']:checked").serializeObject().selected),t.extend(a,t("#aqeQuickEditForm").serializeHash()),e.aqe_popup_update.call(u,n.saveurl,a).done(function(n){(!0===n||n.success)&&e.update_related(c,t.unique(t.merge([o],a.ids||[]))),"function"==typeof l&&l.call(null,n),i.reset(r),n.results&&n.results.done&&t.each(n.results.done,function(e,a){null!=n.values?(n.values.hasOwnProperty("*")&&(ret_val=n.values["*"]),n.values.hasOwnProperty(a)&&(ret_val=n.values[a])):ret_val=n.value,t("#"+c+"-"+a).html(ret_val)}),t("#"+s).css("width","").editable("enable")}).fail(function(e){"function"==typeof l&&l.call(null,e),i.reset(r)})},"modal-lg").fail(function(){i.reset(r)})}}),t.editable.addInputType("multilingual_edit",{element:function(e,a){var n=t("<input />");return n.attr({type:"hidden"}),t(this).append(n),n},content:function(a,n,i){var l;l=String===a.constructor?JSON.parse(a):a;var r=this;if(l.alerts&&!l.error&&e.display_alerts(l.alerts,!0,t("#alerts")),l.error)return e.display_alert(t("#alerts"),l.error,"error",0,!0),t("input",this).remove(),void setTimeout(function(){i.reset(r)},0);var s=t(i).attr("id"),o=t(i).attr("id").split("-")[1],c=t(i).attr("id").split("-")[0],u={alerts:t.merge(t("#aqe-modal .notice"),t("#alerts"))};e.aqe_popup(l.title,l.popup,function(l){a={id:s,old:"",new:""},e.batch_edit&&t("input[name*='selected']:checked").length&&(a.ids=t("input[name*='selected']:checked").serializeObject().selected),t.extend(a,t("#aqeQuickEditForm").serializeHash()),e.aqe_popup_update.call(u,n.saveurl,a).done(function(n){(!0===n||n.success)&&e.update_related(c,t.unique(t.merge([o],a.ids||[]))),"function"==typeof l&&l.call(null,n),i.reset(r),n.results&&n.results.done&&t.each(n.results.done,function(e,a){null!=n.values?(n.values.hasOwnProperty("*")&&(ret_val=n.values["*"]),n.values.hasOwnProperty(a)&&(ret_val=n.values[a])):ret_val=n.value,t("#"+c+"-"+a).html(ret_val)}),t("#"+s).css("width","").editable("enable")}).fail(function(e){"function"==typeof l&&l.call(null,e),i.reset(r)})}).fail(function(){i.reset(r)})}}),t.editable.addInputType("customer_edit",{element:function(e,a){var n=t("<input/>",{type:"hidden"}),i=t("<div/>",{class:"input-group"}).append(t("<input/>",{type:"text",class:"form-control input-sm input-qe",autocomplete:"off"}),t("<span/>",{class:"input-group-btn"}).append(t("<button/>",{type:"submit",class:"btn btn-sm btn-primary"}).html(e.buttontext))),l=t("<input/>",{type:"hidden",name:"first_name"});return last_name=t("<input/>",{type:"hidden",name:"last_name"}),customer_id=t("<input/>",{type:"hidden",name:"customer_id"}),t(this).append(i),t(this).append(n),t(this).append(l),t(this).append(last_name),t(this).append(customer_id),t("input[type=text]",i).autocomplete({source:function(a,n){t.ajax({url:e.autocomplete+encodeURIComponent(a),dataType:"json",success:function(e){n(t.unique(t.map(e,function(e){return{label:e.full_name,value:e.customer_id,first_name:e.first_name,last_name:e.last_name}})))}})},select:function(e){return n.val(e.value),l.val(e.first_name),last_name.val(e.last_name),customer_id.val(e.customer_id),t("input[type=text]",i).val(e.label),!1}}),n}}),t.editable.addInputType("date_edit",{element:function(e,a){var n=t("<input/>",{type:"text",class:"form-control input-sm input-qe"}),i=t("<div/>",{class:"input-group date"}).append(n,t("<span/>",{class:"input-group-addon input-sm"}).append(t("<i/>",{class:"fa fa-calendar"})));return n.css({"min-width":"70px"}),t(this).append(i),i.datetimepicker({pickTime:!1,format:"YYYY-MM-DD"}),t(this).on("submit",function(){i.data("DateTimePicker").destroy()}),n},content:function(e,a,n){t(".date",this).data("DateTimePicker").setDate(e)}}),t.editable.addInputType("date_time_edit",{element:function(e,a){var n=t("<input/>",{type:"text",class:"form-control input-sm input-qe"}),i=t("<div/>",{class:"input-group date"}).append(n,t("<span/>",{class:"input-group-addon input-sm"}).append(t("<i/>",{class:"fa fa-calendar"})));return n.css({"min-width":"120px"}),t(this).append(i),i.datetimepicker({pickTime:!0,pickDate:!0,useSeconds:!0,format:"YYYY-MM-DD HH:mm:ss"}),t(this).on("submit",function(){i.data("DateTimePicker").destroy()}),n},content:function(e,a,n){t(".date",this).data("DateTimePicker").setDate(e)}}),t.editable.addInputType("image_edit",{element:function(e,a){var n=t("<input />");return n.attr({type:"hidden",id:"img-"+t(a.revert).attr("data-id"),value:""}),t(this).append(n),n},content:function(a,n,i){var l=t("<a/>",{href:"#",class:"img-thumbnail img-relative",id:"quick-edit-thumbnail"}).append(t("<div/>",{class:"content-overlay in"}).append(t("<span/>",{class:"fa fa-2x fa-refresh fa-spin"})),t(i.revert).removeClass("img-thumbnail"));t(this).append(l);var r=this;e.update_image("img-"+t(i.revert).attr("data-id")).done(function(){r.trigger("submit")}).fail(function(){i.reset(r)})}}),e.update_failed=function(){t(this).html(this.revert).editable("enable")},e.quick_update=function(a,n,i,l,r){var s=a.revert,o=t(a),c={id:t(a).attr("id"),old:s,new:n},u=t(a).attr("id").split("-")[0],d=t.Deferred(),p={alerts:t("#alerts")};return e.batch_edit&&t("input[name*='selected']:checked").length&&(c.ids=t("input[name*='selected']:checked").serializeObject().selected),r&&t.extend(c,r),o.editable("disable"),e.aqe_popup_update.call(p,l,c).done(function(e){e.results&&e.results.done?t.each(e.results.done,function(a,n){null!=e.values?(e.values.hasOwnProperty("*")&&(s=e.values["*"]),e.values.hasOwnProperty(n)&&(s=e.values[n])):s=e.value,t("#"+u+"-"+n).html(s)}):o.html(s),o.css("width","").editable("enable"),d.resolve(e)}).fail(function(e){d.reject(c)}),d.promise()},e.aqe_popup_update=function(a,n){var i={},l=t.Deferred(),r=this;return n&&t.extend(i,n),t.ajax({type:"POST",url:a,dataType:"json",data:i}).done(function(t,a,n){t?(Object.values(t.alerts).flat().length?e.display_alerts(t.alerts,!0,r.alerts):t.success&&e.clear_alerts(r.alerts),t.success?l.resolve(t):l.reject(t)):l.reject({})}).fail(function(a,n,i){e.display_alert(t("#alerts"),n,"error",0,!0),l.reject(n)}),l.promise()},e.cell_updating=function(e){var a=e.outerWidth(),n=e.outerHeight();e.data("original-content",e.html()).addClass("updating").css({padding:0,width:a+"px",height:n+"px",position:"relative"}).html(t("<div/>",{class:"overlay-container"}).append(t("<i/>",{class:"fa fa-2x fa-spin fa-refresh overlay-loading-spinner"}))),e.editable("disable")},e.update_finished=function(t){t&&t.success&&t.results&&t.results.done&&this.column&&e.update_related&&e.update_related(this.column,t.results.done)},e.load_popup_data=function(a,n){var i=t.Deferred(),l={};return n&&t.extend(l,n),t.ajax({type:"POST",url:a,data:l,dataType:"json"}).done(function(t,a,n){t?(t.alerts&&e.display_alerts(t.alerts,!0,this.alerts),t.success?i.resolve(t):i.reject(t)):i.reject({})}).fail(function(a,n,l){e.display_alert(t("#alerts"),n,"error",0,!0),i.reject(n)}),i.promise()},e.aqe_popup=function(a,n,i,l){var r=t.Deferred(),s=(l=void 0!==l?l:"",!0);return t("#aqe-modal").modal("hide"),t("#aqe-modal .notice").html(""),t("#aqe-modal .modal-dialog").removeClass("modal-lg").removeClass("modal-md"),t("#aqe-modal .aqe-modal-contents").html(n),t("#aqe-modal .modal-title").html(a),t("#aqe-modal .modal-dialog").addClass(l),t("#aqe-modal").modal("show"),t("#aqe-modal").on("hide.bs.modal",function(){t("#aqe-modal").off("hide.bs.modal"),s&&(s=!1,r.reject())}).on("hidden.bs.modal",function(){t("#aqe-modal").off("shown.bs.modal"),t("#aqe-modal").off("hidden.bs.modal"),t("#aqe-modal").off("click",".modal-footer .cancel"),t("#aqe-modal").off("click",".modal-footer .submit")}).on("shown.bs.modal",function(){t("form :input:visible:enabled:first",this).focus()}).on("click",".modal-footer .submit",function(a){var n={self:this,btn:t(this),form:t(t(this).data("form")),alerts:t.merge(t("#aqe-modal .notice"),t("#alerts")),context:t(t(this).data("context"))};a.preventDefault(),n.form&&"function"==typeof i?(n.btn.data("loading-text")?n.btn.button("loading"):n.btn.prop("disabled",!0),i.call(n,function(a){n.btn.data("loading-text")?n.btn.button("reset"):n.btn.prop("disabled",!1),t("#aqe-modal .aqe-modal-contents .error-text").remove(),!0===a||a.success?(s=!1,r.resolve()):(a.alerts&&e.display_alerts(a.alerts,!0,this.alerts),a.errors&&t.each(a.errors,function(e,a){var n=t("[name='"+e+"']");if(n.length){var i=t("<div/>",{class:"text-danger error-text"}).html(a);n.parent().hasClass("input-group")?n.parent().after(i):n.after(i)}}))})):r.resolve()}),r.always(function(){t("#aqe-modal").modal("hide")}),r.promise()},t(function(){t("body").on("click",".modal-footer .cancel",function(e){}).on("click","#global-selector",function(e){t("input[name*='selected']").prop("checked",this.checked).trigger("change")}).on("change","#batch-edit",function(){e.batch_edit=t(this).is(":checked")}).on("change","input[name*='selected']",function(t){e.update_nav_controls()}).on("change","select.view_in_store",function(e){t(this).val()&&(window.open(this.value),this.value="")}).on("click",".filter-special-price",function(a){a.preventDefault(),e.update_special_price_menu(t(this))}).on("click","#filter",function(t){e.filter()}).on("click","#clear-filter",function(a){e.update_special_price_menu(t("#special-price-off")),t("tr.filters .fltr:input").val(""),e.filter()}).on("keydown","tr.filters input,tr.filters select",function(t){13==t.keyCode&&e.filter()}).on("click","#btn-delete",function(e){var a=!1,n=t.Deferred();$form=t(t(this).data("form")),options={self:this,btn:t(this),form:$form,url:t(this).data("url"),alerts:t("#alerts"),context:t(t(this).data("context"))},e.preventDefault(),options.url&&options.form&&(t("#confirmDelete").length?(t("#confirmDelete").modal("show"),t("#confirmDelete button.delete").click(function(){t("#confirmDelete").modal("hide"),a=!0}),t("#confirmDelete").on("hidden.bs.modal",function(){a?n.resolve():n.reject()})):n.resolve(),n.done(function(){options.btn.data("loading-text")?options.btn.button("loading"):options.btn.prop("disabled",!0),options.form.attr("action",options.url).submit()}))}).on("click","#btn-copy",function(e){var a=t(t(this).data("form")),n={self:this,btn:t(this),form:a,url:t(this).data("url"),alerts:t("#alerts"),context:t(t(this).data("context"))};e.preventDefault(),n.url&&n.form&&(n.btn.data("loading-text")?n.btn.button("loading"):n.btn.prop("disabled",!0),n.form.attr("action",n.url).submit())}).on("click","#btn-send,.btn-send",function(a){var n=t(t(this).data("form")),i={},l={self:this,btn:t(this),form:n.length?n:null,url:t(this).data("url"),alerts:t("#alerts"),context:t(t(this).data("context"))};a.preventDefault(),l.url&&(l.btn.data("loading-text")?l.btn.button("loading"):l.btn.prop("disabled",!0),l.form&&(i=l.form.serializeObject()),e.aqe_popup_update.call(l,l.url,i).always(function(e){l.btn.data("loading-text")?l.btn.button("reset"):l.btn.prop("disabled",!1)}))}).on("click","#btn-insert,#btn-cancel,.btn.btn-nav-link",function(e){var a={btn:t(this),url:t(this).data("url")||this.href};e.preventDefault(),a.url&&(a.btn.data("loading-text")?a.btn.button("loading"):a.btn.prop("disabled",!0),window.location=a.url)}),t('input[type=checkbox][name^="selected"]').change(function(){t(this).is(":checked")?t(this).parents("tr").first().addClass("warning"):t(this).parents("tr").first().removeClass("warning")}),t("#alerts .alert.alert-success").each(function(e){var a=t(this);setTimeout(function(){a.alert("close")},8e3)}),e.update_nav_controls()})}(window.bull5i=window.bull5i||{},jQuery);
