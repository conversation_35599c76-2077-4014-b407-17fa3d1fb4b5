<?php
//==============================================================================
// Basic UPS v2025-4-06
// 
// Author: Clear Thinking, LLC
// E-mail: <EMAIL>
// Website: http://www.getclearthinking.com
// 
// All code within this file is copyright Clear Thinking, LLC.
// You may not copy or reuse code within this file without written permission.
//==============================================================================

$_['version'] = 'v2025-4-06';

//------------------------------------------------------------------------------
// Heading
//------------------------------------------------------------------------------
$_['heading_title']						= 'Basic UPS';

//------------------------------------------------------------------------------
// Extension Settings
//------------------------------------------------------------------------------
$_['tab_extension_settings']			= 'Extension Settings';
$_['heading_extension_settings']		= 'Extension Settings';

$_['entry_status']						= 'Status: <div class="help-text">Set the status for the extension as a whole.</div>';
$_['entry_check_for_updates']			= 'Check For Updates: <div class="help-text">Choose whether to automatically check for updates when the extension admin panel is loaded.</div>';
$_['entry_heading']						= 'Heading: <div class="help-text">The heading under which these shipping options will appear. Use [weight] to display the cart weight. HTML is supported.</div>';
$_['entry_sort_order']					= 'Sort Order: <div class="help-text">Set the Sort Order for the extension, relative to other shipping methods.</div>';
$_['entry_tax_class_id']				= 'Tax Class: <div class="help-text">If you want to tax your shipping costs, set a tax class.</div>';

$_['entry_rate_sorting']				= 'Rate Sorting: <div class="help-text">Choose how to sort rates returned by UPS.</div>';
$_['text_sort_by_name']					= 'Sort by Name';
$_['text_sort_by_price_ascending']		= 'Sort by Price Ascending';
$_['text_sort_by_price_descending']		= 'Sort by Price Descending';

//------------------------------------------------------------------------------
// Restrictions
//------------------------------------------------------------------------------
$_['tab_restrictions']					= 'Restrictions';
$_['heading_restrictions']				= 'Restrictions';

$_['entry_stores']						= 'Store(s): <div class="help-text">Select the stores that can use this shipping method.</div>';

$_['entry_geo_zones']					= 'Geo Zone(s): <div class="help-text">Select the geo zones that can use this shipping method. The "Everywhere Else" checkbox applies to any locations not within a geo zone.</div>';
$_['text_everywhere_else']				= '<em>Everywhere Else</em>';

$_['entry_customer_groups']				= 'Customer Group(s): <div class="help-text">Select the customer groups that can use this shipping method. The "Guests" checkbox applies to all customers not logged in to an account.</div>';
$_['text_guests']						= '<em>Guests</em>';

//------------------------------------------------------------------------------
// UPS Settings
//------------------------------------------------------------------------------
$_['tab_ups_settings']					= 'UPS Settings';
$_['heading_ups_settings']				= 'UPS Settings';

$_['entry_mode']						= 'Mode: <div class="help-text">Rates can be tested in "Test" mode. Once your store is live you should use "Live" mode.</div>';
$_['text_test']							= 'Test';
$_['text_live']							= 'Live';

$_['help_ups_settings']					= '
	<div class="text-info text-center">
		Enter your Client ID and Client Secret below from your <a target="_blank" href="https://developer.ups.com/apps">UPS App</a>.
		For directions on how to create a UPS App, <a style="color: #428bca" onclick="$(\'#help-creating-app\').slideToggle()">follow these steps</a>.
	</div>
	<div id="help-creating-app" class="alert alert-info" style="display: none; margin-top: 15px;">
		<ol style="line-height: 2; margin: 0; padding: 5px 0 0 20px;">
			<li>Navigate to <a target="_blank" href="https://developer.ups.com/apps">https://developer.ups.com/apps</a></li>
			<li>Log in using your UPS credentials.</li>
			<li>Click the "Add App" link.</li>
			<li>In the pop-up that appears, choose "I want to integrate UPS shipping capabilities to my business."</li>
			<li>Follow the prompts to fill in your contact info.</li>
			<li>When you get to the "Add App" page, fill in the "App name" with whatever you want (e.g. OpenCart).</li>
			<li>Click the + button for the following APIs:
				<ul>
					<li>Authorization (O Auth)</li>
					<li>Rating</li>
				</ul>
			</li>
			<li>Leave the "Callback URL" field blank.</li>
			<li>Click "Save".</li>
			<li>You should now be shown a page with your Client ID and Client Secret.</li>
		</ol>
	</div>
';

$_['entry_client_id']					= 'Client ID:';
$_['entry_client_secret']				= 'Client Secret:';
$_['entry_account_number']				= 'Account Number: <div class="help-text">If you want to use UPS Negotiated Rates, enter your account number in this field.</div>';

$_['entry_store_address']				= 'Store Address: <div class="help-text">Enter your store\'s physical address that is used for the "Ship From" address.</div>';
$_['placeholder_street_address']		= 'Street Address';
$_['placeholder_city']					= 'City';
$_['placeholder_state']					= 'State';
$_['placeholder_postcode']				= 'Postcode';
$_['placeholder_country']				= 'Country';

// Rate Settings
$_['heading_rate_settings']				= 'Rate Settings';

$_['entry_address_type']				= 'Address Type: <div class="help-text">Choose whether to request rates for Residential or Commercial delivery.</div>';
$_['text_commercial']					= 'Commercial';
$_['text_residential']					= 'Residential';

$_['entry_insurance']					= 'Add Insurance: <div class="help-text">Choose whether to include the insurance cost in the rate calculation.</div>';
$_['entry_rate_adjustment']				= 'Add Rate Adjustment: <div class="help-text">Optionally enter a flat or percentage value to increase the rate costs returned from UPS. For example, enter <code>5.00</code> to increase all prices by $5.00, or enter <code>-10%</code> to decrease all prices by 10%.</div>';

$_['entry_customer_classification']		= 'Customer Classification: <div class="help-text">If you use negotiated rates you may need to set a particular value for this setting. Talk to your UPS Representative if you do not know what value to use.</div>';
$_['text_rates_associated']				= '00 - Rates Associated with Shipper Number';
$_['text_daily_rates']					= '01 - Daily Rates';
$_['text_retail_rates']					= '04 - Retail Rates';
$_['text_regional_rates']				= '05 - Regional Rates';
$_['text_general_list_rates']			= '06 - General List Rates';
$_['text_standard_list_rates']			= '53 - Standard List Rates';

$_['entry_pickup_type']					= 'Pickup Type: <div class="help-text">Choose the pickup type for the rate request. If you use negotiated rates you may need to use "Customer Counter" or "None" to get the correct rates from your account.</div>';
$_['text_none']							= 'None';
$_['text_daily_pickup']					= 'Daily Pickup';
$_['text_customer_counter']				= 'Customer Counter';

$_['entry_negotiated_rates']			= 'Use Negotiated Rates: <div class="help-text">Choose whether to retrieve UPS Negotiated Rates for your account.</div>';

// Package Settings
$_['heading_package_settings']			= 'Package Settings';

$_['entry_box_dimensions']				= 'Box Dimensions: <div class="help-text">Set the size of the box used in the API request. Enter values in order from largest to smallest using this format:<br><code>12 x 10 x 8</code></div>';
$_['entry_dimension_units']				= 'Dimension Units: <div class="help-text">Choose whether to send the dimensions to UPS in inches or centimeters.</div>';
$_['text_inches']						= 'Inches';
$_['text_centimeters']					= 'Centimeters';

$_['entry_weight_limit']				= 'Weight Limit: <div class="help-text">Set the weight limit. If the cart weighs more than this, the extension will be disabled. The default value is 150, which is the standard UPS limit for pounds. If you use kilograms you want to set this to 68.</div>';
$_['entry_weight_units']				= 'Weight Units: <div class="help-text">Choose whether to send the weight to UPS in pounds or kilograms.</div>';
$_['text_pounds']						= 'Pounds';
$_['text_kilograms']					= 'Kilograms';

//------------------------------------------------------------------------------
// UPS Services
//------------------------------------------------------------------------------
$_['tab_ups_services']					= 'UPS Services';
$_['heading_ups_services']				= 'UPS Services';

$_['help_ups_services']					= 'Enter the name for the service displayed to the customer on the front-end. Leave the name blank to hide that service.';

$_['text_service_03']					= 'UPS Ground';
$_['text_service_12']					= 'UPS 3 Day Select';
$_['text_service_02']					= 'UPS 2nd Day Air';
$_['text_service_59']					= 'UPS 2nd Day Air A.M.';
$_['text_service_01']					= 'UPS Next Day Air';
$_['text_service_13']					= 'UPS Next Day Air Saver';
$_['text_service_14']					= 'UPS Next Day Air Early A.M.';
$_['text_service_11']					= 'UPS Standard';
$_['text_service_65']					= 'UPS Worldwide Saver';
$_['text_service_07']					= 'UPS Worldwide Express';
$_['text_service_08']					= 'UPS Worldwide Expedited';
$_['text_service_54']					= 'UPS Worldwide Express Plus';

//------------------------------------------------------------------------------
// Testing Mode
//------------------------------------------------------------------------------
$_['tab_testing_mode']					= 'Testing Mode';
$_['testing_mode_help']					= 'Enable testing mode if things are not working as expected on the front end. Messages logged during testing can be viewed below.';
$_['heading_testing_mode']				= 'Testing Mode';

$_['entry_testing_mode']				= 'Testing Mode:';
$_['entry_testing_messages']			= 'Messages:';
$_['button_refresh_log']				= 'Refresh Log';
$_['button_download_log']				= 'Download Log';
$_['button_clear_log']					= 'Clear Log';

//------------------------------------------------------------------------------
// Standard Text
//------------------------------------------------------------------------------
$_['contact_url']						= 'https://www.getclearthinking.com/contact?storeurl=' . str_replace('www.', '', $_SERVER['HTTP_HOST']) . '&version=' . VERSION;
$_['copyright']							= '<hr><div class="text-center" style="margin: 15px">' . $_['heading_title'] . ' (' . $_['version'] . ') &copy; <a target="_blank" href="' . $_['contact_url'] . '">Clear Thinking, LLC</a></div>';

$_['standard_autosaving_enabled']		= 'Auto-Saving Enabled';
$_['standard_confirm']					= 'This operation cannot be undone. Continue?';
$_['standard_error']					= '<strong>Error:</strong> You do not have permission to modify ' . $_['heading_title'] . '!';
$_['standard_max_input_vars']			= '<strong>Warning:</strong> The number of settings is close to your <code>max_input_vars</code> server value. You should enable auto-saving to avoid losing any data.';
$_['standard_please_wait']				= 'Please wait...';
$_['standard_saved']					= 'Saved!';
$_['standard_saving']					= 'Saving...';
$_['standard_select']					= '--- Select ---';
$_['standard_success']					= 'Success!';
$_['standard_testing_mode']				= "Your log is too large to open! If you need to archive it, you can download it using the button above.\n\nTo start a new log, (1) click the Clear Log button, (2) reload the admin panel page, then (3) run your test again.";

$_['standard_check_for_updates']		= 'Check For Updates';
$_['standard_contact_clear_thinking']	= 'Contact Clear Thinking';
$_['standard_error_checking']			= 'There was an error checking for the latest version.';
$_['standard_using_latest']				= 'You are using the latest version';
$_['standard_new_version']				= 'A new version is available!';
$_['standard_your_version']				= 'Your Version:';
$_['standard_latest_version']			= 'Latest Version:';
$_['standard_release_notes']			= 'View release notes';
$_['standard_continue']					= 'Continue';
$_['standard_update_warning']			= '<ul><li>Before updating, it is highly recommended to <b>back up your website files</b>.</li><br><li>To update, enter your license key below and click "Update". A license comes with 1 year of free updates, so you may not qualify for the latest version if you are beyond that period. If that is the case, you will be notified after attempting to update.</li><br><li>Updating the extension will <b>overwrite all current extension files</b>. If you have made modifications to any files, make sure you back up your edits before updating.</li><br><li>If any issues occur during or after updating, download and reinstall the extension manually.</li><br><li>If you have lost your license key or download link, you can retrieve them <a target="_blank" href="https://www.getclearthinking.com/downloads/license">on this page</a>.</li></ul><br>';
$_['standard_update']					= 'Update';
$_['standard_updating']					= 'Updating...';
$_['standard_license_key']				= 'License Key:';

$_['standard_module']					= 'Modules';
$_['standard_shipping']					= 'Shipping';
$_['standard_payment']					= 'Payments';
$_['standard_total']					= 'Order Totals';
$_['standard_feed']						= 'Feeds';
?>