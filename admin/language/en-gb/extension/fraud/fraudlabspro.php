<?php
// Heading
$_['heading_title']              = 'FraudLabs Pro';

// Text
$_['text_extension']             = 'Extensions';
$_['text_success']               = 'Success: You have modified FraudLabs Pro Settings!';
$_['text_edit']                  = 'Settings';
$_['text_signup']                = 'FraudLabs Pro is a fraud detection service. You can <a href="http://www.fraudlabspro.com/plan?ref=1730" target="_blank"><u>sign up here</u></a> for a free API Key.';
$_['text_id']                    = 'FraudLabs Pro ID';
$_['text_ip_address']            = 'IP Address';
$_['text_ip_net_speed']          = 'IP Net Speed';
$_['text_ip_isp_name']           = 'IP ISP Name';
$_['text_ip_usage_type']         = 'IP Usage Type';
$_['text_ip_domain']             = 'IP Domain';
$_['text_ip_time_zone']          = 'IP Time Zone';
$_['text_ip_location']           = 'IP Location';
$_['text_ip_distance']           = 'IP Distance';
$_['text_ip_latitude']           = 'IP Latitude';
$_['text_ip_longitude']          = 'IP Longitude';
$_['text_risk_country']          = 'High Risk Country';
$_['text_free_email']            = 'Free Email';
$_['text_ship_forward']          = 'Ship Forward';
$_['text_using_proxy']           = 'Using Proxy';
$_['text_bin_found']             = 'BIN Found';
$_['text_email_blacklist']       = 'Email Blacklist';
$_['text_credit_card_blacklist'] = 'Credit Card Blacklist';
$_['text_score']                 = 'FraudLabs Pro Score';
$_['text_status']                = 'FraudLabs Pro Status';
$_['text_message']               = 'Message';
$_['text_transaction_id']        = 'Transaction ID';
$_['text_credits']               = 'Balance';
$_['text_error']                 = 'Error:';
$_['text_flp_upgrade']           = '<a href="http://www.fraudlabspro.com/plan" target="_blank">[Upgrade]</a>';
$_['text_flp_merchant_area']     = 'Please login to <a href="http://www.fraudlabspro.com/merchant/login" target="_blank">FraudLabs Pro Merchant Area</a> for more information about this order.';
$_['text_rule_validation']       = 'Rules Validation';
$_['text_testing']               = 'Testing Purpose';

// Entry
$_['entry_status']               = 'Status';
$_['entry_key']                  = 'API Key';
$_['entry_score']                = 'Risk Score';
$_['entry_order_status']         = 'Order Status';
$_['entry_review_status']        = 'Review Status';
$_['entry_approve_status']       = 'Approve Status';
$_['entry_reject_status']        = 'Reject Status';
$_['entry_simulate_ip']          = 'Simulate IP';

// Help
$_['help_order_status']          = 'Orders that have a score over your set risk score will be assigned this order status.';
$_['help_review_status']         = 'Orders that marked as review by FraudLabs Pro will be assigned this order status.';
$_['help_approve_status']        = 'Orders that marked as approve by FraudLabs Pro will be assigned this order status.';
$_['help_reject_status']         = 'Orders that marked as reject by FraudLabs Pro will be assigned this order status.';
$_['help_simulate_ip']           = 'Simulate the visitor IP address for testing. Leave blank to disable it.';
$_['help_fraudlabspro_id']       = 'Unique identifier for a transaction screened by FraudLabs Pro system.';
$_['help_ip_address']            = 'IP Address.';
$_['help_ip_net_speed']          = 'Connection speed.';
$_['help_ip_isp_name']           = 'ISP of the IP address.';
$_['help_ip_usage_type']         = 'Usage type of the IP address. E.g, ISP, Commercial, Residential.';
$_['help_ip_domain']             = 'Domain name of the IP address.';
$_['help_ip_time_zone']          = 'Time zone of the IP address.';
$_['help_ip_location']           = 'Location of the IP address.';
$_['help_ip_distance']           = 'Distance from IP address to Billing Location.';
$_['help_ip_latitude']           = 'Latitude of the IP address.';
$_['help_ip_longitude']          = 'Longitude of the IP address.';
$_['help_risk_country']          = 'Whether IP address country is in the latest high risk country list.';
$_['help_free_email']            = 'Whether e-mail is from free e-mail provider.';
$_['help_ship_forward']          = 'Whether shipping address is a freight forwarder address.';
$_['help_using_proxy']           = 'Whether IP address is from Anonymous Proxy Server.';
$_['help_bin_found']             = 'Whether the BIN information matches our BIN list.';
$_['help_email_blacklist']       = 'Whether the email address is in our blacklist database.';
$_['help_credit_card_blacklist'] = 'Whether the credit card is in our blacklist database.';
$_['help_score']                 = 'Risk score, 0 (low risk) - 100 (high risk).';
$_['help_status']                = 'FraudLabs Pro status.';
$_['help_message']               = 'FraudLabs Pro error message description.';
$_['help_transaction_id']        = 'Unique identifier for a transaction screened by FraudLabs Pro system.';
$_['help_credits']               = 'Balance of the credits available after this transaction.';

// Error
$_['error_permission']           = 'Warning: You do not have permission to modify FraudLabs Pro settings!';
$_['error_key']                  = 'API Key Required!';