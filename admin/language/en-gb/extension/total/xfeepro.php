<?php
// Heading
$_['heading_title']    = 'X-Feepro';
$_['button_save'] = 'Save All and Close';
$_['button_save_continue'] = 'Save All and Stay';
$_['button_cache'] = 'Clear Cache';
// Common accors mods
$_['text_for_all']    = 'For Any';
$_['text_checked_all'] = 'Check all';
$_['text_unchecked_all'] = 'Uncheck all';
$_['text_checked_show'] = 'Show Selected';
$_['text_batch_select']= 'Batch Selection';
$_['text_remove_all']    = 'Remove All';
$_['text_free_search']    = 'Type to search';
$_['text_ocm_inclusive']    = 'Inlcusive';
$_['text_ocm_exclusive']    = 'Exclusive';
$_['text_ocm_mode']    = 'Mode';
$_['text_start']    = 'Start';
$_['text_end']    = 'End';

// Format: prefix_varname i.e entry_name or help_name
// Entry
$_['entry_heading']    = 'Heading';
$_['entry_display'] = 'Name';
$_['entry_inc_vat'] = 'Show Price With Tax/vat';
$_['entry_tax_class_id']    = 'Tax Class:';
$_['entry_logo']    = 'Logo URL';
$_['entry_sort_order'] = 'Sort Order:';
$_['entry_status']     = 'Status:';
$_['entry_group']    = 'Select Group';
$_['entry_store'] = 'Store:';
$_['entry_geo_zone']   = 'Geo Zone:';
$_['entry_currency']    = 'Currency';
$_['entry_customer_group'] = 'Customer Group:';
$_['entry_manufacturer_rule'] = 'Manufacturer Rule:';
$_['entry_country']    = 'Country';
$_['entry_category'] = 'Category Rule';
$_['entry_customer_group'] = 'Customer Group:';
$_['entry_payment']  = 'Payment Method:';
$_['entry_shipping']  = 'Shipping Method:';
$_['entry_name']       = 'Fee/Discount Title:';
$_['entry_tax']        = 'Tax Class:';
$_['entry_status']     = 'Status:';
$_['entry_sort_order'] = 'Sort Order:';
$_['entry_customer_group'] = 'Customer Group:';
$_['entry_store'] = 'Store:';
$_['entry_manufacturer'] = 'Manufacturer:';
$_['entry_product_product']     = 'Products';
$_['entry_product_category']   = 'Categories';
$_['entry_coupon_all']    = 'Coupon';
$_['entry_coupon']    = 'Enter Coupon Code';
$_['entry_coupon_rule']    = 'Coupon Rule';
$_['entry_days']    = 'Days of Week';
$_['entry_time']    = 'Time Period';
$_['entry_product'] = 'Product Rule';
$_['entry_date']    = 'Date Range';
$_['entry_date_start']    = 'Any Date';
$_['entry_date_end']    = 'Any Date';
$_['entry_int_data'] = 'Data(s)';
$_['entry_postal_all']    = 'Zip/Postal';
$_['entry_postal']    = 'Enter Zip/Postal Code';
$_['entry_postal_rule']    = 'Zip/Postal Rule';
$_['entry_city_all']    = 'City';
$_['entry_city']    = 'Enter City';
$_['entry_city_rule']    = 'City Rule';
$_['entry_customer_rule']    = 'Customer Rule';
$_['entry_order_total']       = 'Order Total Range:';
$_['entry_weight']       = 'Weight Range:';
$_['entry_quantity']       = 'Quantity Range:';
$_['entry_final_cost']    = 'Final Cost';
$_['entry_rate_percent']    = 'Percentage related to';
$_['entry_cart_adjust']    = 'Shopping Cart Modifier';
$_['entry_equation']    = 'Final Equation';
$_['entry_hide']    = 'Hide Others On Show';
$_['entry_hide_inactive']    = 'Hide Others On Hide';
$_['entry_free_option']    = 'Enable Free Option';
$_['entry_product_attribute']    = 'Product Attributes';
$_['entry_attribute']    = 'Attribute Rule';
$_['entry_zone']    = 'Region / State';
$_['entry_option']    = 'Option Rule';
$_['entry_product_option']    = 'Product Options';
$_['entry_location_rule']    = 'Location Rule';
$_['entry_location']    = 'Product Location';
$_['entry_ingore_product_rule']    = 'Don\'t restrict the method with product rules';
$_['entry_product_or']    = 'Product Rules Mode:';
$_['entry_method_specific']    = 'Respect Product Rules';
$_['entry_rate_type']    = 'Fee/Discount By';
$_['entry_dimensional_factor']    = 'Factor Value';
$_['entry_dimensional_overfule']    = 'Count Actual Weight if it is Higher';
$_['entry_cost']    = 'Fee/Discount Amount';
$_['entry_rate_final']    = 'Final Cost';
$_['entry_unit_range']    = 'Fee/Discount Details';
$_['entry_additional']    = 'Additional Price';
$_['entry_price_adjustment']    = 'Price Adjustment';
$_['entry_group_name']       = 'Group Name (Optional)';
$_['entry_equation_neg']    = 'Negative Value';
$_['entry_map_api']    = 'Google map API key(Optional):';
$_['entry_address']    = 'Address Resolution:';
$_['entry_sorting']    = 'Order By';
$_['entry_debug']    = 'Debugging:';
$_['entry_estimator_store'] = 'Display on Stores:';
$_['entry_estimator_status']    = 'Enable Estimator';
$_['entry_estimator_fields']    = 'Estimator Fields:';
$_['entry_estimator_type']    = 'Estimator Type';
$_['entry_estimator_selector']    = 'Enter CSS selecotr';
$_['entry_estimator_css']    = 'Custom CSS';
$_['entry_export']    = 'Export';
$_['entry_import']    = 'Import';
$_['entry_method_group']    = 'Group in methods';
$_['entry_module_status']    = 'Module Status';
$_['entry_fake']    = 'Fake Fee';
$_['entry_hidden']    = 'Hidden Fee';
$_['entry_first']    = 'First order only';
$_['entry_logged']    = 'Logged Customers only';
$_['entry_customer_all'] = 'Customers:';
$_['entry_customers'] = 'Select Customers:';
$_['entry_disable'] = 'Disable All Fees/Discounts';
$_['entry_disable_other'] = 'Disable Other Methods';
$_['entry_custom'] = 'Custom Fields';
$_['entry_id']  = 'Fee/Discount ID';
// Tabs
$_['tab_help']    = 'Help & Support';
$_['tab_global']    = 'Global Setting';
$_['tab_global_general']    = 'General';
$_['tab_general']    = 'General';
$_['tab_rate']    = 'Method Setting';
$_['tab_price_setting']    = 'Price Setting';
$_['tab_others']    = 'Other Rules';
$_['tab_sub_options'] = 'Sub-Options';
$_['tab_import_export']    = 'Import/Export';
$_['tab_group_option']    = 'Group Option';
$_['tab_criteria_setting']    = 'Generic Rules';
$_['tab_category_product']    = 'Product Rules';
$_['tab_condition_event']    = 'Event/Condition';

// Text 
$_['text_store_default'] = 'Default';
$_['text_extension']  = 'Extensions';
$_['text_shipping']    = 'Shipping';
$_['text_edit']    = 'Configure Your X-Feepro';
$_['text_add_new_method']    = 'Add New Fee/Discount';
$_['text_select_all']    = 'Select All';
$_['text_unselect_all']    = 'Unselect All';
$_['text_checall']    = 'For All';
$_['text_any'] = 'For any';
$_['text_ones_any'] = 'Any of the selected ones';
$_['text_ones_any_with_other'] = 'Any of the selected ones along with others';
$_['text_ones_must'] = 'Must have selected ones';
$_['text_ones_must_with_other'] = 'Must have selected ones along with others';
$_['text_ones_except'] = 'Except for the selected ones';
$_['text_ones_except_with_other'] = 'Except for the selected ones but allowed with others';
$_['text_rule_inclusive']    = 'Inlcusive';
$_['text_rule_exclusive']    = 'Exclusive';
$_['text_sunday']    = 'Sunday';
$_['text_monday']    = 'Monday';
$_['text_tuesday']    = 'Tuesday';
$_['text_wednesday']    = 'Wednesday';
$_['text_thursday']    = 'Thursday';
$_['text_friday']    = 'Friday';
$_['text_saturday']    = 'Saturday';
$_['text_sub_total']     = 'SubTotal';
$_['text_total']     = 'SubTotal + Tax of SubTotal';
$_['text_sub_without_coupon']  = 'SubTotal - Coupon Value';
$_['text_total_without_coupon'] = 'SubTotal + Tax of SubTotal - Coupon Value';
$_['text_sub_with_shipping']  = 'SubTotal + Shipping Cost';
$_['text_total_with_shipping'] = 'SubTotal + Tax of SubTotal + Shipping Cost';
$_['text_grand']     = 'Grand Total';
$_['text_method_remove']    = 'Remove this';
$_['text_method_copy']    = 'Copy this';
$_['text_method_save']    = 'Save this';
$_['text_yes']    = 'Yes';
$_['text_no']    = 'No';
$_['text_add_product']    = 'Add  Products';
$_['text_debug_button']    = 'Live Debug Log';
$_['text_help']    = 'Help';
$_['text_guest_checkout']    = 'Guest Checkout';
$_['text_delete_all']    = 'Delete All';
$_['text_csv_import']    = 'CSV Import';
$_['text_start']    = 'Start';
$_['text_end']    = 'End';
$_['text_cost']    = 'Cost';
$_['text_qnty_block']    = 'Per Unit Block';
$_['text_add_new']    = 'Add New';
$_['text_final_cost']    = 'Final Cost';
$_['text_final_single']    = 'Single';
$_['text_final_cumulative']    = 'Cumulative';
$_['text_percentage_related']    = 'Percentage related to';
$_['text_percent_sub_total']    = 'SubTotal';
$_['text_percent_total']    = 'SubTotal + Tax of SubTotal';
$_['text_percent_shipping']    = 'Shipping Cost';
$_['text_percent_vouchers']    = 'Voucher Amount';
$_['text_percent_sub_total_shipping']    = 'SubTotal + Shipping Cost';
$_['text_percent_total_shipping']    = 'SubTotal + Tax of SubTotal + Shipping Cost';
$_['text_percent_sub_total_shipping_plus']    = 'SubTotal + Shipping Cost + Tax of Shipping Cost';
$_['text_percent_total_shipping_plus']    = 'SubTotal + Tax of SubTotal + Shipping Cost + Tax of Shipping Cost';
$_['text_percent_sub_special']   = 'SubTotal - Cost of Discounted Products';
$_['text_percent_total_special'] = 'SubTotal + Tax of SubTotal - Cost of Discounted Products';
$_['text_percent_special']       = 'Cost of Discounted Products';

$_['text_tax_products_wise']    = 'Apply as same as the tax of the products';
$_['text_price_adjustment']    = 'Price Adjustment';
$_['text_price_min']    = 'Min';
$_['text_price_max']    = 'Max';
$_['text_price_add']    = 'Modifier e.g. +5';
$_['text_days_week']    = 'Days of Week';
$_['text_time_period']    = 'Time Period';
$_['text_rate_flat']    = 'Flat Amount';
$_['text_rate_quantity']    = 'Quantity';
$_['text_rate_weight']    = 'Weight';
$_['text_rate_volume']    = 'Volume';
$_['text_rate_total']    = 'SubTotal +  Tax of SubTotal';
$_['text_grand_total']    = 'Grand Total';
$_['text_rate_sub_coupon']    = 'SubTotal - Coupon/Reward Value';
$_['text_rate_total_coupon']    = 'SubTotal + Tax of SubTotal - Coupon/Reward Value';
$_['text_rate_sub_total']    = 'SubTotal';
$_['text_dimensional_weight'] = 'Dimensional Weight';
$_['text_volumetric_weight'] = 'Volumetric Weight';
$_['text_dimensional_factor'] = 'Factor Value';
$_['text_dimensional_overrule'] = 'Consider Actual Weight if it is greater';
$_['text_percent_grand_wo_tax'] = 'Grand Total - Grand Tax';
$_['text_no_of_category']     = 'No. of Categories';
$_['text_no_of_manufacturers']     = 'No. of Manufacturers';
$_['text_no_of_location'] = 'No. of Locations';
$_['text_no_of_product'] = 'No. of Products';
$_['text_rate_type_distance']    = 'Distance (KM)';
$_['text_type_equation']    = 'Equation Value';
$_['text_group_none']    = 'None';
$_['text_no_grouping']    = 'No Grouping';
$_['text_lowest']    = 'Lowest';
$_['text_highest']    = 'Highest';
$_['text_average']    = 'Average';
$_['text_sum']    = 'Sum';
$_['text_group_desc'] = 'Group Desc';
$_['text_partial']    = 'Partial Block';
$_['text_no_unit_row']    = 'There is nothing to show, please click `Add New` button to add!';
$_['text_remove']    = 'Remove';
$_['text_sort_manual']    = 'Manual';
$_['text_sort_price_asc']    = 'Ascending by Price';
$_['text_sort_price_desc']    = 'Descending by Price';
$_['text_sort_name_asc']    = 'Ascending by name';
$_['text_sort_name_desc']    = 'Descending by name';
$_['text_group_type']    = 'Group Type';
$_['text_group_name']    = 'Group Name';
$_['text_group_id'] = 'Group ID';
$_['text_mode_and']    = 'AND';
$_['text_mode_or']    = 'OR';
$_['text_ingore_product']    = 'These product rules will be not used to restrict this fee/discount.';
$_['text_export'] = 'Export';
$_['text_import'] = 'Start Import';
$_['text_action'] = 'Action';
$_['text_estimator_country']    = 'Country';
$_['text_estimator_zone']    = 'Region / State';
$_['text_estimator_postal']    = 'Postcode';
$_['text_method_specific']    = 'Only take the products into account according to the product rules';
$_['text_disable_other'] = 'It will disable other fee/discount methods and only keep this fee active.';
$_['text_applied_sub'] = 'Cost of the products for which an X-FeePro discount already applied';

$_['text_eq_no_block'] = 'Total number of `Per unit block` if it is available';
$_['text_eq_cart_total'] = 'Cost of all cart\'s products';
$_['text_eq_cart_weight'] = 'Weight of all cart\'s products';
$_['text_eq_cart_qnty'] = 'Quantity of all cart\'s products';
$_['text_eq_cart_vol'] = 'Volume of all cart\'s products';
$_['text_eq_method_total'] = 'Cost of the products those are valid according to the products rule.';
$_['text_eq_method_weight'] = 'Weight of the products those are valid according to the products rule.';
$_['text_eq_method_qnty'] = 'Quantity of the products those are valid according to the products rule.';
$_['text_eq_method_vol'] = 'Volume of the products those are valid according to the products rule.';
$_['text_eq_shipping'] = 'Shipping cost';
$_['text_eq_xfeepro'] = 'Evaluated Fee/Discount amount found before applying the final equation.';
$_['text_eq_modifier'] = 'Value of the Modifier of the price adjustment section';
$_['text_eq_no_man'] = 'Total number of unique manufacturers';
$_['text_eq_no_loc'] = 'Total number of unique locations';
$_['text_eq_no_product'] = 'Total number of unique products';
$_['text_eq_no_product_method'] = 'Total number of unique products as per products rule';
$_['text_eq_no_free_product'] = 'Total number of free products in the shopping cart';
$_['text_eq_no_cat'] = 'Total number of unique categories';
$_['text_eq_coupon'] = 'Applied Coupon Value';
$_['text_eq_reward'] = 'Applied Reward Amount';
$_['text_eq_ref_price'] = 'You can also insert value of other xfeepro by using the their ID. For example, ID of this fee/discount is %s and you can place this {xfeepro%s} placeholder into the equation field of the other fee/discount.';
$_['text_eq_cart_total_tax'] = 'Cost of all cart\'s products including tax';
$_['text_eq_method_total_tax'] = 'Cost of the products including tax which are valid according to the products rule';
$_['text_eq_height']    = 'Denotes minimum, maximun and accrued height of the cart products';
$_['text_eq_width']    = 'Denotes minimum, maximun and accrued width of the cart products';
$_['text_eq_length']    = 'Denotes minimum, maximun and accrued length of the cart products';
$_['text_eq_all']    = 'Available when `Fee/Discount By` is Equation Value. Placeholders denote the respective parameter value of the product. It will iterate provided ranges over products and sup up cost of all matched products.';
$_['text_eq_any']    = 'Available in Ternary operator only. Placeholders denote the respective parameter value of the product. It will iterate the equation over products. By default, it will stop iterating once it finds a valid product. However, If you want to sum up the cost for every product that fulfills the condition, you must put a @ sign at the very beginning of the equation. 
<br>Example 1. @ {anyProductPrice} > 100 ? {xfeepro} : -1 
<br>It will iterate over all products and sum up the value. Note: the initial value of the {xfeepro} will be determined from the above price fields. 
<br>Example 2.  {anyProductPrice} > 100 ? 99 : -1 
<br>The fee/discount will be $99 if any cart products have a price of over $100.';
$_['text_eq_grand_wo_tax'] = 'Grand Total without Tax/Vat';
$_['text_eq_grand_round'] = 'Rounded Grand Total';
$_['text_fake']    = 'Make it fake i.e. will not affect order-total but displays on summary';
$_['text_hidden']  = 'Make it hidden i.e. will NOT display on order summary but affects order-total';
$_['text_first']   = 'This fee/discount will valid only for customers first order';
$_['text_logged'] = 'Will be available to logged customers only';
$_['text_disable'] = 'Make all fees/discount including itself unavailable if this fee became active';
$_['text_eq_dimension'] = 'Evaluated Dimensional Weight';
$_['text_eq_volumetric'] = 'Evaluated Volumetric Weight';
$_['text_address_shipping'] = 'Shipping, Billing';
$_['text_address_billing'] = 'Billing, Shipping';
$_['text_up'] = 'Ceiling';
$_['text_down'] = 'Flooring';
$_['text_fraction'] = 'Fraction';
$_['text_eq_block_asc'] = 'Total Price of the products that falls under the `block` after sorting them ascendingly.';
$_['text_eq_block_desc'] = 'Total Price of the products that falls under the `block` after sorting them descendingly';
$_['text_eq_no_block_asc'] = 'Total Price of Nth products after sorting them ascendingly where N is the value of `No. of Blocks`.';
$_['text_eq_no_block_desc'] = 'Total Price of Nth products after sorting them descendingly where N is the value of `No. of Blocks`.';
$_['text_eq_highest'] = 'Highest product price of the cart';
$_['text_eq_lowest'] = 'Lowest product price of the cart';
$_['text_eq_average'] = 'Average product price of the cart';
$_['text_eq_highest_qnty'] = 'Quantity of the `highest product price` of the cart';
$_['text_eq_lowest_qnty'] = 'Quantity of the `lowest product price` of the cart';
$_['text_total_special'] = 'SubTotal + Tax of SubTotal - Cost of Discounted Products';
$_['text_self_sub'] = 'Price Of Individual Product';
$_['text_self_total'] = 'Price Of Individual Product + Tax';
$_['text_eq_non_method_sub'] = 'Subtotal of the products that does not satisfy proudct rules';
$_['text_eq_non_method_qnty'] = 'Quantity of the products that does not satisfy proudct rules';
$_['text_original_total'] = 'Actual SubTotal i.e. without discount';
$_['text_xlevel_discount'] = 'The discount amount created by X-Level module';
$_['text_xdiscount_discount'] = 'The discount amount created by X-Discount module';
$_['text_product_rules'] = 'Please <a href="https://docs.google.com/document/d/1u8KUVlbzO2mOAOsStxPqB_EIQXtZCUqQcw2-p8rFH8M/edit" target="_blank">check this document</a> to understand the difference between various modes.';
$_['text_product_rules_advanced'] = 'In most cases, you don\'t need to configure the following options, and it works with default settings. However, you can adjust them if you feel you need them.';
// Global text across mods
$_['text_documentation']   = 'Documentation';
$_['text_update']    = 'Check For Update';
$_['text_support']    = 'Request For Support';
$_['text_blog_post']    = 'Check out Blog Posts to know more details';
$_['text_ocm_extension']    = 'Check out our other extensions';

// Placeholder 
$_['placeholder_name'] = 'Enter fee/discount title';
$_['placeholder_additional'] = 'Additional Price';
$_['placeholder_additional_per'] = 'Per Unit Block e.g 1';
$_['placeholder_additional_limit'] = 'Max Limit';
$_['placeholder_rate_min'] = 'Minimum Amount';
$_['placeholder_rate_max'] = 'Maximum Amount';
$_['placeholder_rate_add'] = 'Modifier e.g. +5';
$_['placeholder_equation'] = 'e.g. {subTotal}-5*{quantity}';
$_['placeholder_cart_adjust'] = 'e.g. +100 or 100 ';
$_['placeholder_max_height'] = 'Package height';
$_['placeholder_max_width'] = 'Package width';
$_['placeholder_max_length'] = 'Package length'; 
$_['placeholder_group_name'] = 'e.g. xyz Or @ or @$';
$_['placeholder_cost'] = 'e.g. 10% or -10% or 10 or -10';

// More Help
$_['more_zone'] = 'Please select country first to display country wise zones';
$_['more_dimensional_factor'] = 'Note: X-Feepro consider this equation. For Dimensional Weight = (volume / factor) x weight. For Volumetric Weight = (volume / factor)';
$_['more_equation'] = 'You can also insert the value of other xfeepro by using their placeholder. For example, ID of this fee/discount is <id>0</id> and placeholder is {xfeepro<id>0</id>}';


// Help
$_['help_method_group']    = 'You can divide your fee/discount up to 10 groups and can be applied different operation to the group. Here are available group mode <br /><b>Lowest</b>: Only fee with lowest cost would be shown .<br /><b>Highest</b>: Only fee with highest cost would be shown.<br /><b>Average</b>: All fees costs would be averaged.<br /><b>Sum</b>: All fees would be summed.';
$_['help_group_name']       = 'If you provide any name, It will show group name instead of fee/discount name during checkout. It is possible to place original name and prices in the group name. To put name, price and name-price pair, you have to put @, $ and @$ respectively.';
$_['help_name']       = 'The fee/discount title to be displayed. If you want to show the fee/discount amount in percentage, please include %s in it e.g. Discount (%s).';
$_['help_sort_order']       = 'Sorting order amoung fees created by X-Feepro';
$_['help_status']       = 'Enable/Disable this particular fee only';
$_['help_store']  = $_['help_zone'] = $_['help_customer_group'] = $_['help_currency'] = $_['help_geo_zone'] = $_['help_country'] = $_['help_payment'] = $_['help_shipping'] = $_['help_city_all'] = $_['help_postal_all'] = $_['help_coupon_all'] = $_['help_days'] = $_['help_customer_all'] = $_['help_custom_all'] = 'If you want to specify any particular items, please uncheck `For Any` to show the available options';
$_['help_customer_rule'] = $_['help_coupon_rule'] = $_['help_postal_rule'] = $_['help_city_rule']= '<b>Inlcusive:</b> Fee will appear for the entered items only. <b>Exclusive:</b> Fee will appear for non-entered items only.';
$_['help_weight'] = $_['help_quantity'] = $_['help_order_total']  = 'Inclusive value. Additional restriction rule if you want to use. If you don\'t need, just keep blank. Noted that this rule does not respect product rules i.e. it would consider whole cart value.';
$_['help_display'] = 'For admin use only. It helps you to differentiate a fee/discount from others sharing same name.';
$_['help_city']  = 'Multiple cities are supported. Newline/Comma Separated';
$_['help_coupon']       = 'Please enter coupon codes that would be valid for this fee. Multiple coupons are supported. Newline/Comma Separated';
$_['help_category'] = $_['help_product'] =  $_['help_manufacturer_rule'] = $_['help_option'] = $_['help_attribute'] =  $_['help_location_rule'] = '<b>For any ones</b>: Valid for any items.<br /><b>Any of the selected ones</b>: Shopping cart must contain at least one of the selected items to become it available. However, If the cart contains any non-selected items at the same time, it will be unavailable.<br /><b>Any of the selected ones along with others</b>: Shopping cart must contain at least one of the selected items to become it available regardless of the presence of non-selected items.<b>Must have selected ones</b>: Shopping cart must contain ALL the selected items to become it available. However, If the cart contains any non-selected items at the same time, it will be unavailable.<br /><b>Must have selected ones along with others</b>: Shopping cart must contain ALL the selected items to become it available regardless of the presence of non-selected items.<br /><b>Except for the selected ones</b>: Shopping cart must not contain any of the selected items to become it available.<br /><b>Except for the selected ones but allowed with others</b>: Shopping cart must not contain any of the selected items to become it available. However, if the cart contains any of the non-selected items at the same time, it will be available.';
$_['help_time']       = 'Please set time period for this fee/discount. Note: Server time will be considered. Server time now: %s';
$_['help_date']    = 'If you want to restrict fee/discount by date range, please choose applicable dates. Note: Server date will be considered. Server Date: %s';
$_['help_postal']='Newline/Comma Separated. Wildcards support (*, ?) and Range Support. <br /><b>Example:</b><br />12345,443300-443399,9843*,875*22,45433?,S3432?2 <br /><b>Explanation</b>:<br /> 12345: A single Postal Code <br /> 443300-443399: Postal Code start from 443300 to 443399<br /> 9843*: Any code that starts with 9843 <br />875*22:  Any code that starts with 875 and ends by 22 <br /> 45433?: Any code that start with 45433 and ends by any single alpha-numeric char. <br /> SE-1-10: Postal Code start from 1 to 10 with prefix SE i.e SE9 <br /> PA-1-10-NK: Postal Code start from 1 to 10 with prefix PK and suffix NK i.e PA9NK';
$_['help_additional'] ='If the shopping cart value is out of the above ranges, X-Feepro will calculate fee/discount for additional items based on these fields. If you don\'t want to auto calculate, keep it blank. Default Max Limit is unlimited.';
$_['help_module_status']       = 'Enable/Disable X-Feepro';
$_['help_module_sort_order']       = 'Sorting Order in respective to other Order Total modules';
$_['help_debug']       = 'It would show the rule names that restricted a fee/discount from showing up during checkout through live debug option found in the global setting tab.';

$_['help_unit_start']       = 'Counting will start from this value';
$_['help_unit_end']       = 'Counting will end in this value';
$_['help_unit_price']       = 'If \'<i>Per Unit Block</i>\' is 0, this field denotes the price for the entered range. <br /> If \'<i>Per Unit Block</i>\' is greater than 0, this field  denotes the price for that block. Percentage is allowed e.g. 5 or 5%. For discount enter value with negative sign e.g. -10%';
$_['help_unit_ppu']       = '<b>Per unit block</b>: It means cost for every N items what is referred as block or set in X-Feepro. If you want to calculate fee/discount for every set of items, you need this option. For example, if you want to charge $5 for every 3 items up to the total quanity 10 then you will have to set: Start=0, End=10, Cost=5 and Per unit block=3. Simply <i>Per Unit Block</i> means price for every N units where unit could be quanity or weight or volume etc depending on the `Fee/Discount By`';
$_['help_partial'] = 'Define how to treat if it finds the partial block. The `Ceiling` rounds up to the next block, the `Flooring` ignore the fraction value and the `Fraction` will count block with the fraction. Ignore this option if the `Per unit block` is zero or empty';
$_['help_export'] = 'Export X-Feepro data';
$_['help_product_unit_type'] = 'Define how the `per unit` would would calculate';
$_['help_map_api'] = 'If you want to set fee/discount by distance, please enter your google map API key. Also you must set Geocode on the store setting.';
$_['help_address']    = 'Define which address will be prioritized to validate the rules relating to address.';
$_['help_import'] = 'Warning! importing will overwrite your existing data.';
$_['help_inc_vat']    = 'This option will show fee/discount with vat/tax';
$_['help_group']    = 'If you want to make group with other fee, please select the group. You can define group mode in the Global setting tab -> Group Option';
$_['help_product_or'] = 'Define how product rules would be treated in case of having more than one product rule. If you are not sure, please just keep the default value `OR`.';
$_['help_ingore_product_rule'] = 'If you checked this option, Product Rules will not be validated anymore to restrict this fee/discount rather it will be used just for the purpose of fee/discount calculation.';
$_['help_hide'] = 'Make unavailable other fee/discount on the time of showing itself';
$_['help_hide_inactive'] = 'Make unavailable other fee/discount on the time of dying itself';
$_['help_rate_type'] = 'Define the parameter you want to calculate fee/discount based on. In case of Equation Value, you must define equation on the equation field below.';
$_['help_method_specific'] = 'By default, it takes products during calculation as per product rules. If you want to take all cart products into account instead, uncheck this option.';
$_['help_rate_percent']       = 'Select the parameter of how the percentage value would be calculated. The parameter `Price Of Individual Product` is available when the `Fee/Discount By` is `Equation` only.';
$_['help_price_adjustment']       = 'You can adjust the final fee/discount. <br /><b>Min</b>: If final fee is less than Min, then Min value will be considered . <br /><b>Max</b>: If final fee is greater than Max, then Max value will be counted. <br /><b>Modifier</b>: Any value with an valid operator(+,-,*,/) which will be added/multiplied/subtracted/divided respectively with the fee/discount depends on operator. e.g: +5.2 means 5.2 will be added to the final price';
$_['help_equation']  = 'Any arithmetic equation. If provided, final fee/discount cost would be adjusted based on result returned by this equation unless `Fee/Discount By` is `Equation Value`. If the equation returned zero(0), fee/discount would be unavailable. Ternary Operation is allowed e.g  {subTotal} > 200 ? 0 : 20';
$_['help_cart_adjust'] ='If you want to adjust i.e. add/substruct/multiply/divide shopping cart value right before calculation of the fee/discount, enter the value with an arithmetic operator. For example, if you want to add additional 200g to the shopping cart weight, enter +200. Or if you want to deduct 200g, you will have to enter -200. Percentage is allowed. e.g +10%.';
$_['help_rate_final']       = '<b>Single</b>: It counts the cost of the respective range row where the shopping cart value fall under. <br /> <b>Cumulative</b>: It accrues/cumulate the cost of the rows from starting to the active row where shopping cart value fall under. Note: Shopping cart value means either Weight or Total or Volume etc depending on `Fee/Discount By` above';
$_['help_method_id']  = 'Placeholder of this fee/discount is {xfeepro%s} to use in the equation.' ;
$_['help_cost'] = 'Percentage is allowed. For discount, please enter value with negative sign. e.g -10% Or 10% or 10';
$_['help_tax_class_id']    = 'Select the Tax Class to apply to the amount';
$_['help_fake']    = 'Fake Fee will show on the Order total section like other fee but will not add upp to the Order Total Amount i.e. it will not affect Order Total amount.';
$_['help_customers'] = 'Select applicable customers for this method';
$_['help_disable'] = 'It will make all fee/discounts unavailable if all the rules of this validate true.';
$_['help_first']    = 'Fee/Discount will available for customers first purchase only.';
$_['help_disable_other'] = 'If you enabled this option for several methods, the first item encountered will prevail over others.';
$_['help_custom'] = 'Select the values of the custom Field you want to make this method available to';
$_['help_hidden']    = 'Hidden Fee will be added up into Order total but would not show on the Order summary.';
$_['help_logged']    = 'Fee/Discount will be available to logged customers only';
// Error & Success
$_['text_success']     = 'Success: You have modified X-Feepro!';
$_['error_permission'] = 'Warning: You do not have permission to modify X-Feepro!';
$_['error_filetype'] = 'Invalid file type. Only CSV allowed';
$_['error_upload'] = 'Error! unable to upload';
$_['error_no_data'] = 'No data found to import';
$_['error_partial'] = 'Partial uploading error. Please check your upload size limit.';
$_['error_import'] = 'Please select a valid file to import';