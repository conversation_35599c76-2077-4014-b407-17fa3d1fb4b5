<?php
// Heading
$_['heading_title']               = 'Power Image Manager';

// Text
$_['text_module']                 = 'Modules';
$_['text_success']                = 'Success: You have modified module Power Image Manager!';

// Entry
$_['entry_delete_def_image']      = '<span data-toggle="tooltip" title="If set to NO, the default image will be keeped as additional image.">Delete Default image from additional images</span>';

$_['text_yes']                    = "Yes";
$_['text_no']                     = "No";
$_['entry_status']                = 'Status:';

// patches
$_['entry_miu_patch']             = '<span data-toggle="tooltip" title="Enable this if you have Multi Image Uploader installed."> Multi Image Uploader Patch: </span>';

//options
$_['entry_dimensions']            = 'Manager size (WxH):';
$_['entry_language']              = 'Language: ';

// Root options
$_['entry_copyOverwrite']         = '<span data-toggle="tooltip" title="Replace files on paste or give new names to pasted files">Copy Overwrite:</span>';
$_['entry_uploadOverwrite']       = '<span data-toggle="tooltip" title="Replace files on upload or give them new names"> Upload Overwrite: </span>';
$_['entry_uploadMaxSize']         = '<span data-toggle="tooltip" title="Maximum upload file size in MegaBytes. <u>Note you still have to configure PHP files upload limits.</u>">Upload Max Size: </span>';


// Client options
$_['entry_defaultView']           = 'Default View:';
$_['entry_dragUploadAllow']       = 'Allow to drag and drop to upload: ';
$_['entry_loadTmbs']              = '<span data-toggle="tooltip" title="Amount of thumbnails to create per one request">Load thumbs: </span>';

// tabs
$_['tab_general']                 = 'General';
$_['tab_volume']                  = 'Volumes';
$_['tab_module']                  = 'Modules';
$_['tab_help']                    = 'Help';
$_['text_enabled']                = 'Enabled';
$_['text_disabled']               = 'Disabled';

// Text
$_['text_success']                = 'Success: You have modified modules!';
$_['text_module_installed']       = 'Success: You have installed %s module!';
$_['text_volume_installed']       = 'Success: You have installed %s volume!';

$_['text_module_uninstalled']     = 'Success: You have uninstalled %s module!';
$_['text_volume_uninstalled']     = 'Success: You have uninstalled %s volume!';
$_['text_confirm']                = 'Are you sure?';
$_['text_layout']                 = 'After you have installed and configured a module you can add it to a layout <a href="%s" class="alert-link">here</a>!';
$_['text_add']                    = 'Add Module';
$_['text_list']                   = 'Module List';

// Column
$_['column_name']                 = 'Module Name';
$_['column_description']          = 'Description';
$_['column_action']               = 'Action';

// Entry
$_['entry_code']                  = 'Module';
$_['entry_name']                  = 'Module Name';

// Mods
$_['text_AutoResize']             = 'This plugin will auto resize your images before uploading';
$_['text_Normalizer']             = '';
$_['text_Sanitizer']              = 'Santizer will replace all special characers in your file names with "_". This way you won\'t have to worry about broken links in yor images';
$_['text_Watermark']              = 'Add a watermark before uploading files. Warning! Watermarks are built in the images and cannot be removed after that.';

// vols
$_['text_volume_LocalFileSystem'] = 'The default driver for accessing the local file system';
$_['text_volume_MySQL']           = 'MySQL - stores files inside the database.';
$_['text_volume_VolumeS3']        = 'Connects to Amazon S3';

$_['error_mui']                   = 'Error: You are trying to enable a patch without having Multi Image Uplodaer module installed.';

?>