<?php
// Heading
$_['heading_title']    = 'Advanced QuickBook Online Opencart Connector';

// Text
$_['text_configuration']   = 'Advanced QuickBook Configuration';
$_['text_adv_quickbook']   = 'Advanced QuickBooks';
$_['text_customer_advqb'] = 'Sync Customer';
$_['text_product_advqb']  = 'Sync Product';
$_['text_order_advqb']    = 'Sync Order';
$_['text_extension']   = 'Extensions';
$_['text_success']     = 'Success: You have modified Advanced QuickBook Online Opencart Connector module!';
$_['text_edit']        = 'Edit Advanced QuickBook Online Opencart Connector Module';
$_['text_sales']           = 'Sales Receipt';
$_['text_invoice']         = 'Invoice';
$_['text_estimate']        = 'Estimate';
$_['text_credit']          = 'Credit Memo';
$_['text_create_account']  = 'Click <a href="https://developer.intuit.com/" target="blank">here</a> to create QuickBook account.';
$_['text_create_keys']     = 'Click <a href="https://developer.intuit.com/app/developer/playground" target="blank">here</a> to generate QuickBook keys manually.';

// Entry
$_['entry_status']          = 'Status';
$_['entry_sandbox']         = 'Sandbox';
$_['entry_client_key']      = 'Client ID';
$_['entry_client_secret']   = 'Client Secret';
$_['entry_access_token']    = 'Access Token';
$_['entry_refresh_token']   = 'Refresh Token';
$_['entry_realmid']         = 'Company ID';
$_['entry_asset']           = 'Asset Account';
$_['entry_expense']         = 'Expense Account';
$_['entry_discount']        = 'Discount Account';
$_['entry_income']          = 'Income Account';
$_['entry_tax']             = 'Tax Code';
$_['entry_sandbox_help']    = 'Enable if you are using sandbox or development keys from QuickBook';
$_['entry_slot']            = 'Batch';
$_['entry_redirect_uri']    = 'Redirect URI';
$_['entry_order_date']      = 'Start date of Order Sync';
$_['entry_slot_help']       = 'Enter the number between 5 and 50 that will be used to sync the records at one batch';
$_['entry_auto_sync']            = 'Auto Sync';
$_['entry_auto_sync_help']       = 'If enabled then customers, products and orders will be automatically sync to QuickBook when they will be created at opencart end';
$_['entry_order_mapping']            = 'Mapping';
$_['entry_order_mapping_help']       = 'Select QuickBook transaction type for mapping opencart order';
$_['entry_transaction_prefix']            = 'Transaction Number Prefix';
$_['entry_transaction_prefix_help']       = 'The prefix will be added in the transaction number. For example if prefix is OC and order id is 100 then QuickBook transaction number will be displayed as OC100. The default value is OC-.';
$_['entry_order_status']            = 'Order Status';
$_['help_order_status']             = 'Select the order statuses for which you want to sync the orders to quickbooks online. Do not select any order status if you want to sync all orders.';
$_['entry_paid_order_status']       = 'Paid Order Status';
$_['help_paid_order_status']        = 'Select the order statuses for which you want to sync the orders to quickbooks online as paid.';
$_['text_select_all ']              = 'Select All';
$_['text_unselect_all']             = 'Unselect All';

// Error
$_['error_permission']      = 'Warning: You do not have permission to modify Advanced QuickBook Online Opencart Connector module!';
$_['error_client_key']      = 'Client Key Required';
$_['error_client_secret']   = 'Client Secret Required';
$_['error_access_token']    = 'Access Token Required';
$_['error_refresh_token']   = 'Refresh Token Required';
$_['error_realmid']         = 'Company/realm Id Required';
$_['error_slot']            = 'Slot must be between 5 and 50';
