<?php
$_['heading_title']            = 'Watermark';
$_['text_edit']                = 'Watermark';
$_['entry_marginRight']        = 'Margin Right';
$_['entry_marginBottom']       = 'Margin Bottom';
$_['entry_quality']            = 'Watermark Quality';
$_['entry_transparency']       = 'Transparency';
$_['entry_targetMinPixel']     = 'Min Pixels to apply';
$_['entry_source']             = 'Watermark Image';
$_['button_save']              = 'Save';
$_['button_cancel']            = 'Cancel';
$_['entry_status']             = 'Status';
$_['entry_force_transparent']  = '<span data-toggle="tooltip" title="Check this if your watermark does not get transparent."> Force Transparency: </span>';
$_['text_success']             = 'Success: You have modified module Watermark!';
?>