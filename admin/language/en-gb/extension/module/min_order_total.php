<?php
// Heading
$_['heading_title']          = 'Minimum Order Total';

// Text
$_['text_extension']         = 'Extensions';
$_['text_success']           = 'Success: You have modified module Minimum Order Total!';
$_['text_edit']              = 'Edit Minimum Order Total';
$_['text_use_subtotal']      = 'Subtotal (without taxes, vouchers, discounts)';
$_['text_use_total']         = 'Total (inclusive taxes, vouchers, discounts)';

// Tab
$_['tab_setting']            = 'Settings'; 
$_['tab_help']               = 'Help'; 

// Entry
$_['entry_status']           = 'Status'; 
$_['entry_total_type']       = 'Total type'; 
$_['entry_min_order_amount'] = 'Min order amount'; 
$_['entry_subtotal_warning'] = 'Alert Message (subtotal)';
$_['entry_total_warning']    = 'Alert Message (total)';

// Help
$_['help_min_order_amount']  = 'If order total is below this value, customer is not allowed to go to checkout';
$_['help_subtotal_warning']  = 'Ex: Order Subtotal need to be at least {min_order_total} to allow checkout';
$_['help_total_warning']     = 'Ex: Order Total need to be at least {min_order_total} to allow checkout';

// Error
$_['error_permission']       = 'Warning: You do not have permission to modify module Minimum Order Total!';
$_['error_min_order_amount'] = 'Min order amount does not appear to be valid';
$_['error_subtotal_warning'] = 'Message example: Order Subtotal need to be at least {min_order_total} to allow checkout';
$_['error_total_warning']    = 'Message example: Order Total need to be at least {min_order_total} to allow checkout';
$_['error_no_required_value']= 'Please use {min_order_total} in message to let customer to know minimum allowed value for order.';