<?php
// Text
$_['text_saving']                   = 'Saving ...';
$_['text_copying']                  = 'Copying ...';
$_['text_deleting']                 = 'Deleting ...';
$_['text_loading']                  = 'Loading ...';
$_['text_sending']                  = 'Sending ...';
$_['text_click_edit']               = 'Click to edit ...';
$_['text_double_click_edit']        = 'Double-click to edit ...';
$_['text_image_manager']            = 'Image Manager';
$_['text_batch_edit']               = 'Batch edit';
$_['text_autocomplete']             = 'Autocomplete';
$_['text_notify_customer']          = 'Notify Customer';
$_['text_confirm_delete']           = 'Confirm Deletion!';
$_['text_are_you_sure']             = 'Delete cannot be undone! Are you sure you want to proceed?';
$_['text_toggle_navigation']        = 'Toggle navigation';
$_['text_toggle_dropdown']          = 'Toggle dropdown';
$_['text_filter']                   = 'Filter';
$_['text_clear_filter']             = 'Clear filter';
$_['text_first_name']               = 'First name';
$_['text_last_name']                = 'Last name';
$_['text_missing_order']            = '--Missing Order--';
$_['text_custom_filter']            = 'Custom filter';

// Buttons
$_['button_add']                    = 'Add New';
$_['button_ok']                     = 'OK';

// Actions
$_['action_edit']                   = 'Edit';
$_['action_view']                   = 'View';
$_['action_delete']                 = 'Delete';

// Columns
$_['column_action']                 = 'Action';
$_['column_amount']                 = 'Amount';
$_['column_code']                   = 'Code';
$_['column_comment']                = 'Comment';
$_['column_customer']               = 'Customer';
$_['column_customer_group']         = 'Customer Group';
$_['column_customer_id']            = 'Customer ID';
$_['column_date_added']             = 'Date Added';
$_['column_date_modified']          = 'Date Modified';
$_['column_date_ordered']           = 'Date Ordered';
$_['column_email']                  = 'E-Mail';
$_['column_fax']                    = 'Fax';
$_['column_from_email']             = 'From E-mail';
$_['column_from_name']              = 'From Name';
$_['column_image']                  = 'Image';
$_['column_id']                     = 'ID';
$_['column_ip']                     = 'IP Address';
$_['column_message']                = 'Message';
$_['column_model']                  = 'Model';
$_['column_name']                   = 'Name';
$_['column_newsletter']             = 'Newsletter';
$_['column_opened']                 = 'Opened';
$_['column_order_id']               = 'Order ID';
$_['column_product']                = 'Product';
$_['column_product_id']             = 'Product ID';
$_['column_quantity']               = 'Quantity';
$_['column_return_id']              = 'Return ID';
$_['column_return_action']          = 'Return Action';
$_['column_return_reason']          = 'Return Reason';
$_['column_return_status']          = 'Return Status';
$_['column_safe']                   = 'Safe';
$_['column_selector']               = '<selector>';
$_['column_status']                 = 'Status';
$_['column_telephone']              = 'Telephone';
$_['column_theme']                  = 'Theme';
$_['column_to_email']               = 'To E-mail';
$_['column_to_name']                = 'To Name';

// Errors
$_['error_warning']                 = '<strong>Warning!</strong> Please check the form carefully for errors!';
$_['error_update']                  = '<strong>Warning!</strong> Updating the value failed!';
$_['error_load_popup']              = '<strong>Warning!</strong> Loading popup data failed!';
$_['error_ajax_request']            = 'An AJAX error occured!';
$_['error_batch_edit_email']        = 'Batch editing emails is not possible!';
$_['error_batch_edit_code']         = 'Batch editing codes is not possible!';
$_['error_date_format']             = '<strong>Warning!</strong> Date format is not recognized! Must be \'YYYY-MM-DD HH:MM\'';
$_['error_invalid_date']            = '<strong>Warning!</strong> Date is not valid!';
$_['error_expression']              = 'Invalid expression!';
