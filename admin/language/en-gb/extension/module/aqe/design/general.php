<?php
// Text
$_['text_saving']                   = 'Saving ...';
$_['text_copying']                  = 'Copying ...';
$_['text_deleting']                 = 'Deleting ...';
$_['text_loading']                  = 'Loading ...';
$_['text_sending']                  = 'Sending ...';
$_['text_click_edit']               = 'Click to edit ...';
$_['text_double_click_edit']        = 'Double-click to edit ...';
$_['text_image_manager']            = 'Image Manager';
$_['text_batch_edit']               = 'Batch edit';
$_['text_autocomplete']             = 'Autocomplete';
$_['text_confirm_delete']           = 'Confirm Deletion!';
$_['text_are_you_sure']             = 'Delete cannot be undone! Are you sure you want to proceed?';
$_['text_toggle_navigation']        = 'Toggle navigation';
$_['text_toggle_dropdown']          = 'Toggle dropdown';
$_['text_filter']                   = 'Filter';
$_['text_clear_filter']             = 'Clear filter';

// Buttons
$_['button_add']                    = 'Add New';
$_['button_ok']                     = 'OK';

// Actions
$_['action_edit']                   = 'Edit';

// Columns
$_['column_action']                 = 'Action';
$_['column_id']                     = 'ID';
$_['column_keyword']                = 'Keyword';
$_['column_language']               = 'Language';
$_['column_query']                  = 'Query';
$_['column_store']                  = 'Store';

// Errors
$_['error_warning']                 = '<strong>Warning!</strong> Please check the form carefully for errors!';
$_['error_update']                  = '<strong>Warning!</strong> Updating the value failed!';
$_['error_load_popup']              = '<strong>Warning!</strong> Loading popup data failed!';
$_['error_ajax_request']            = 'An AJAX error occured!';
$_['error_batch_edit_keyword']      = 'Batch editing keywords is not possible!';
$_['error_expression']              = 'Invalid expression!';
