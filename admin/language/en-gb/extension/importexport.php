<?php
// Heading
$_['heading_title']    = 'Import Export';

// Text
$_['text_extension']   = 'Extensions';
$_['text_success']     = 'Success: You have modified Import Export module!';
$_['text_edit']        = 'Edit Import Export Module';
$_['text_list']        = 'Case List';
$_['text_migration']        = 'Migration / Backup';
$_['text_cronjob']        = 'Cron job';
$_['text_all']        = 'All';
$_['text_user']        = 'User';
$_['text_add']        = 'Add';
$_['text_import']        = 'Import';
$_['text_export']        = 'Export';
$_['text_xls']        = 'XLS';
$_['text_csv']        = 'CSV';
$_['text_xlsx']        = 'XLSX';
$_['text_xml']        = 'XML';
$_['text_select_all']        = 'Select All';
$_['text_unselect_all']        = 'Unselect All';
$_['text_upload']        = 'Upload';
$_['text_choose']        = 'Choose';
$_['text_repeatdays']        = 'Repeat Days';
$_['text_save_seting']        = 'Save Setting';
$_['text_enabled']        = 'Enabled';
$_['text_disabled']        = 'Disabled';
$_['text_processed']        = 'Processed';
$_['text_edit1']        = 'Edit';
$_['text_no_results']        = 'No Results';
$_['text_exportconfig']        = 'Export Configuration';
$_['text_fileconfig']        = 'File Configuration';
$_['text_directdownload']        = 'Direct Download';
$_['text_externalserver']        = 'External Server';
$_['text_googlesheet']        = 'Google sheet';
$_['text_ftphost']        = 'Ftp Host';
$_['text_ftpport']        = 'Ftp Port';
$_['text_ftpusername']        = 'Ftp Username';
$_['text_ftppassword']        = 'Ftp Password';
$_['text_filename']        = 'File Name';
$_['text_fileroot']        = 'File Root';
$_['text_googlesheetapi']        = 'Google Sheet API';
$_['text_export_data']        = 'Choose Export table';
$_['text_step_sort_order']        = 'Sort Options';
$_['text_sort_order_field']        = 'Sort Order Field';
$_['text_mode']        = 'Mode';
$_['text_ascending']        = 'Ascending';
$_['text_descending']        = 'Descending';
$_['text_step_filters']        = 'Add Filters';
$_['text_store']        = 'Store';
$_['text_categories']        = 'Categories';
$_['text_language']        = 'Language';
$_['text_manufacturer']        = 'Manufacturer';
$_['text_field']        = 'Element';
$_['text_condition']        = 'Rule';
$_['text_value']        = 'Value';
$_['text_step_mapping']        = 'Choose Fields';
$_['text_cron_job_link']        = 'Cron Job Settings';
$_['text_schedulename']        = 'Schedule Name';
$_['text_scheduleinterval']        = 'Schedule Period';
$_['text_monthly']        = 'Monthly';
$_['text_weekly']        = 'Weekly';
$_['text_yearly']        = 'Yearly';
$_['text_sort_order']        = 'Sort Order';
$_['text_generate']        = 'Generate';
$_['text_save']        = 'Save';
$_['text_step3_mapping']        = 'Choose Fields';
$_['text_action']        = 'Action';
$_['text_step2_filters']        = 'Pre-Filters';
$_['text_import_data']        = 'Import Data';
$_['text_file_url']        = 'File URL';
$_['text_importconfig']        = 'Import Configurations';
$_['text_manuallyupload']        = 'Manually Upload';
$_['text_url']        = 'URL';
$_['text_get_file_from']        = 'Get file from';
$_['text_upload_file']        = 'Upload File';
$_['text_export_add']        = 'New Export';
$_['text_export_edit']        = 'Edit Export';
$_['text_import_add']        = 'New Import';
$_['text_import_edit']        = 'Edit Import';
$_['text_greater_equal']        = 'greater than or equal to';
$_['text_greater']        = 'greater than';
$_['text_equal']        = 'equal to';
$_['text_not_equal']        = 'not equal to';
$_['text_less_equal']        = 'less than or equal to';
$_['text_less']        = 'less than';
$_['text_advanceoption']        = 'Advance Options';
$_['text_addexport']        = 'New Export';
$_['text_addimport']        = 'New Import';
$_['text_import_data']        = 'Choose Import Table';
$_['text_import_type']        = 'Import Type';
$_['text_productid']        = 'Product ID';
$_['text_model']        = 'Model';
$_['text_default_table']        = 'Default Tables';
$_['text_custom_table']        = 'Custom Tables';
$_['text_file_format']        = 'File Format';
$_['text_fetch_image']        = 'Fetch Image';
$_['text_confirm']        = 'Are you sure??';
$_['text_incomplete']        = 'INCOMPLETE';
$_['text_complete']        = 'COMPLETE';
$_['text_filename']        = 'File name :';
$_['text_filesize']        = 'File size :';
$_['text_create']        = 'Created :';
$_['text_update']        = 'Updated :';
$_['text_delete']        = 'Deleted :';
$_['text_log']        = 'Log';
$_['text_processing']        = 'Processing..';
$_['text_export_processing']        = 'Export Processing';
$_['text_import_processing']        = 'Import Processing';
$_['text_select_table']        = 'Please Select Atleast One table';
$_['text_no_upload']        = 'Please Upload the file';
$_['text_import_success']        = 'Successfully Imported';
$_['text_stop_process']        = 'Process Stop';

// Entry
$_['entry_name']       = 'Name';
$_['entry_product']    = 'Products';
$_['entry_limit']      = 'Limit';
$_['entry_width']      = 'Width';
$_['entry_height']     = 'Height';
$_['entry_status']     = 'Status';
$_['entry_total_cases']     = 'Total Cases';
$_['entry_enabled_cases']     = 'Enabled Cases';
$_['entry_disabled_cases']     = 'Disabled Cases';
$_['entry_format']     = 'File Format';
$_['entry_exportfileto']     = 'Export File to';
$_['text_sheet_id']     = 'Sheet ID';
$_['text_sheet_composer']     = 'Sheet Composer Json code';

// Help
$_['button_remove']     = 'Remove';
$_['button_add']     = 'Add';

// Help
$_['help_product']     = '(Autocomplete)';

// Error
$_['error_permission'] = 'Warning: You do not have permission to modify Import Export module!';
$_['error_name']       = 'Name must be between 3 and 64 characters!';
$_['error_width']      = 'Width required!';
$_['error_height']     = 'Height required!';
$_['error_export_table']     = 'Table required!';
$_['error_warning']          = 'Warning: Please check the form carefully for errors!';
$_['error_import']          = 'Warning: Please Fill all the fields';
$_['error_select_once']          = 'Select atleast One';