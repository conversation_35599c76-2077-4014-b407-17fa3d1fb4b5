<?php
// Heading
$_['heading_title']			= 'Authorize.Net (SIM)';

// Text
$_['text_extension']		= 'Extensions';
$_['text_success']			= 'Success: You have modified Authorize.Net (SIM) account details!';
$_['text_edit']             = 'Edit Authorize.Net (SIM)';
$_['text_authorizenet_sim']	= '<a href="https://account.authorize.net/signUpNow?resellerID=26357" target="_BLANK"><img src="view/image/payment/authorizenet.png" alt="Authorize.Net" title="Authorize.Net" style="border: 1px solid #EEEEEE;" /></a>';

// Entry
$_['entry_merchant']		= 'Merchant ID';
$_['entry_key']				= 'Transaction Key';
$_['entry_callback']		= 'Relay Response URL';
$_['entry_md5']				= 'Signature Key/Hash';
$_['entry_test']			= 'Test Mode';
$_['entry_total']			= 'Total';
$_['entry_order_status']	= 'Order Status';
$_['entry_geo_zone']		= 'Geo Zone';
$_['entry_status']			= 'Status';
$_['entry_sort_order']		= 'Sort Order';

// Help
$_['help_callback']			= 'Please login and set this at <a href="https://secure.authorize.net" target="_blank" class="txtLink">https://secure.authorize.net</a>.';
$_['help_md5']				= 'The Hash feature enables you to authenticate that a transaction response is securely received from Authorize.Net. Please login and set this at <a href="https://secure.authorize.net" target="_blank" class="txtLink">https://secure.authorize.net</a>.(Optional)';
$_['help_total']			= 'The checkout total the order must reach before this payment method becomes active.';

// Error
$_['error_permission']		= 'Warning: You do not have permission to modify payment Authorize.Net (SIM)!';
$_['error_merchant']		= 'Merchant ID Required!';
$_['error_key']				= 'Transaction Key Required!';