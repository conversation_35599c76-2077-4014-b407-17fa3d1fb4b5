<?php
// Heading
$_['heading_title']		 = 'PayPal Pro';

// Text
$_['text_extension']     = 'Extensions';
$_['text_success']		 = 'Success: You have modified PayPal Website Payment Pro Checkout account details!';
$_['text_edit']          = 'Edit PayPal Pro';
$_['text_pp_pro']		 = '<a target="_BLANK" href="https://www.paypal.com/uk/mrb/pal=V4T754QB63XXL"><img src="view/image/payment/paypal.png" alt="PayPal Website Payment Pro" title="PayPal Website Payment Pro iFrame" style="border: 1px solid #EEEEEE;" /></a>';
$_['text_authorization'] = 'Authorization';
$_['text_sale']			 = 'Sale';

// Entry
$_['entry_username']	 = 'API Username';
$_['entry_password']	 = 'API Password';
$_['entry_signature']	 = 'API Signature';
$_['entry_test']		 = 'Test Mode';
$_['entry_transaction']	 = 'Transaction Method:';
$_['entry_total']		 = 'Total';
$_['entry_order_status'] = 'Order Status';
$_['entry_geo_zone']	 = 'Geo Zone';
$_['entry_status']		 = 'Status';
$_['entry_sort_order']	 = 'Sort Order';

// Help
$_['help_test']			 = 'Use the live or testing (sandbox) gateway server to process transactions?';
$_['help_total']		 = 'The checkout total the order must reach before this payment method becomes active';

// Error
$_['error_permission']	 = 'Warning: You do not have permission to modify payment PayPal Website Payment Pro Checkout!';
$_['error_username']	 = 'API Username Required!';
$_['error_password']	 = 'API Password Required!';
$_['error_signature']	 = 'API Signature Required!';