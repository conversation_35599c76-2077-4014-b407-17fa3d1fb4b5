<?php
// Heading
$_['heading_title']					 = 'PayPal Express Checkout';

// Text
$_['text_extension']				 = 'Extensions';
$_['text_success']				 	 = 'Success: You have modified PayPal Express Checkout account details!';
$_['text_edit']                      = 'Edit PayPal Express Checkout';
$_['text_pp_express']				 = '<a target="_BLANK" href="https://www.paypal.com/uk/mrb/pal=V4T754QB63XXL"><img src="view/image/payment/paypal.png" alt="PayPal Website Payment Pro" title="PayPal Website Payment Pro iFrame" style="border: 1px solid #EEEEEE;" /></a>';
$_['text_authorization']			 = 'Authorization';
$_['text_sale']						 = 'Sale';
$_['text_signup']                    = 'Sign up for PayPal - save your settings first as this page will be refreshed';
$_['text_sandbox']                   = 'Sign up for PayPal Sandbox - save your settings first as this page will be refreshed';
$_['text_configure_live']            = 'Configure Live';
$_['text_configure_sandbox']         = 'Configure Sandbox';
$_['text_show_advanced']             = 'Show Advanced';
$_['text_show_quick_setup']          = 'Show Quick Setup';
$_['text_quick_setup']             	 = 'Quick setup - Link an existing or create a new PayPal account to start accepting payments in minutes';
$_['text_paypal_consent']		 	 = 'By using the quick setup tool you allow PayPal to receive information about your store';
$_['text_success_connect']			 = 'Success: You have connected your PayPal account!';
$_['text_preferred_main']		 	 = 'Gives your buyers a simplified checkout experience on multiple devices that keeps them local to your website throughout the payment authorization process';
$_['text_learn_more']			 	 = '(Learn more)';
$_['text_preferred_li_1']			 = 'Start accepting PayPal in three clicks';
$_['text_preferred_li_2']			 = 'Accept payments from around the world';
$_['text_preferred_li_3']			 = 'Offer Express Checkout Shortcut, letting buyers checkout directly from your basket page';
$_['text_preferred_li_4']			 = 'Improve conversion with PayPal One Touch and In-Context checkout';
$_['text_connect_paypal']			 = 'Connect with PayPal';
$_['text_incontext_not_supported']	 = '* Not supported with In-Context Checkout';
$_['text_retrieve']	 				 = 'Your details have been entered from PayPal';
$_['text_enable_button']			 = 'We recommend offering PayPal Express Shortcut to maximise checkout conversion, this allows customers to use their PayPal address book and <strong>checkout is as little as three taps</strong> from the basket page. Click enable to install the extension and access the layout manager, you will ned to add "PayPal Express Checkout Button" to the checkout layout';

// Entry
$_['entry_username']				 = 'API Username';
$_['entry_password']				 = 'API Password';
$_['entry_signature']				 = 'API Signature';
$_['entry_sandbox_username']		 = 'API Sandbox Username';
$_['entry_sandbox_password']		 = 'API Sandbox Password';
$_['entry_sandbox_signature']		 = 'API Sandbox Signature';
$_['entry_ipn']						 = 'IPN URL';
$_['entry_test']					 = 'Test (Sandbox) Mode';
$_['entry_debug']					 = 'Debug logging';
$_['entry_currency']				 = 'Default currency';
$_['entry_recurring_cancel']	     = 'Allow customers to cancel recurring payments from account area';
$_['entry_transaction']		         = 'Settlement Type';
$_['entry_total']					 = 'Total';
$_['entry_geo_zone']				 = 'Geo Zone';
$_['entry_status']					 = 'Status';
$_['entry_sort_order']				 = 'Sort Order';
$_['entry_canceled_reversal_status'] = 'Canceled Reversal Status';
$_['entry_completed_status']		 = 'Completed Status';
$_['entry_denied_status']			 = 'Denied Status';
$_['entry_expired_status']			 = 'Expired Status';
$_['entry_failed_status']			 = 'Failed Status';
$_['entry_pending_status']			 = 'Pending Status';
$_['entry_processed_status']		 = 'Processed Status';
$_['entry_refunded_status']			 = 'Refunded Status';
$_['entry_reversed_status']			 = 'Reversed Status';
$_['entry_voided_status']			 = 'Voided Status';
$_['entry_allow_notes']				 = 'Allow Notes';
$_['entry_colour']	      			 = 'Page Background Colour';
$_['entry_logo']					 = 'Logo';
$_['entry_incontext']				 = 'Disable In-Context Checkout';

// Tab
$_['tab_api']				         = 'API Details';
$_['tab_order_status']				 = 'Order status';
$_['tab_checkout']					 = 'Checkout';

// Help
$_['help_ipn']						 = 'Required for subscriptions';
$_['help_total']					 = 'The checkout total the order must reach before this payment method becomes active';
$_['help_logo']						 = 'Max 750px(w) x 90px(h)<br />You should only use a logo if you have SSL set up.';
$_['help_colour']					 = '6 character HTML colour code';
$_['help_currency']					 = 'Used for transaction searches';

// Error
$_['error_permission']				 = 'Warning: You do not have permission to modify payment PayPal Express Checkout!';
$_['error_username']				 = 'API Username Required!';
$_['error_password']				 = 'API Password Required!';
$_['error_signature']				 = 'API Signature Required!';
$_['error_sandbox_username']	 	 = 'API Sandbox Username Required!';
$_['error_sandbox_password']		 = 'API Sandbox Password Required!';
$_['error_sandbox_signature']		 = 'API Sandbox Signature Required!';
$_['error_api']						 = 'Paypal Authorization Error';
$_['error_api_sandbox']				 = 'Paypal Sandbox Authorization Error';
$_['error_consent']				 	 = 'To use quick setup you need to permit PayPal to use your store information';
