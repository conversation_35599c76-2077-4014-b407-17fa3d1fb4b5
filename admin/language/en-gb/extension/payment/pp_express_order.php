<?php
// Text
$_['text_extension']		 = 'Payment Information';
$_['text_capture_status']	 = 'Capture status';
$_['text_amount_authorised'] = 'Amount authorised';
$_['text_amount_captured']	 = 'Amount captured';
$_['text_amount_refunded']	 = 'Amount refunded';
$_['text_transaction']		 = 'Transactions';
$_['text_complete']			 = 'Complete';
$_['text_confirm_void']		 = 'If you void you cannot capture any further funds';
$_['text_view']				 = 'View';
$_['text_refund']			 = 'Refund';
$_['text_resend']			 = 'Resend';
$_['text_success']           = 'Transaction was successfully sent';
$_['text_full_refund']		 = 'Full refund';
$_['text_partial_refund']	 = 'Partial refund';
$_['text_payment']		 	 = 'Payment';
$_['text_current_refunds']   = 'Refunds have already been done for this transaction. The max refund is';

// Column
$_['column_transaction']	 = 'Transaction ID';
$_['column_amount']			 = 'Amount';
$_['column_type']			 = 'Payment Type';
$_['column_status']			 = 'Status';
$_['column_pending_reason']	 = 'Pending Reason';
$_['column_date_added']		 = 'Date Added';
$_['column_action']			 = 'Action';

// Entry
$_['entry_capture_amount']	 = 'Capture amount';
$_['entry_capture_complete'] = 'Complete capture';
$_['entry_full_refund']		 = 'Full refund';
$_['entry_amount']			 = 'Amount';
$_['entry_note']             = 'Note';

// Help
$_['help_capture_complete']  = 'If this is a the last capture.';

// Tab
$_['tab_capture']		     = 'Capture';
$_['tab_refund']             = 'Refund';

// Button
$_['button_void']			 = 'Void';
$_['button_capture']		 = 'Capture';
$_['button_refund']		     = 'Issue refund';

// Error
$_['error_capture']		     = 'Enter an amount to capture';
$_['error_transaction']	     = 'Transaction could not be carried out!';
$_['error_not_found']	     = 'Transaction could not be found!';
$_['error_partial_amt']		 = 'You must enter a partial refund amount';