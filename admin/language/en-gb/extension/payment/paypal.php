<?php
// Heading
$_['heading_title']		 				= 'PayPal Commerce Platform';

// Text
$_['text_paypal']		 				= '<a target="_BLANK" href="https://www.paypal.com/uk/mrb/pal=V4T754QB63XXL"><img src="view/image/payment/paypal.png" alt="PayPal Commerce Platform" title="PayPal Commerce Platform" style="border: 1px solid #EEEEEE;" /></a>';
$_['text_extensions']     				= 'Extensions';
$_['text_edit']          				= 'Edit PayPal';
$_['text_general']				 	 	= 'General';
$_['text_order_status']				 	= 'Order Status';
$_['text_checkout_express']				= 'Checkout';
$_['text_checkout_card']				= 'Advanced Card';
$_['text_checkout_message']				= 'Pay Later Message';
$_['text_production']			 	 	= 'Production';
$_['text_sandbox']			 			= 'Sandbox';
$_['text_authorization']			 	= 'Authorization';
$_['text_sale']			 	 			= 'Sale';
$_['text_connect']						= 'Your seller account has been connected.<br />Client ID = %s<br />Secret = %s<br />Merchant ID = %s<br />If you would like to connect another account, please, disconnect.';
$_['text_currency_aud']					= 'Australian Dollar';
$_['text_currency_brl']					= 'Brazilian Real';
$_['text_currency_cad']					= 'Canadian Dollar';
$_['text_currency_czk']					= 'Czech Krone';
$_['text_currency_dkk']					= 'Danish Krone';
$_['text_currency_eur']					= 'Euro';
$_['text_currency_hkd']					= 'Hong Kong Dollar';
$_['text_currency_huf']					= 'Hungarian Forint';
$_['text_currency_inr']					= 'Indian Rupee';
$_['text_currency_ils']					= 'Israeli New Shekel';
$_['text_currency_jpy']					= 'Japanese Yen';
$_['text_currency_myr']					= 'Malaysian Ringgit';
$_['text_currency_mxn']					= 'Mexican Peso';
$_['text_currency_twd']					= 'New Taiwan Dollar';
$_['text_currency_nzd']					= 'New Zealand Dollar';
$_['text_currency_nok']					= 'Norwegian Krone';
$_['text_currency_php']					= 'Philippine peso';
$_['text_currency_pln']					= 'Polish Zloty';
$_['text_currency_gbp']					= 'Pound Sterling';
$_['text_currency_rub']					= 'Russian Ruble';
$_['text_currency_sgd']					= 'Singapore Dollar';
$_['text_currency_sek']					= 'Swedish Krone';
$_['text_currency_chf']					= 'Swiss Frank';
$_['text_currency_thb']					= 'Thai Baht';
$_['text_currency_usd']					= 'US Dollar';
$_['text_completed_status']				= 'Completed Status';
$_['text_denied_status']				= 'Denied Status';
$_['text_failed_status']				= 'Failed Status';
$_['text_pending_status']				= 'Pending Status';
$_['text_refunded_status']				= 'Refunded Status';
$_['text_reversed_status']				= 'Reversed Status';
$_['text_voided_status']				= 'Voided Status';
$_['text_align_left']					= 'Align Left';
$_['text_align_center']					= 'Align Center';
$_['text_align_right']					= 'Align Right';
$_['text_small']			 			= 'Small';
$_['text_medium']			 	 		= 'Medium';
$_['text_large']			 	 		= 'Large';
$_['text_responsive']			 	 	= 'Responsive';
$_['text_gold']			 	 			= 'Gold';
$_['text_blue']			 	 			= 'Blue';
$_['text_silver']			 	 		= 'Silver';
$_['text_white']			 	 		= 'White';
$_['text_black']			 	 		= 'Black';
$_['text_pill']			 	 			= 'Pill';
$_['text_rect']			 	 			= 'Rect';
$_['text_checkout']			 	 		= 'Checkout';
$_['text_pay']			 	 			= 'Pay';
$_['text_buy_now']			 	 		= 'Buy Now';
$_['text_pay_pal']			 	 		= 'PayPal';
$_['text_installment']			 	 	= 'Installment';
$_['text_text']							= 'Text Message';
$_['text_flex']							= 'Flexible Banner';
$_['text_accept']			 	 		= 'Accept';
$_['text_decline']			 	 		= 'Decline';
$_['text_recommended']			 	 	= '(recommended)';
$_['text_3ds_undefined']				= 'You have not required 3D Secure for the buyer or the card network did not require a 3D Secure.';
$_['text_3ds_error']					= 'An error occurred with the 3DS authentication system.';
$_['text_3ds_skipped_by_buyer'] 		= 'Buyer was presented the 3D Secure challenge but chose to skip the authentication.';
$_['text_3ds_failure']					= 'Buyer may have failed the challenge or the device was not verified.';
$_['text_3ds_bypassed']					= '3D Secure was skipped as authentication system did not require a challenge.';
$_['text_3ds_attempted']				= 'Card is not enrolled in 3D Secure as card issuing bank is not participating in 3D Secure.';
$_['text_3ds_unavailable']				= 'Issuing bank is not able to complete authentication.';
$_['text_3ds_card_ineligible'] 			= 'Card is not eligible for 3DS Secure authentication.';
$_['text_confirm']						= 'Are you sure?';

// Entry
$_['entry_connect']	 					= 'Connect';
$_['entry_checkout_express_status']		= 'Checkout';
$_['entry_checkout_card_status']		= 'Advanced Card';
$_['entry_checkout_message_status']		= 'Pay Later Message';
$_['entry_environment']				 	= 'Environment';
$_['entry_debug']				 		= 'Debug Logging';
$_['entry_transaction_method']	 		= 'Settlement Method';
$_['entry_total']		 				= 'Total';
$_['entry_geo_zone']	 				= 'Geo Zone';
$_['entry_status']		 				= 'Status';
$_['entry_sort_order']	 				= 'Sort Order';
$_['entry_currency_code']	 			= 'Currency';
$_['entry_currency_value']	 			= 'Currency Value';
$_['entry_smart_button'] 				= 'Smart Button';
$_['entry_button_align']     			= 'Button Align';
$_['entry_button_size'] 				= 'Button Size';
$_['entry_button_color'] 				= 'Button Color';
$_['entry_button_shape'] 				= 'Button Shape';
$_['entry_button_label'] 				= 'Button Label';
$_['entry_form_align']     				= 'Form Align';
$_['entry_form_size'] 					= 'Form Size';
$_['entry_secure_status'] 				= '3D Secure Status';
$_['entry_secure_scenario'] 			= '3D Secure Scenarios';
$_['entry_message_align']     			= 'Message Align';
$_['entry_message_size'] 				= 'Message Size';
$_['entry_message_layout'] 				= 'Message Layout';
$_['entry_message_text_color'] 			= 'Message Text Color';
$_['entry_message_text_size'] 			= 'Message Text Size';
$_['entry_message_flex_color'] 			= 'Message Banner Color';
$_['entry_message_flex_ratio'] 			= 'Message Banner Ratio';

// Help
$_['help_checkout_express']				= 'If your country is not available in the list when going through the PayPal onboarding experience please <a id="button_connect_express_checkout" href="%s" target="_blank" data-paypal-onboard-complete="onBoardedCallback">click here</a>.';
$_['help_checkout_express_status']		= 'When activated PayPal will display personalized Smart Buttons avalible to your customers based on their location.';
$_['help_checkout_card_status']			= 'PayPal verifies if you are eligible for advanced card payment and will display this option on the checkout step if available.';
$_['help_checkout_message_status']		= 'Add pay later messaging to your site.';
$_['help_total']		 				= 'The checkout total the order must reach before this payment method becomes active.';
$_['help_currency_code']		 		= 'Select the default currency for PayPal.';
$_['help_currency_value']		 		= 'Set to 1.00000 if this is your default currency.';
$_['help_secure_status'] 				= '3D Secure enables you to authenticate card holders through card issuers. It reduces the likelihood of fraud when you use supported cards and improves transaction perfomance. A successful 3D Secure authentication can shift liability for chargebacks due to fraud from you -the merchant- to the card issuer.';
$_['help_secure_scenario'] 				= '3D Secure authentication is perfomed only if the card is enrolled for the service. In scenarios where the 3D Secure authentication has not been successful, you have the option to complete the payment at your own risk, meaning that you -the merchant- will be liable in case of a chargeback.';

// Button
$_['button_connect'] 					= 'Connect with PayPal';
$_['button_disconnect'] 				= 'Disconnect';
$_['button_smart_button']				= 'Smart Button Configure';

// Success
$_['success_save']		 				= 'Success: You have modified PayPal!';

// Error
$_['error_permission']	 				= 'Warning: You do not have permission to modify payment PayPal!';
$_['error_timeout'] 	  				= 'Sorry, PayPal is currently busy. Please try again later!';