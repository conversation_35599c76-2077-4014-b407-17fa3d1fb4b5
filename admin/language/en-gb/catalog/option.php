<?php
// Heading
$_['heading_title']      = 'Options';

// Text
$_['text_success']       = 'Success: You have modified options!';
$_['text_list']          = 'Option List';
$_['text_add']           = 'Add Option';
$_['text_edit']          = 'Edit Option';
$_['text_choose']        = 'Choose';
$_['text_select']        = 'Select';
$_['text_radio']         = 'Radio';
$_['text_checkbox']      = 'Checkbox';
$_['text_input']         = 'Input';
$_['text_text']          = 'Text';
$_['text_textarea']      = 'Textarea';
$_['text_file']          = 'File';
$_['text_date']          = 'Date';
$_['text_datetime']      = 'Date &amp; Time';
$_['text_time']          = 'Time';
$_['text_option']        = 'Option';
$_['text_value']         = 'Option Values';

// Column
$_['column_name']        = 'Option Name';
$_['column_sort_order']  = 'Sort Order';
$_['column_action']      = 'Action';

// Entry
$_['entry_name']         = 'Option Name';
$_['entry_type']         = 'Type';
$_['entry_option_value'] = 'Option Value Name';
$_['entry_image']        = 'Image';
$_['entry_sort_order']   = 'Sort Order';

// Error
$_['error_permission']   = 'Warning: You do not have permission to modify options!';
$_['error_name']         = 'Option Name must be between 1 and 128 characters!';
$_['error_type']         = 'Warning: Option Values required!';
$_['error_option_value'] = 'Option Value Name must be between 1 and 128 characters!';
$_['error_product']      = 'Warning: This option cannot be deleted as it is currently assigned to %s products!';