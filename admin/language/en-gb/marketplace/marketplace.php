<?php
// Heading
$_['heading_title']      = 'Extension Marketplace';

// Text
$_['text_success']       = 'Success: You have modified extensions!';
$_['text_list']          = 'Extension List';
$_['text_filter']        = 'Filter';
$_['text_search']        = 'Search for extensions and themes';
$_['text_category']      = 'Categories';
$_['text_all']           = 'All';
$_['text_theme']         = 'Themes';
$_['text_marketplace']   = 'Marketplaces';
$_['text_language']      = 'Languages';
$_['text_payment']       = 'Payment';
$_['text_shipping']      = 'Shipping';
$_['text_module']        = 'Modules';
$_['text_total']         = 'Order Totals';
$_['text_feed']          = 'Feeds';
$_['text_report']        = 'Reports';
$_['text_other']         = 'Other';
$_['text_free']          = 'Free';
$_['text_paid']          = 'Paid';
$_['text_purchased']     = 'Purchased';
$_['text_date_modified'] = 'Date Modified';
$_['text_date_added']    = 'Date Added';
$_['text_rating']        = 'Rating';
$_['text_reviews']       = 'reviews';
$_['text_compatibility'] = 'Compatibility';
$_['text_downloaded']    = 'Downloaded';
$_['text_member_since']  = 'Member since:';
$_['text_price']         = 'Price';
$_['text_partner']       = 'Developed by OpenCart Partner';
$_['text_support']       = '12 Months free support';
$_['text_documentation'] = 'Documentation Included';
$_['text_sales']         = 'Sales';
$_['text_comment']       = 'Comments';
$_['text_download']      = 'Downloading';
$_['text_install']       = 'Installing';
$_['text_comment_add']   = 'Leave your comment';
$_['text_write']         = 'Write your comment here..';
$_['text_purchase']      = 'Please confirm who you are!';
$_['text_pin']           = 'Please enter your 4 digit PIN number. This PIN number is to protect your account.';
$_['text_secure']        = 'Do not give your PIN to any one including developers. If you require an extension seller to install an extension for you then you should email them the required extension.';
$_['text_name']          = 'Download Name';
$_['text_progress']      = 'Progress';
$_['text_available']     = 'Available Installs';
$_['text_action']        = 'Action';
$_['text_see_more']      = 'see more replies...';

// Entry
$_['entry_pin']          = 'PIN';

// Tab
$_['tab_description']    = 'Description';
$_['tab_documentation']  = 'Documentation';
$_['tab_download']       = 'Download';
$_['tab_comment']        = 'Comment';

// Button
$_['button_opencart']    = 'Marketplace API';
$_['button_purchase']    = 'Purchase';
$_['button_view_all']    = 'View all extensions';
$_['button_get_support'] = 'Get Support';
$_['button_comment']     = 'Comment';
$_['button_reply']       = 'Reply';
$_['button_refresh']     = 'Refresh';

// Error
$_['error_permission']   = 'Warning: You do not have permission to modify extensions!';
$_['error_opencart']     = 'Warning: You must enter your OpenCart API information before you can make any purchases!';
$_['error_install']      = 'Extension installation taking place please wait a few seconds before trying to install!';
$_['error_purchase']     = 'Extension could not be purchased!';
$_['error_download']     = 'Extension could not be downloaded!';
