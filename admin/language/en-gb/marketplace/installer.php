<?php
// Heading
$_['heading_title']     = 'Extension Installer';

// Text
$_['text_progress']     = 'Install Progress';
$_['text_upload']       = 'Upload your extensions';
$_['text_history']      = 'Install History';
$_['text_success']      = 'Success: The extension has been installed!';
$_['text_install']      = 'Installing';

// Column
$_['column_filename']   = 'Filename';
$_['column_date_added'] = 'Date Added';
$_['column_action']     = 'Action';

// Entry
$_['entry_upload']      = 'Upload File';
$_['entry_progress']    = 'Progress';

// Help
$_['help_upload']       = 'Requires a modification file with extension \'.ocmod.zip\'.';

// Error
$_['error_permission']  = 'Warning: You do not have permission to modify extensions!';
$_['error_install']     = 'Extension installation taking place please wait a few seconds before trying to install!';
$_['error_upload']      = 'File could not be uploaded!';
$_['error_filetype']    = 'Invalid file type!';
$_['error_file']        = 'File could not be found!';