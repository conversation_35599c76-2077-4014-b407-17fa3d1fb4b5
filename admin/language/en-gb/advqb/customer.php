<?php
// Heading
$_['heading_title']             = 'QuickBooks Customers';

// Button
$_['button_clear_filter']       = 'Clear';

// Text
$_['text_filter']               = 'Filters';
$_['text_success_customer']     = 'Success: %s customer(s) synchronization completed!';
$_['text_list']                 = 'Customer List';
$_['text_add']                  = 'Add Customer';
$_['text_edit']                 = 'Edit Customer';
$_['text_default']              = 'Default';
$_['text_account']              = 'Customer Details';
$_['text_password']             = 'Password';
$_['text_other']                = 'Other';
$_['text_payment']              = 'Payment Details';
$_['text_balance']              = 'Balance';
$_['text_cheque']               = 'Cheque';
$_['text_paypal']               = 'PayPal';
$_['text_bank']                 = 'Bank Transfer';
$_['text_history']              = 'History';
$_['text_history_add']          = 'Add History';
$_['text_transaction']          = 'Transactions';
$_['text_transaction_add']      = 'Add Transaction';
$_['text_reward']               = 'Reward Points';
$_['text_reward_add']           = 'Add Reward Points';
$_['text_ip']                   = 'IP';
$_['text_option']               = 'Options';
$_['text_login']                = 'Login into Store';
$_['text_unlock']               = 'Unlock Account';
$_['text_export']               = 'Export';
$_['text_exporting']            = 'Exporting Customer(s), please wait...';
$_['text_import']               = 'Import';
$_['text_importing']            = 'Importing Customer(s), please wait...';
$_['text_success_import']       = 'Success: %s customer(s) imported successfully!';
$_['text_error_log']            = 'Error Log';
$_['text_modal_header']         = 'QuickBooks Errors';
$_['text_date']                 = 'Date';
$_['text_message']              = 'Error Message';
$_['text_clear']                = 'Clear Log';
$_['text_confirm']              = 'Are you sure?';
$_['text_success_clear']        = 'Error log cleared successfully';

// Column
$_['column_customer_id']        = 'Customer Id';
$_['column_advqb_customer_id']     = 'QuickBooks Customer Id';
$_['column_name']               = 'Customer Name';
$_['column_email']              = 'E-Mail';
$_['column_customer_group']     = 'Customer Group';
$_['column_status']             = 'Status';
$_['column_date_added']         = 'Date Added';
$_['column_comment']            = 'Comment';
$_['column_description']        = 'Description';
$_['column_amount']             = 'Amount';
$_['column_points']             = 'Points';
$_['column_ip']                 = 'IP';
$_['column_total']              = 'Total Accounts';
$_['column_action']             = 'Action';

// Entry
$_['entry_customer_id']         = 'Opencart Customer Id';
$_['entry_advqb_customer_id']      = 'QuickBooks Customer Id';
$_['entry_customer_group']      = 'Customer Group';
$_['entry_firstname']           = 'First Name';
$_['entry_lastname']            = 'Last Name';
$_['entry_email']               = 'E-Mail';
$_['entry_telephone']           = 'Telephone';
$_['entry_newsletter']          = 'Newsletter';
$_['entry_status']              = 'Status';
$_['entry_approved']            = 'Approved';
$_['entry_safe']                = 'Safe';
$_['entry_password']            = 'Password';
$_['entry_confirm']             = 'Confirm';
$_['entry_company']             = 'Company';
$_['entry_address_1']           = 'Address 1';
$_['entry_address_2']           = 'Address 2';
$_['entry_city']                = 'City';
$_['entry_postcode']            = 'Postcode';
$_['entry_country']             = 'Country';
$_['entry_zone']                = 'Region / State';
$_['entry_default']             = 'Default Address';
$_['entry_tracking']            = 'Tracking Code';
$_['entry_website']             = 'Web Site';
$_['entry_commission']          = 'Commission (%)';
$_['entry_tax']                 = 'Tax ID';
$_['entry_payment']             = 'Payment Method';
$_['entry_cheque']              = 'Cheque Payee Name';
$_['entry_paypal']              = 'PayPal Email Account';
$_['entry_bank_name']           = 'Bank Name';
$_['entry_bank_branch_number']  = 'ABA/BSB number (Branch Number)';
$_['entry_bank_swift_code']     = 'SWIFT Code';
$_['entry_bank_account_name']   = 'Account Name';
$_['entry_bank_account_number'] = 'Account Number';
$_['entry_comment']             = 'Comment';
$_['entry_description']         = 'Description';
$_['entry_amount']              = 'Amount';
$_['entry_points']              = 'Points';
$_['entry_name']                = 'Customer Name';
$_['entry_ip']                  = 'IP';
$_['entry_date_added']          = 'Date Added';

// Help
$_['help_safe']                 = 'Set to true to avoid this customer from being caught by the anti-fraud system';
$_['help_tracking']             = 'The tracking code that will be used to track referrals.';
$_['help_points']               = 'Use minus to remove points';

// Error
$_['error_warning']             = 'Warning: Please check the form carefully for errors!';
$_['error_permission']          = 'Warning: You do not have permission to modify customers!';
$_['error_exists']              = 'Warning: E-Mail Address is already registered!';
$_['error_firstname']           = 'First Name must be between 1 and 32 characters!';
$_['error_lastname']            = 'Last Name must be between 1 and 32 characters!';
$_['error_email']               = 'E-Mail Address does not appear to be valid!';
$_['error_telephone']           = 'Telephone must be between 3 and 32 characters!';
$_['error_password']            = 'Password must be between 4 and 20 characters!';
$_['error_confirm']             = 'Password and password confirmation do not match!';
$_['error_address_1']           = 'Address 1 must be between 3 and 128 characters!';
$_['error_city']                = 'City must be between 2 and 128 characters!';
$_['error_postcode']            = 'Postcode must be between 2 and 10 characters for this country!';
$_['error_country']             = 'Please select a country!';
$_['error_zone']                = 'Please select a region / state!';
$_['error_custom_field']        = '%s required!';
$_['error_tracking']            = 'Tracking Code required!';
$_['error_cheque']              = 'Cheque Payee Name required!';
$_['error_paypal']              = 'PayPal Email Address does not appear to be valid!';
$_['error_bank_account_name']   = 'Account Name required!';
$_['error_bank_account_number'] = 'Account Number required!';
