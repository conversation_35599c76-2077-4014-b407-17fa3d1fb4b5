<?php
// Heading
$_['heading_title']        = 'APIs';

// Text
$_['text_success']         = 'Success: You have modified APIs!';
$_['text_list']            = 'API List';
$_['text_add']             = 'Add API';
$_['text_edit']            = 'Edit API';
$_['text_ip']              = 'Below you can create a list of IP\'s allowed to access the API. Your current IP is %s';

// Column
$_['column_username']      = 'API Username';
$_['column_status']        = 'Status';
$_['column_token']         = 'Token';
$_['column_ip']            = 'IP';
$_['column_date_added']    = 'Date Added';
$_['column_date_modified'] = 'Date Modified';

$_['column_action']        = 'Action';

// Entry
$_['entry_username']       = 'API Username';
$_['entry_key']            = 'API Key';
$_['entry_status']         = 'Status';
$_['entry_ip']             = 'IP';

// Error
$_['error_permission']     = 'Warning: You do not have permission to modified APIs!';
$_['error_username']       = 'API USername must be between 3 and 20 characters!';
$_['error_key']            = 'API Key must be between 64 and 256 characters!';
$_['error_ip']             = 'You must have at least one IP added to the allowed list!';
